// eslint-disable-next-line @typescript-eslint/no-unused-vars
import NextAuth, { DefaultSession, DefaultUser } from 'next-auth';

declare module 'next-auth' {
  interface Session extends DefaultSession {
    user: {
      id: string;
      extPuuid?: string;
      extGameName?: string;
      extDisplayName?: string;
      extEmail?: string;
      extCodeVerified?: Date;
      extRegistred?: Date;
      extMarketing?: boolean;
      extRulesAccepted?: boolean;
      extQuestionnaire?: boolean;
      extReleaseForm?: boolean;
      extRiotId?: string;
      extHasTicket?: string;
      extRealmUsername?: string;
      extRealmPassword?: string;
      extKbygRead?: boolean;
    } & DefaultSession['user'];
  }

  interface User extends DefaultUser {
    extGameName?: string;
    extPuuid?: string;
    extEmail?: string;
    extDisplayName?: string;
    extCode?: string;
    extCodeExpires?: Date;
    extCodeVerified?: Date;
    extData?: Date;
    extRegistred?: Date;
    extMarketing?: boolean;
    extRulesAccepted?: boolean;
    extQuestionnaire?: any;
    extReleaseForm?: boolean;
    extRiotId?: string;
    extHasTicket?: string;
    extRealmUsername?: string;
    extRealmPassword?: string;
    extKbygRead?: boolean;
  }
}
