import { getPurchaseInfoProps } from '@/data/competitor-bundle';
import { getDashboardLocales } from '@/data/dashboard';

//
// Site Config
//

export type SiteConfig = {
  localeJSON: Record<string, string>;
  regions: Region[];
  featureFlags: FeatureFlags;
  releaseFormModalHtml: string;
  officialRulesModalHtml: string;
  participationAgreementModalHtml: string;
};

export interface Region {
  publicId: string;
  name: string;
  ageRestriction: number;
  countries: {
    publicId: string;
    name: string;
  }[];
}

interface FeatureFlags {
  isLoginEnabled?: boolean;
  isPurchaseEnabled?: boolean;
  showLanguageDropdown?: boolean;
  isProfileProgressEnabled?: boolean;
  areSpectatorPassesOnSale?: boolean;
  isReleaseFormModalVisible?: boolean;
}

//
// Pages
//

export type HomeConfig = {
  title: string;
  heroTitle: unknown;
  heroTitleTactitiansCrown: unknown;
  heroTitleChallenger: unknown;
  heroTitleMaster: unknown;
  countdownHeroTitleTactitiansCrown: unknown;
  countdownHeroTitleChallenger: unknown;
  countdownHeroTitleMaster: unknown;
  faqItems?: FAQItem[];
  spectatorPassesSaleLink?: string;
  spectatorPassesSaleLinkCn?: string;
};

export type SupportPage = {
  title: string;
  faqItems?: FAQItem[];
};

export type RulesPage = {
  html: string;
};

export type DashboardLocaleData = ReturnType<typeof getDashboardLocales>;

export type EventSchedulePage = {
  streams: Stream[];
};

//
// Shared
//

type FAQItem = {
  title: string;
  content: unknown;
};

export type Stream = {
  id: number;
  label: string;
  url: string;
  platform: StreamPlatform;
};

export enum StreamPlatform {
  YOUTUBE = 'youtube',
  TWITCH = 'twitch',
  TIKTOK = 'tiktok',
  HUYA = 'huya',
  KUAISHOU = 'kuaishou',
  DOUYU = 'douyu',
  NAVER = 'naver',
  SOOP = 'soop',
  DOUYIN = 'douyin',
}

export enum EventTag {
  Freeplay = 'FREEPLAY',
  Tournament = 'TOURNAMENT',
  Panel = 'PANEL',
  免费参与 = '免费参与',
  娱乐赛事 = '娱乐赛事',
  策划面对面 = '策划面对面',
}

export type SideEvent = {
  title: string;
  tag: EventTag;
  location: string;
  info: unknown;
  startTime: Date;
  order?: number;
};

export type PurchaseInfoProps = ReturnType<typeof getPurchaseInfoProps>;
