import SomethingWentWrong from '@/components/shared/SomethingWentWrong';
import { PageProps } from '@/types/router';
import { Box } from '@/ui';

// The "not found" page is implemented here as a catch-all route under the [locale] path.
// This allows us to access the locale from PageProps and use it to fetch the necessary
// translations for the "not found" page from the CMS.

export default function NotFound({ params: { locale } }: PageProps) {
  return (
    <Box
      sx={{
        backgroundColor: 'primary-midnight',
        backgroundImage: 'url(/images/paris-letters.png), url(/images/notFoundBackground.png)',
        backgroundRepeat: 'no-repeat',
        backgroundSize: 'cover, cover',
        backgroundPosition: 'center',
        flexGrow: 1,
      }}
      pt={[4, 6, 20]}
      pb={[20, 20, 30]}
      px={[0, 6, 20]}
    >
      <SomethingWentWrong locale={locale} />
    </Box>
  );
}
