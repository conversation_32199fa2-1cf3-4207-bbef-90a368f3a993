import { getPageConfig, getSiteConfig } from '@/apis/strapi';
import { Container, FAQ } from '@/components';
import { getFAQProps, getWaysToReachOutProps } from '@/data/support';
import { WaysToReachOut } from '@/pages-components/support/WaysToReachOut';
import { PageProps } from '@/types/router';
import type { SupportPage } from '@/types/strapi';
import { Box, FlexLayout, Text } from '@/ui';

export * from '@/config/page-cache';

export default async function Support({ params: { locale } }: PageProps) {
  const [siteConfig, pageConfig] = await Promise.all([
    getSiteConfig(locale),
    getPageConfig<SupportPage>('page-support', locale),
  ]);

  const props = {
    siteConfig,
    pageConfig,
  };

  const waysToReachUs = getWaysToReachOutProps(props);

  return (
    <Box
      sx={{
        backgroundColor: 'midnight900',
        backgroundImage: 'url(/images/supportBackground.png)',
        backgroundRepeat: 'no-repeat',
        backgroundSize: 'cover',
      }}
    >
      <Container>
        <FlexLayout
          px={[6, 10, 16]}
          sx={{
            paddingBottom: '0 !important',
            '@media screen and (width: 430px)': {
              px: '30px',
            },
          }}
          alignItems="center"
          flexDirection="column"
          mt={[16, 20, 30]}
          mb={[10, 12, 20]}
          space={[2, 4, 4]}
        >
          <Text textVariant={['h4', 'h3']} upperCase color="primary-midnight">
            {siteConfig.localeJSON['supportTitle'] ?? 'Support'}
          </Text>
          <Text textVariant={['h8', 'h5']} upperCase color="primary-midnight">
            {waysToReachUs.title}
          </Text>
        </FlexLayout>
        <FlexLayout
          flexDirection="column"
          py={[6, 10, 12]}
          px={[6, 10, 16]}
          mb={[15, 15, 40]}
          mx={[0, 6, 15]}
          sx={{
            minHeight: 800,
            paddingBottom: '0 !important',
            '@media screen and (width: 430px)': {
              px: '30px',
            },
          }}
          space={16}
        >
          <WaysToReachOut items={waysToReachUs.items} />
          <Box
            sx={{
              width: '120px',
              alignSelf: 'center',
              height: '1px',
              background: 'primary-dusk',
              backgroundSize: 'cover',
              backgroundRepeat: 'no-repeat',
            }}
          />
          <FlexLayout sx={{ width: '100%' }}>
            <FAQ withBorder={false} {...getFAQProps(props)} />
          </FlexLayout>
        </FlexLayout>
      </Container>
    </Box>
  );
}
