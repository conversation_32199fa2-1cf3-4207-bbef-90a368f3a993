import { Container } from '@/components';
import { Box, FlexLayout } from '@/ui';
import { LeaderboardDto, toLeaderboardData } from './_api/getLeaderboardDataServer';
import { Leaderboard, PageHeader } from './_components';
import { FindPlayerProvider, LeaderboardDataProvider, LeaderboardFiltersProvider } from './_providers';

export default async function LeaderboardPage() {
  const response = await fetch('https://tft-static-prod.s3.us-east-2.amazonaws.com/leaderboard.json');

  const data = (await response.json()) as LeaderboardDto[];
  const leaderboardData = data.map(toLeaderboardData);

  return (
    <Box
      sx={{
        position: 'relative',
        overflow: 'clip',
        backgroundImage: 'url(/images/bracketsBackground.webp)',
        backgroundPosition: '0 -200px',
        backgroundSize: '100% clamp(1700px, 200vw, 2700px)',
      }}
    >
      <Container>
        <FlexLayout flexDirection={'column'} space={[11, 12, 18]} color="white">
          <Box px={[4, 6, 20]} pt={[4, 8, 18]}>
            <PageHeader />
          </Box>
          <Box px={[0, 0, 20]} pb={[8, 8, 18]}>
            <LeaderboardFiltersProvider>
              <FindPlayerProvider>
                <LeaderboardDataProvider data={leaderboardData}>
                  <Leaderboard />
                </LeaderboardDataProvider>
              </FindPlayerProvider>
            </LeaderboardFiltersProvider>
          </Box>
        </FlexLayout>
      </Container>
    </Box>
  );
}
