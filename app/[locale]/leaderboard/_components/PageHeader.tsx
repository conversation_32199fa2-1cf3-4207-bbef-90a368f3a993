'use client';

import { useSiteConfig } from '@/context/SiteConfig';
import { Box, FlexLayout, Image, Text, useScreenType } from '@/ui';

export const PageHeader = () => {
  const { isMobile } = useScreenType();
  const { localeJSON } = useSiteConfig();

  return (
    <Box sx={{ position: 'relative', width: '100%' }}>
      <FlexLayout flexDirection={'column'} space={4} sx={{ maxWidth: ['100%', 500, 636] }}>
        <Text as={'p'} variant={isMobile ? 'h3' : 'h1'}>
          {localeJSON['leaderboardPageTitle']}
        </Text>
        <Text as={'p'} variant={isMobile ? 'paragraph-xs-medium' : 'paragraph-s-medium'}>
          {localeJSON['leaderboardPageInfo']}
        </Text>
      </FlexLayout>
      <Box
        sx={{
          width: [321, 321, 398],
          height: [321, 321, 398],
          display: ['none', 'initial'],
          position: 'absolute',
          top: [-12, -12, -20],
          right: [-24, -24, 0],
        }}
      >
        <Image src={'/images/leaderboard.webp'} alt={''} width={'100%'} height={'100%'} />
      </Box>
    </Box>
  );
};
