'use client';

import { useSession } from 'next-auth/react';

import { useSiteConfig } from '@/context/SiteConfig';
import { Button, useScreenType } from '@/ui';
import { useFindPlayer } from '../../../_providers';

export const FindMeButton = () => {
  const { isDesktop } = useScreenType();
  const { localeJSON } = useSiteConfig();

  const { data } = useSession();
  const { onFindPlayer } = useFindPlayer();

  if (!data?.user) {
    return null;
  }

  return (
    <Button
      variant="tertiary"
      onClick={() => onFindPlayer(data.user.extRiotId ?? '')}
      label={localeJSON['leaderboardFindMe']}
      size={isDesktop ? 'medium' : 'small'}
    />
  );
};
