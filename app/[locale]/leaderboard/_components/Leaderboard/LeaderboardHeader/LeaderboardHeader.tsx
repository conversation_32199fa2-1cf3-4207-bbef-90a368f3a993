'use client';

import { useSiteConfig } from '@/context/SiteConfig';
import { FlexLayout, Text, useScreenType } from '@/ui';
import { FindMeButton } from './FindMeButton';
import { PlayerSearch } from './PlayerSearch';
import { RegionSelect } from './RegionSelect';

export const LeaderboardHeader = () => {
  const { isDesktop } = useScreenType();
  const { localeJSON } = useSiteConfig();

  return (
    <FlexLayout p={3} justifyContent={'space-between'} space={4} backgroundColor="rgba(8,0,77,0.8)">
      <FlexLayout
        flexDirection={['column', 'row']}
        space={[3, 6]}
        alignItems={['start', 'center']}
        flexGrow={1}
        sx={{ position: 'relative' }}
      >
        <Text
          as="p"
          variant={isDesktop ? 'paragraph-l-medium' : 'paragraph-s-medium'}
          sx={{ whiteSpace: 'nowrap', flexShrink: 0 }}
        >
          {localeJSON['leaderboardSet12Rank']}
        </Text>
        <RegionSelect />
      </FlexLayout>
      <FlexLayout space={6} alignItems={'center'} sx={{ alignSelf: 'end' }}>
        <FindMeButton />
        <PlayerSearch />
      </FlexLayout>
    </FlexLayout>
  );
};
