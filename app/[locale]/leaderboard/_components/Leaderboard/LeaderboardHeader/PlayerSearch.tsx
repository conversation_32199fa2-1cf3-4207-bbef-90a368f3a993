'use client';

import { useSiteConfig } from '@/context/SiteConfig';
import { Icon, Input, useScreenType } from '@/ui';
import { useLeaderboardFilters } from '../../../_providers';

export const PlayerSearch = () => {
  const { isMobile } = useScreenType();
  const { searchedPlayer, onSearchPlayer } = useLeaderboardFilters();
  const { localeJSON } = useSiteConfig();

  //todo how does this work on mobile?
  if (isMobile) {
    return <Icon icon={'search'} />;
  }

  return (
    <Input
      type="text"
      value={searchedPlayer}
      onChange={onSearchPlayer}
      iconLeft={'search'}
      theme="transparent"
      width={['240px', '240px', '300px']}
      placeholder={localeJSON['searchBarPlaceholder']}
    />
  );
};
