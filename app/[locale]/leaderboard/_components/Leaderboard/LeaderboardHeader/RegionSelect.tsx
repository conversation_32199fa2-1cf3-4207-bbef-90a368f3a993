'use client';

import { getGameServers } from '@/components/RegistrationFlow/steps/CollectUserData';
import { Box, Select } from '@/ui';
import { useLeaderboardFilters } from '../../../_providers';

export const REGION_OPTIONS = [{ label: 'Global', value: 'Global' }, ...getGameServers()];

export const RegionSelect = () => {
  const { selectedRegion, onRegionSelect } = useLeaderboardFilters();

  return (
    <Box sx={{ width: ['180px', '150px', '240px'] }}>
      <Select
        value={selectedRegion}
        onChange={(region) => onRegionSelect(region as string)}
        name="regionFilter"
        options={REGION_OPTIONS}
        type="transparent"
        hasDropdownIndicator
        isSearchable={false}
      />
    </Box>
  );
};
