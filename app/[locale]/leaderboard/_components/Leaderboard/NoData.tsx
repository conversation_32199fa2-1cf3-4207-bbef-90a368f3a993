'use client';

import { useSiteConfig } from '@/context/SiteConfig';
import { FlexLayout, Text } from '@/ui';

export const NoData = () => {
  const { localeJSON } = useSiteConfig();

  return (
    <FlexLayout justifyContent={'center'} backgroundColor="rgba(8,0,77,0.8)" sx={{ width: '100%', py: 18 }}>
      <Text as={'p'} variant="paragraph-l-medium">
        {localeJSON['leaderboardPagePlayerNotFound']}
      </Text>
    </FlexLayout>
  );
};
