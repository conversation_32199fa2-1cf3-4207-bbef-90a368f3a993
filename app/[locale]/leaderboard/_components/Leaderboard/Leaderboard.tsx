'use client';

import { Box, FlexLayout } from '@/ui';
import { useLeaderboardData } from '../../_providers';
import { ROWS_PER_PAGE } from '../../_providers/LeaderboardDataProvider/hooks';
import { LeaderboardHeader } from './LeaderboardHeader';
import { LeaderboardTable } from './LeaderboardTable';
import { ROW_HEIGHT, TABLE_HEADER_HEIGHT } from './LeaderboardTable/const';
import { NoData } from './NoData';
import { Paging } from './Paging';

export const Leaderboard = () => {
  const { data } = useLeaderboardData();
  const isDataEmpty = data.length === 0;

  return (
    <FlexLayout flexDirection={'column'} space={8}>
      <FlexLayout
        px={[0, 4, 6]}
        py={[4, 4, 6]}
        flexDirection={'column'}
        space={8}
        backgroundColor="rgba(8,0,77,0.4)"
        sx={{ zIndex: 1, backdropFilter: 'blur(4px)' }}
      >
        <LeaderboardHeader />
        <Box sx={{ width: '100%', overflow: 'auto', minHeight: ROWS_PER_PAGE * ROW_HEIGHT + TABLE_HEADER_HEIGHT }}>
          {isDataEmpty ? (
            <NoData />
          ) : (
            <Box sx={{ width: '100%', overflow: 'auto', height: '100%' }}>
              <LeaderboardTable />
            </Box>
          )}
        </Box>
      </FlexLayout>
      {!isDataEmpty && (
        <div style={{ alignSelf: 'center' }}>
          <Paging />
        </div>
      )}
    </FlexLayout>
  );
};
