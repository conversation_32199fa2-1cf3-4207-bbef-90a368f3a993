const MAX_DISPLAY_PAGE_NUMBERS = 5;

export function getDisplayedPages(pages: number, currentPage: number) {
  const defaultPages = Array.from({ length: pages }, (_, i) => ({
    page: i + 1,
    text: `${i + 1}`,
  }));

  if (pages <= MAX_DISPLAY_PAGE_NUMBERS) {
    return defaultPages;
  }

  const firstPage = 1;
  const lastPage = pages;

  if (currentPage <= firstPage + 2) {
    const slicedPages = defaultPages.slice(0, 3);
    return [
      ...slicedPages,
      { page: (slicedPages.at(-1)?.page as number) + 1, text: '...' },
      { page: lastPage, text: lastPage.toString() },
    ];
  }

  if (currentPage >= lastPage - 2) {
    const slicedPages = defaultPages.slice(-3);
    return [
      { page: firstPage, text: firstPage.toString() },
      { page: slicedPages[0].page - 1, text: '...' },
      ...slicedPages,
    ];
  }

  return [
    { page: firstPage, text: firstPage.toString() },
    { page: currentPage - 1, text: '...' },
    { page: currentPage, text: currentPage.toString() },
    { page: currentPage + 1, text: '...' },
    { page: lastPage, text: lastPage.toString() },
  ];
}
