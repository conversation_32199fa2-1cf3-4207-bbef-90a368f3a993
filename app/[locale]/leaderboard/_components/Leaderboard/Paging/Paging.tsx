'use client';

import React from 'react';

import { Box, FlexLayout, Icon, Text } from '@/ui';
import { useLeaderboardData } from '../../../_providers';
import { getDisplayedPages } from './utils';

export const Paging = () => {
  const { pages, currentPage, onPageSelect } = useLeaderboardData();
  const displayedPages = getDisplayedPages(pages, currentPage);

  const firstPage = 1;
  const lastPage = pages;

  return (
    <FlexLayout alignItems={'center'} space={3}>
      <Icon
        icon="chevronLeft"
        color="white"
        opacity={0.7}
        onClick={() => onPageSelect(currentPage - 1)}
        sx={{ ':hover': { opacity: 1, cursor: 'pointer' }, visibility: currentPage === firstPage && 'hidden' }}
      />
      <FlexLayout space={1}>
        {displayedPages.map((p) => (
          <PageBox
            isHighlighted={p.page === currentPage}
            key={p.page}
            text={p.text}
            onClick={() => onPageSelect(p.page)}
          />
        ))}
      </FlexLayout>
      <Icon
        icon="chevronRight"
        color="white"
        sx={{ ':hover': { opacity: 1, cursor: 'pointer' }, visibility: currentPage === lastPage && 'hidden' }}
        opacity={0.7}
        onClick={() => onPageSelect(currentPage + 1)}
      />
    </FlexLayout>
  );
};

interface PageBoxProps {
  text: number | string;
  isHighlighted?: boolean;
  onClick?: () => void;
}

const PageBox: React.FC<PageBoxProps> = ({ text, isHighlighted, onClick }) => (
  <Box sx={{ width: 42, textAlign: 'center', cursor: onClick ? 'pointer' : 'default' }} onClick={onClick}>
    <Text variant="paragraph-l-regular" color={isHighlighted ? 'purpleLavander500' : 'white'}>
      {text}
    </Text>
  </Box>
);
