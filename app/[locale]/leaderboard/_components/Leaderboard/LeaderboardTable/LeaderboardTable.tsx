'use client';
import React from 'react';

import { useSiteConfig } from '@/context/SiteConfig';
import { Box, Text, useScreenType } from '@/ui';
import { useLeaderboardData } from '../../../_providers';
import { Row } from './Row';

const HEADER_LOCALE_KEYS = [
  'leaderboardRank',
  'leaderboardDisplayName',
  'leaderboardRegion',
  'leaderboardWins',
  'leaderboardPoints',
];

export const LeaderboardTable = () => {
  const screen = useScreenType();
  const { localeJSON } = useSiteConfig();

  const { data } = useLeaderboardData();

  return (
    <table style={{ width: '100%', borderSpacing: '0 8px' }}>
      <thead>
        <tr>
          {HEADER_LOCALE_KEYS.map((h) => (
            <Box
              as={'th'}
              key={h}
              sx={{
                textAlign: headerAlignmentMap[h],
                px: 3,
                whiteSpace: 'nowrap',
                width: getHeaderWidthMap(screen)[h],
              }}
            >
              <Text variant={screen.isDesktop ? 'h4' : 'h6'}>{localeJSON[h]}</Text>
            </Box>
          ))}
        </tr>
      </thead>
      <Box as="tbody" sx={{ '&:before': { content: '""', display: 'block', height: '4px' } }}>
        {data.map((d, i) => (
          <Row key={i} {...d} />
        ))}
      </Box>
    </table>
  );
};

function getHeaderWidthMap({ isDesktop, isTablet }: { isDesktop: boolean; isTablet: boolean }): Record<string, string> {
  if (isDesktop) {
    return {
      leaderboardRank: 'auto',
      leaderboardDisplayName: 'auto',
      leaderboardRegion: '240px',
      leaderboardWins: '240px',
      leaderboardPoints: '160px',
    };
  }

  if (isTablet) {
    return {
      leaderboardRank: 'auto',
      leaderboardDisplayName: 'auto',
      leaderboardRegion: '160px',
      leaderboardWins: '160px',
      leaderboardPoints: '100px',
    };
  }

  return {
    leaderboardRank: 'auto',
    leaderboardDisplayName: 'auto',
    leaderboardRegion: '100px',
    leaderboardWins: '100px',
    leaderboardPoints: '100px',
  };
}

const headerAlignmentMap: Record<string, 'start' | 'center' | 'end'> = {
  leaderboardRank: 'start',
  leaderboardDisplayName: 'start',
  leaderboardRegion: 'center',
  leaderboardWins: 'center',
  leaderboardPoints: 'center',
};
