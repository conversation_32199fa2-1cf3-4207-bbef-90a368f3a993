import { keyframes } from '@emotion/react';

import { FIND_PLAYER_ANIMATION_DURATION, useFindPlayer } from '../../../_providers/FindPlayerProvider';

const blush = keyframes({ from: { backgroundColor: 'rgba(8,0,77,0.4)' }, to: { backgroundColor: '#0E0080' } });

export function useFindPlayerAnimation(rowPlayerId: string) {
  const { playerId } = useFindPlayer();

  if (rowPlayerId === playerId) {
    return {
      animation: `${blush}`,
      animationDuration: `${FIND_PLAYER_ANIMATION_DURATION / 2}ms`,
      animationIterationCount: 2,
      animationDirection: 'alternate',
      animationFillMode: 'backwards',
    };
  }

  return {};
}
