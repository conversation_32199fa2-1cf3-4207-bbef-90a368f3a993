'use client';

import { Tier } from '@prisma/client';
import { capitalize } from 'lodash';
import React from 'react';

import { useSiteConfig } from '@/context/SiteConfig';
import { Box, FlexLayout, Text, useScreenType } from '@/ui';
import { LeaderboardData } from '../../../_providers/LeaderboardDataProvider/types';
import { useFindPlayerAnimation } from './hooks';

export const Row: React.FC<LeaderboardData> = ({ id, riotId, rank, name, tier, region, wins, points }) => {
  const { isDesktop } = useScreenType();
  const findPlayerAnimation = useFindPlayerAnimation(riotId);

  return (
    <Box
      id={id}
      as={'tr'}
      sx={{
        backgroundColor: 'rgba(8,0,77,0.4)',
        transition: 'background-color, 250ms',
        ':hover': { backgroundColor: 'midnight700' },
        ...findPlayerAnimation,
      }}
    >
      <Box as="td" pl={4}>
        <Text variant={isDesktop ? 'paragraph-m-medium' : 'paragraph-s-medium'}>{rank}</Text>
      </Box>
      <Box as="td" py={2} pl={3}>
        <FlexLayout flexDirection={['column', 'row']} alignItems={['start', 'center']} space={[2, 4]}>
          <Text variant={isDesktop ? 'paragraph-m-medium' : 'paragraph-s-medium'}>{name}</Text>
          {tier && <TierBox tier={tier} />}
        </FlexLayout>
      </Box>
      <td style={{ textAlign: 'center' }}>
        <Text variant={isDesktop ? 'paragraph-m-medium' : 'paragraph-s-medium'}>{region}</Text>
      </td>
      <td style={{ textAlign: 'center' }}>
        <Text variant={isDesktop ? 'paragraph-m-medium' : 'paragraph-s-medium'}>{wins}</Text>
      </td>
      <Box as="td" style={{ textAlign: 'center' }} pr={3}>
        <Text variant={isDesktop ? 'paragraph-m-medium' : 'paragraph-s-medium'}>{points}</Text>
      </Box>
    </Box>
  );
};

const TierBox: React.FC<{ tier: Tier }> = ({ tier }) => {
  const { localeJSON } = useSiteConfig();

  const localeKey = tierToLocaleKey(tier);
  return (
    <Box
      sx={{ flexShrink: 0 }}
      backgroundColor={
        tier === Tier.GRANDMASTER
          ? 'paleYellow600'
          : tier === Tier.MASTER
          ? 'dreamBlue'
          : tier === Tier.CHALLENGER
          ? 'midnight200'
          : 'gray500'
      }
      px={2}
      py={1}
    >
      <Text variant="paragraph-xs-bold" color={tier === Tier.GRANDMASTER ? 'midnight800' : 'white'}>
        {localeKey === 'rankOther' ? capitalize(tier) : localeJSON[localeKey]}
      </Text>
    </Box>
  );
};

function tierToLocaleKey(tier: Tier) {
  switch (tier) {
    case Tier.CHALLENGER:
      return 'rankChallenger';
    case Tier.MASTER:
      return 'rankMaster';
    case Tier.GRANDMASTER:
      return 'rankGrandmaster';
    case 'GOLD' as Tier:
      return 'rankGold';
    case 'PLATINUM' as Tier:
      return 'rankPlatinum';
    case 'DIAMOND' as Tier:
      return 'rankDiamond';
    default:
      return 'rankOther';
  }
}
