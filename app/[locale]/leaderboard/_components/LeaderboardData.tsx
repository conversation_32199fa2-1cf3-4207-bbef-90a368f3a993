'use client';

import { QueryClient, QueryClientProvider } from '@tanstack/react-query';

// import { LeaderboardDataProvider } from '../_providers';
// import { Leaderboard } from './Leaderboard/Leaderboard';

const queryClient = new QueryClient();

export default function LeaderboardData() {
  return (
    <QueryClientProvider client={queryClient}>
      {/* <LeaderboardDataProvider>
        <Leaderboard />
      </LeaderboardDataProvider> */}
    </QueryClientProvider>
  );
}
