'use client';

import { createContext, useContext, useState } from 'react';

import { GLOBAL_REGION_VALUE } from './const';

interface LeaderboardFiltersData {
  selectedRegion: string;
  onRegionSelect: (region: string) => void;
  searchedPlayer: string;
  onSearchPlayer: (player: string) => void;
}

const LeaderboardFiltersContext = createContext<LeaderboardFiltersData | undefined>(undefined);

export const LeaderboardFiltersProvider = ({ children }: React.PropsWithChildren) => {
  const [selectedRegion, setSelectedRegion] = useState(GLOBAL_REGION_VALUE);
  const [searchedPlayer, setSearchedPlayer] = useState('');

  return (
    <LeaderboardFiltersContext.Provider
      value={{ selectedRegion, onRegionSelect: setSelectedRegion, searchedPlayer, onSearchPlayer: setSearchedPlayer }}
    >
      {children}
    </LeaderboardFiltersContext.Provider>
  );
};

export function useLeaderboardFilters() {
  const state = useContext(LeaderboardFiltersContext);

  if (!state) {
    throw new Error('useLeaderboardFilters must be used within a LeaderboardFiltersProvider!');
  }

  return state;
}
