'use client';

import { sortBy } from 'lodash';
import { createContext, useContext } from 'react';

import { useLeaderboardFilters } from '../LeaderboardFiltersProvider';
import { GLOBAL_REGION_VALUE } from '../LeaderboardFiltersProvider/const';
import { ROWS_PER_PAGE, usePaging } from './hooks';
import { LeaderboardData } from './types';

interface LeaderboardContextData {
  data: LeaderboardData[];
  pages: number;
  currentPage: number;
  onPageSelect: (page: number) => void;
}

const LeaderboardDataContext = createContext<LeaderboardContextData | undefined>(undefined);

export const LeaderboardDataProvider = ({ data, children }: React.PropsWithChildren<{ data: LeaderboardData[] }>) => {
  const { selectedRegion, searchedPlayer } = useLeaderboardFilters();

  let filteredData = data;
  if (selectedRegion !== GLOBAL_REGION_VALUE) {
    filteredData = filteredData.filter((d) => d.region === selectedRegion);
  }

  if (searchedPlayer) {
    filteredData = filteredData.filter((d) => d.name.toLowerCase().includes(searchedPlayer));
  }

  const sortedData = sortBy(filteredData, 'rank');

  const { pages, currentPage, setCurrentPage } = usePaging(sortedData);
  const pagedData = sortedData.slice((currentPage - 1) * ROWS_PER_PAGE, currentPage * ROWS_PER_PAGE);

  return (
    <LeaderboardDataContext.Provider
      value={{
        data: pagedData,
        pages,
        currentPage,
        onPageSelect: setCurrentPage,
      }}
    >
      {children}
    </LeaderboardDataContext.Provider>
  );
};

export function useLeaderboardData() {
  const state = useContext(LeaderboardDataContext);

  if (!state) {
    throw new Error('useLeaderboardData must be used within a LeaderboardDataProvider!');
  }

  return state;
}
