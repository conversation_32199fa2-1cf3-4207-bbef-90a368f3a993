import { useEffect, useState } from 'react';

import { useSiteConfig } from '@/context/SiteConfig';
import { showSuccessToast } from '@/ui';
import { useFindPlayer } from '../FindPlayerProvider';
import { LeaderboardData } from './types';

export function usePaging(data: LeaderboardData[]) {
  const [pages, setPages] = useState(() => calculatePages(data.length));
  const [currentPage, setCurrentPage] = useState(1);

  useEffect(() => {
    setPages(() => calculatePages(data.length));
    setCurrentPage(1);
  }, [data.length]);

  usePageToFoundPlayer(data, setCurrentPage);

  return { pages, currentPage, setCurrentPage };
}

export const ROWS_PER_PAGE = 10;

const calculatePages = (dataCount: number) => Math.ceil(dataCount / ROWS_PER_PAGE);

function usePageToFoundPlayer(data: LeaderboardData[], setPageFn: (page: number) => void) {
  const { playerId } = useFindPlayer();
  const { localeJSON } = useSiteConfig();

  useEffect(() => {
    if (!playerId) {
      return;
    }

    const playerDataIndex = data.findIndex((d) => d.riotId === playerId);
    const playerNotInData = playerDataIndex === -1;
    if (playerNotInData) {
      showSuccessToast(`${localeJSON['leaderboardPagePlayerNotFound']}!`);
      return;
    }

    const pageWithPlayer = playerDataIndex === 0 ? 1 : Math.ceil(playerDataIndex / ROWS_PER_PAGE);
    setPageFn(pageWithPlayer);
  }, [playerId, data]);
}
