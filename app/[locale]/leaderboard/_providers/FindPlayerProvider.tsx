'use client';

import { createContext, useContext, useState } from 'react';

interface FindPlayerValues {
  playerId: string;
  onFindPlayer: (id: string) => void;
}

const FindPlayerContext = createContext<FindPlayerValues | undefined>(undefined);

export const FIND_PLAYER_ANIMATION_DURATION = 2000;

export const FindPlayerProvider = ({ children }: React.PropsWithChildren) => {
  const [playerId, setPlayerId] = useState('');

  function setAndResetPlayerId(id: string) {
    setPlayerId(id);
    setTimeout(() => setPlayerId(''), FIND_PLAYER_ANIMATION_DURATION + 500);
  }

  return (
    <FindPlayerContext.Provider value={{ playerId, onFindPlayer: setAndResetPlayerId }}>
      {children}
    </FindPlayerContext.Provider>
  );
};

export function useFindPlayer() {
  const state = useContext(FindPlayerContext);

  if (!state) {
    throw new Error('useFindPlayer must be used within a FindPlayerProvider!');
  }

  return state;
}
