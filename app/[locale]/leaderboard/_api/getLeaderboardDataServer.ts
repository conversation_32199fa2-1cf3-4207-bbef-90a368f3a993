import { Tier } from '@prisma/client';

import { LeaderboardData } from '../_providers/LeaderboardDataProvider';

import 'server-only';

if (!process.env.FORGE_API_URL) {
  throw new Error('Missing env var FORGE_API_URL!');
}

const leaderboardUrl = `${process.env.FORGE_API_URL}/leaderboard`;

export interface LeaderboardDto {
  id: number;
  riotId: string;
  displayName: string;
  rank: number;
  tier: Tier | null;
  gameServer: string;
  wins: number;
  totalPoints: number;
  numberOfWins: number;
  numberOfTop4: number;
}

export async function getLeaderboardDataServer() {
  try {
    const response = await fetch(leaderboardUrl, { next: { revalidate: 1200 } });
    if (!response.ok) {
      throw new Error('Invalid response');
    }

    const data = (await response.json()) as LeaderboardDto[];
    const leaderboardData = data.map(toLeaderboardData);

    return leaderboardData;
  } catch (error) {
    throw new Error('Error fetching leaderboard data.');
  }
}

export function toLeaderboardData(l: LeaderboardDto) {
  return {
    id: l.id.toString(),
    riotId: l.riotId,
    name: l.displayName,
    rank: l.rank,
    points: l.totalPoints,
    wins: l.wins,
    region: l.gameServer,
    tier: l.tier,
  } as LeaderboardData;
}
