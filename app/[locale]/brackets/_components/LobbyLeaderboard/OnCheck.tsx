'use client';

import { useSiteConfig } from '@/context/SiteConfig';
import { FlexLayout, Icon, Text } from '@/ui';

export const OnCheck = () => {
  const { localeJSON } = useSiteConfig();

  return (
    <FlexLayout space={3} alignItems={'center'}>
      <Text variant="h5" upperCase>
        {localeJSON['onCheck']}
      </Text>
      <Icon icon="polygon" color="successGreen" size="xs" sx={{ mt: '2px' }} />
    </FlexLayout>
  );
};
