import { Box, FlexLayout, Text } from '@/ui';
import { frozenStyles } from './Row/const';

interface Props {
  title: string;
  subtitle?: string;
  verticalAlign: 'center' | 'start';
  isFrozenFirstColumn?: boolean;
}

export const Header: React.FC<Props> = ({ title, subtitle, verticalAlign, isFrozenFirstColumn }) => {
  return (
    <Box
      as="th"
      sx={{
        alignContent: 'center',
        ':first-of-type': {
          pl: 3,
          ...(isFrozenFirstColumn && frozenStyles),
          width: 380,
        },
      }}
    >
      <FlexLayout alignItems={verticalAlign} flexDirection={'column'} space={1} sx={{ minWidth: 120 }} color="white">
        <Text as="p" variant="h5" upperCase>
          {title}
        </Text>
        {subtitle && (
          <Text as="p" variant="h10" upperCase>
            {subtitle}
          </Text>
        )}
      </FlexLayout>
    </Box>
  );
};
