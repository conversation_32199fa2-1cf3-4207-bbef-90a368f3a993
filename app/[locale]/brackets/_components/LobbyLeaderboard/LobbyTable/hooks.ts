import debounce from 'lodash/debounce';
import { useEffect, useRef, useState } from 'react';

import { useSiteConfig } from '@/context/SiteConfig';
import { Header, LobbyPlayerData } from './types';

export function useIsOverflowing() {
  const [isOverflowing, setIsOverflowing] = useState(false);
  const ref = useRef<HTMLElement>();

  useEffect(() => {
    const element = ref.current;

    if (!document || !element) {
      return;
    }

    const updateIsOverflowing = () => {
      const isElementOverflowing = element.scrollWidth > element.clientWidth;
      setIsOverflowing(isElementOverflowing);
    };

    const observer = new ResizeObserver(debounce(updateIsOverflowing, 10));
    observer.observe(document.body);

    return () => observer.disconnect();
  }, []);

  return { ref, isOverflowing };
}

export function useHeaders(datum: LobbyPlayerData): Header[] {
  const { localeJSON } = useSiteConfig();
  const baseHeaders: Header[] = [
    { title: localeJSON['player'] },
    { title: localeJSON['total'], subtitle: localeJSON['points'] },
  ];

  const dataKeys = Object.keys(datum);
  const gamePointsKeys = dataKeys.filter((k) => k.startsWith('game'));

  const gamePointsHeaders: Header[] = gamePointsKeys.map((k) => ({
    title: `${localeJSON['game']} ${k.at(-1)?.padStart(2, '0')}`,
    subtitle: localeJSON['points'],
  }));

  return [...baseHeaders, ...gamePointsHeaders];
}
