'use client';

import { sortBy } from 'lodash';
import React, { useMemo } from 'react';

import { Box } from '@/ui';
import { Header } from './Header';
import { useHeaders, useIsOverflowing } from './hooks';
import { Row, RowSeparator } from './Row';
import { LobbyPlayerData } from './types';

const TOP_ADVANCE_COUNT = 4;

interface Props {
  data: LobbyPlayerData[];
  checkedPlayerIds: string[];
  isAdvancementTable?: boolean;
}

export const LobbyTable = ({ data, checkedPlayerIds, isAdvancementTable }: Props) => {
  const { ref, isOverflowing } = useIsOverflowing();

  const sortedData = useMemo(() => sortBy(data, 'placement'), [data]);
  const headers = useHeaders(sortedData[0]);

  return (
    <Box
      ref={ref}
      sx={{
        overflowX: 'auto',
        '::-webkit-scrollbar': { height: '4px' },
        '::-webkit-scrollbar-track': { background: 'white20' },
        '::-webkit-scrollbar-thumb': { background: 'purpleLavander500' },
      }}
    >
      <Box as={'table'} sx={{ width: '100%', borderSpacing: ['0 2px', '0 2px', '0 4px'] }}>
        <thead>
          <tr>
            {headers.map((h, i) => (
              <Header
                {...h}
                key={h.title}
                isFrozenFirstColumn={isOverflowing}
                verticalAlign={i === 0 ? 'start' : 'center'}
              />
            ))}
          </tr>
        </thead>
        <Box as="tbody" sx={{ '&:before': { content: '""', display: 'block', height: '12px' } }}>
          {sortedData.map((d, i) => (
            <React.Fragment key={d.id}>
              <Row {...d} isFrozenFirstColumn={isOverflowing} isChecked={checkedPlayerIds.includes(d.id)} />
              {isAdvancementTable && i === TOP_ADVANCE_COUNT - 1 && <RowSeparator columnCount={headers.length} />}
            </React.Fragment>
          ))}
        </Box>
      </Box>
    </Box>
  );
};
