'use client';

import React from 'react';

import { useSiteConfig } from '@/context/SiteConfig';
import { FlexLayout, Icon, Text, useScreenType } from '@/ui';

interface Props {
  name: string;
  isCheckedIndicatorVisible: boolean;
  isCurrentUser?: boolean;
}

export const NameCell = ({ name, isCheckedIndicatorVisible, isCurrentUser }: Props) => {
  const { localeJSON } = useSiteConfig();
  const { isDesktop } = useScreenType();

  return (
    <FlexLayout space={3} alignItems={'center'} px={3} py={2} sx={{ width: 'max-content' }}>
      <Text variant="paragraph-m-medium" sx={{ flexShrink: 0 }}>
        {name}
      </Text>
      {isCheckedIndicatorVisible && <Icon color="successGreen" icon="polygon" size="xs" />}
      {isCurrentUser && (
        <FlexLayout
          flexShrink={0}
          alignItems={'center'}
          space={1}
          py={1}
          px={'6px'}
          backgroundColor="rgba(210,255,164,0.2)"
          color="paleYellow600"
        >
          <Icon color="inherit" icon="polygon" size="xs" />
          <Text color="inherit" variant={isDesktop ? 'paragraph-xs-bold' : 'paragraph-s-medium'}>
            {localeJSON['you']}
          </Text>
        </FlexLayout>
      )}
    </FlexLayout>
  );
};

export const PointsCell = ({ points }: { points: number }) => {
  const { isDesktop } = useScreenType();

  return (
    <FlexLayout justifyContent={'center'}>
      <Text variant={isDesktop ? 'paragraph-m-medium' : 'paragraph-s-medium'}>{points}</Text>
    </FlexLayout>
  );
};
