'use client';

import { useSiteConfig } from '@/context/SiteConfig';
import { Box, Text, useScreenType } from '@/ui';

export const RowSeparator = ({ columnCount }: { columnCount: number }) => {
  const { localeJSON } = useSiteConfig();
  const { isDesktop } = useScreenType();

  return (
    <tr>
      <td colSpan={columnCount}>
        <Box backgroundColor="midnight700">
          <Text
            pt={1}
            pb={'6px'}
            px={3}
            as={'p'}
            isCentered={isDesktop}
            variant="paragraph-xs-bold"
            color="midnight50"
            sx={!isDesktop ? { position: 'sticky', left: 0, width: 'fit-content' } : undefined}
          >
            {localeJSON['topFourAdvance']}
          </Text>
        </Box>
        <Box sx={{ height: 16 }} />
      </td>
    </tr>
  );
};
