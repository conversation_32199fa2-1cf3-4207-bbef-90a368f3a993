import React from 'react';

import { Box } from '@/ui';
import { LobbyPlayerData } from '../types';
import { frozenStyles } from './const';
import { NameCell, PointsCell } from './NameCell';

type Props = LobbyPlayerData & { isChecked: boolean; isFrozenFirstColumn: boolean };

export const Row: React.FC<Props> = ({ id, name, total, isChecked, isFrozenFirstColumn, ...rest }) => {
  const gamePointsData = Object.entries(rest).filter(([key]) => key.startsWith('game'));

  return (
    <Box
      as={'tr'}
      sx={{
        backgroundColor: 'rgba(8,0,77,0.8)',
        transition: 'background-color, 250ms',
        ':hover': { backgroundColor: 'midnight700' },
      }}
    >
      <Box as="td" sx={isFrozenFirstColumn ? frozenStyles : undefined}>
        <NameCell name={name} isCurrentUser={id === 'player#1'} isCheckedIndicatorVisible={isChecked} />
      </Box>
      <td>
        <PointsCell points={total} />
      </td>
      {gamePointsData.map(([key, points]) => (
        <td key={key}>
          <PointsCell points={points} />
        </td>
      ))}
    </Box>
  );
};
