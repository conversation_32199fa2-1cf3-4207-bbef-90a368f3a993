'use client';

import { useSiteConfig } from '@/context/SiteConfig';
import { Box, Text, useScreenType } from '@/ui';

export const Title = ({ title, stationNumber }: { title: string; stationNumber: number }) => {
  const { isDesktop } = useScreenType();
  const { localeJSON } = useSiteConfig();

  return (
    <Box pb={6} sx={{ borderBottom: '1px solid', borderBottomColor: 'white20' }}>
      <Text
        as={'h2'}
        variant={isDesktop ? 'paragraph-xl-bold' : 'paragraph-l-bold'}
        color="white"
        upperCase
        sx={{ textAlign: 'center' }}
      >
        {title} - {localeJSON['dashboardMatchStation']} {stationNumber}
      </Text>
    </Box>
  );
};
