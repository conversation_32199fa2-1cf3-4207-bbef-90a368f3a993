import { Box, FlexLayout } from '@/ui';
import { LobbyTable } from './LobbyTable';
import { LobbyPlayerData } from './LobbyTable/types';
import { OnCheck } from './OnCheck';
import { Title } from './Title';

interface Props {
  title: string;
  stationNumber: number;
  isFinalsLeaderboard: boolean;
  data: LobbyPlayerData[];
}

export const LobbyLeaderboard = ({ title, data, isFinalsLeaderboard, stationNumber }: Props) => {
  const checkedPlayerIds = getCheckedPlayers(data, isFinalsLeaderboard);

  return (
    <Box px={[0, 0, 6]} pt={[4, 4, 6]} pb={6} backgroundColor="rgba(8,0,77,0.4)" sx={{ backdropFilter: 'blur(10px)' }}>
      <FlexLayout flexDirection={'column'} space={[6, 6, 8]}>
        {!isFinalsLeaderboard && <Title title={title} stationNumber={stationNumber} />}
        <LobbyTable data={data} isAdvancementTable={!isFinalsLeaderboard} checkedPlayerIds={checkedPlayerIds} />
        {checkedPlayerIds.length !== 0 && (
          <Box sx={{ alignSelf: 'end', px: [6, 6, 0] }}>
            <OnCheck />
          </Box>
        )}
      </FlexLayout>
    </Box>
  );
};

const CHECK_THRESHOLD = 20;
function getCheckedPlayers(data: LobbyPlayerData[], isFinals: boolean) {
  if (!isFinals) {
    return [];
  }

  const checkedPlayers = data.filter((d) => d.total >= CHECK_THRESHOLD);
  const ids = checkedPlayers.map((p) => p.id);

  return ids;
}
