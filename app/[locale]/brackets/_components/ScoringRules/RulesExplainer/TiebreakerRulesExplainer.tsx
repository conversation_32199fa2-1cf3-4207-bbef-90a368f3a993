'use client';

import sanitize from 'sanitize-html';

import { useSiteConfig } from '@/context/SiteConfig';
import { RulesExplainer } from './RulesExplainer';

export const TiebreakerRulesExplainer = () => {
  const { rulesTitle, rulesHtml } = useLocalizedRulesHtml();

  return <RulesExplainer explainerTitle={rulesTitle} text={rulesTitle} explainerContentHtml={rulesHtml} />;
};

function useLocalizedRulesHtml() {
  const { localeJSON } = useSiteConfig();

  const rulesTitle = localeJSON['tiebreakerRules'];

  const rulesParagraph = localeJSON['tiebreakerRulesParagraph'];
  const rulesRows = [
    localeJSON['tiebreakerRulesFirstRow'],
    localeJSON['tiebreakerRulesSecondRow'],
    localeJSON['tiebreakerRulesThirdRow'],
    localeJSON['tiebreakerRulesFourthRow'],
    localeJSON['tiebreakerRulesFifthRow'],
  ];

  const rulesHtml = `<div>
      <p>
        ${rulesParagraph}
      </p>
      <ul>
      ${rulesRows.map((r) => `<li>${r}</li>`).join('')}
      </ul>
    </div>`;

  return { rulesTitle, rulesHtml: sanitize(rulesHtml, { allowedTags: ['div', 'p', 'ul', 'li'] }) };
}
