import React from 'react';

import { FlexLayout, Icon, Text } from '@/ui';

interface Props {
  placement: string;
  points: string;
}

export const PlacePoints = ({ placement, points }: Props) => {
  return (
    <FlexLayout alignItems={'center'} space={1} color="white" flexShrink={0}>
      <Text variant="paragraph-s-bold" color="midnight200" as="p" sx={{ width: ['80px', null] }}>
        {placement}
      </Text>
      <Icon icon={'arrowRight'} size="s" />
      <Text variant="paragraph-s-regular" as="p">
        {points}
      </Text>
    </FlexLayout>
  );
};
