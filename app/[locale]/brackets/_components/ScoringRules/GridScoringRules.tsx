'use client';

import React from 'react';

import { useSiteConfig } from '@/context/SiteConfig';
import { Box, FlexLayout } from '@/ui';
import { PlacePoints } from './PlacePoints';
import { TiebreakerRulesExplainer } from './RulesExplainer';

export const GridScoringRules = () => {
  const { localeJSON } = useSiteConfig();

  const first4Places = [
    { placement: localeJSON['firstPlace'], points: `8 ${localeJSON['points']}` },
    { placement: localeJSON['secondPlace'], points: `7 ${localeJSON['points']}` },
    { placement: localeJSON['thirdPlace'], points: `6 ${localeJSON['points']}` },
    { placement: localeJSON['fourthPlace'], points: `5 ${localeJSON['points']}` },
  ];

  const second4Places = [
    { placement: localeJSON['fifthPlace'], points: `4 ${localeJSON['points']}` },
    { placement: localeJSON['sixthPlace'], points: `3 ${localeJSON['points']}` },
    { placement: localeJSON['seventhPlace'], points: `2 ${localeJSON['points']}` },
    { placement: localeJSON['eighthPlace'], points: `1 ${localeJSON['points']}` },
  ];

  return (
    <FlexLayout flexDirection={'column'} space={2}>
      <Box p={[3, 4]} backgroundColor="rgba(8, 0, 77, 0.3)" sx={{ width: 'fit-content' }}>
        <FlexLayout flexDirection={'column'} space={[3, 3, 4]}>
          <FlexLayout space={2} alignItems={'center'}>
            {first4Places.map((place, i, arr) => (
              <React.Fragment key={place.points}>
                <PlacePoints {...place} />
                {i !== arr.length - 1 && <Divider />}
              </React.Fragment>
            ))}
          </FlexLayout>
          <FlexLayout space={2} alignItems={'center'}>
            {second4Places.map((place, i, arr) => (
              <React.Fragment key={place.points}>
                <PlacePoints {...place} />
                {i !== arr.length - 1 && <Divider />}
              </React.Fragment>
            ))}
          </FlexLayout>
        </FlexLayout>
      </Box>
      <TiebreakerRulesExplainer />
    </FlexLayout>
  );
};

//todo use VerticalDivider
const Divider = () => <Box sx={{ width: 1, height: 17 }} backgroundColor={'white50'} />;
