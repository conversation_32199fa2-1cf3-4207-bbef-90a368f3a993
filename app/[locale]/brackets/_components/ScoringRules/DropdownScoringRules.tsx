'use client';

import { useToggle } from '@uidotdev/usehooks';

import { useSiteConfig } from '@/context/SiteConfig';
import { Box, FlexLayout, Icon, Text } from '@/ui';
import { PlacePoints } from './PlacePoints';

export const DropdownScoringRules = () => {
  const { localeJSON } = useSiteConfig();
  const [isOpen, toggleIsOpen] = useToggle(false);

  const placements = [
    { placement: localeJSON['firstPlace'], points: `8 ${localeJSON['points']}` },
    { placement: localeJSON['secondPlace'], points: `7 ${localeJSON['points']}` },
    { placement: localeJSON['thirdPlace'], points: `6 ${localeJSON['points']}` },
    { placement: localeJSON['fourthPlace'], points: `5 ${localeJSON['points']}` },
    { placement: localeJSON['fifthPlace'], points: `4 ${localeJSON['points']}` },
    { placement: localeJSON['sixthPlace'], points: `3 ${localeJSON['points']}` },
    { placement: localeJSON['seventhPlace'], points: `2 ${localeJSON['points']}` },
    { placement: localeJSON['eighthPlace'], points: `1 ${localeJSON['points']}` },
  ];

  return (
    <Box
      px={4}
      py={3}
      backgroundColor={'rgba(8,0,77,0.3)'}
      color="white"
      sx={{
        height: isOpen ? 'calc-size(auto)' : '46px',
        transition: 'height 300ms',
        interpolateSize: 'allow-keywords',
        overflowY: 'clip',
      }}
    >
      <FlexLayout flexDirection="column" space={4}>
        <FlexLayout
          justifyContent={'space-between'}
          space={4}
          sx={{ cursor: 'pointer' }}
          onClick={() => toggleIsOpen()}
        >
          <Text as={'p'} variant="paragraph-s-medium" sx={{ whiteSpace: 'nowrap', flexShrink: 0 }}>
            {localeJSON['bracketsHowScoringWorks']}
          </Text>
          <Icon icon="chevronDown" sx={{ transition: 'rotate 200ms', rotate: isOpen ? '180deg' : '0' }} />
        </FlexLayout>
        {isOpen && (
          <FlexLayout flexDirection="column" space={3}>
            {placements.map(({ placement, points }) => (
              <PlacePoints key={placement} placement={placement} points={points} />
            ))}
          </FlexLayout>
        )}
      </FlexLayout>
    </Box>
  );
};
