'use client';

import { useScreenType } from '@/ui';
import { FINALS_ROUTE } from '../const';
import { useRoundRouteParam } from '../hooks';
import { DropdownScoringRules } from './DropdownScoringRules';
import { FinalsRules } from './FinalsRules';
import { GridScoringRules } from './GridScoringRules';

export const ScoringRules = () => {
  const routeParam = useRoundRouteParam();
  const { isMobile } = useScreenType();

  if (routeParam === FINALS_ROUTE) {
    return <FinalsRules />;
  }

  return isMobile ? <DropdownScoringRules /> : <GridScoringRules />;
};
