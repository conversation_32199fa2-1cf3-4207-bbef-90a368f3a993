import { useSiteConfig } from '@/context/SiteConfig';
import { Box, FlexLayout, Text } from '@/ui';

export const FinalsRules = () => {
  const { localeJSON } = useSiteConfig();

  return (
    <FlexLayout flexDirection={'column'} space={2}>
      <Box p={[3, 4]} backgroundColor="rgba(8,0,77,0.3)" sx={{ maxWidth: '762px' }}>
        <Text variant="paragraph-s-regular">{localeJSON['finalsInfo']}</Text>
      </Box>
      {/* <MaxGameRulesExplainer /> */}
    </FlexLayout>
  );
};
