'use client';

import { QueryClient, QueryClientProvider, useQuery } from '@tanstack/react-query';
import React from 'react';

import { Box } from '@/ui';
import { LobbyLeaderboard } from '../_components';
import { FINALS_ROUTE } from '../_components/const';
import { SECOND_HEAT_ANCHOR_ID } from '../_components/JumpToHeatButton';
import { getRoundData } from '../_utils/getRoundData';

const queryClient = new QueryClient();

interface Props {
  roundId: string;
}

export function LobbyLeaderboardData({ roundId }: Props) {
  return (
    <QueryClientProvider client={queryClient}>
      <DataComponent roundId={roundId} />
    </QueryClientProvider>
  );
}

const DataComponent = ({ roundId }: Props) => {
  const { data } = useQuery({ queryFn: () => getRoundData(roundId), queryKey: ['brackets', roundId] });
  const secondHeatLobby = data?.find((d) => d.isSecondHeat);

  return data?.map((d) => (
    <Box id={d.id === secondHeatLobby?.id ? SECOND_HEAT_ANCHOR_ID : undefined} key={d.id}>
      <LobbyLeaderboard {...d} isFinalsLeaderboard={roundId === FINALS_ROUTE} />
    </Box>
  ));
};
