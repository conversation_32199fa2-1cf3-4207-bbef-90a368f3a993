'use client';

import Link from 'next/link';
import { usePathname } from 'next/navigation';

import { Box, Text } from '@/ui';

interface Props {
  label: string;
  path: string;
  isDisabled: boolean;
}

export const RoundNavItem: React.FC<Props> = ({ label, path, isDisabled }) => {
  const currentPath = usePathname();

  const isActive = currentPath.endsWith(path);
  const isClickable = !isActive && !isDisabled;

  const NavBox = (
    <Box
      as={'a'}
      sx={{
        flexShrink: 0,
        textDecoration: 'none',
        transition: 'color 250ms',
        color: isActive ? 'paleYellow600' : 'white',
        opacity: isDisabled ? 0.5 : 1,
        cursor: isClickable ? 'pointer' : 'default',
        ...(isClickable && {
          ':hover': { color: 'paleYellow300' },
        }),
      }}
    >
      <Text variant="brackets-round-nav-item" color="inherit">
        {label}
      </Text>
    </Box>
  );

  if (isClickable) {
    return (
      <Link href={path} passHref legacyBehavior>
        {NavBox}
      </Link>
    );
  }

  return NavBox;
};
