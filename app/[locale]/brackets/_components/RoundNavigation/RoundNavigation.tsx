'use client';

import { Container } from 'theme-ui';

import { useSiteConfig } from '@/context/SiteConfig';
import { Box, FlexLayout } from '@/ui';
import { disabledScrollbarStyles } from '@/ui/const';
import { FINALS_ROUTE } from '../const';
import { RoundNavItem } from './RoundNavItem';

interface Props {
  availableRounds: { id: string; name: string }[];
}

export const RoundNavigation = ({ availableRounds }: Props) => {
  const { localeJSON } = useSiteConfig();

  const navItems = [
    { name: 'Round 1 - 512', path: '/brackets/1' },
    { name: 'Round 2 - 256', path: '/brackets/2' },
    { name: 'Round 3 - 128', path: '/brackets/3' },
    { name: 'Round 4 - 64', path: '/brackets/4' },
    { name: 'Round 5 - 32', path: '/brackets/5' },
    { name: 'Round 6 - 16', path: '/brackets/6' },
    { name: 'Finals - 8', path: `/brackets/${FINALS_ROUTE}` },
  ];

  return (
    <Box
      sx={{
        position: 'sticky',
        top: '69px',
        zIndex: 1,
      }}
    >
      <Container>
        <FlexLayout px={[4, 6, 20]} py={[3]} backgroundColor="dreamBlue" justifyContent={'center'}>
          <FlexLayout
            space={6}
            sx={{
              overflow: 'auto',
              px: [4, 6, 20],
              mx: [-4, -6, -20],
              ...disabledScrollbarStyles,
            }}
          >
            {navItems.map((item) => (
              <RoundNavItem
                label={getRoundLocale(item.path, localeJSON)}
                key={item.path}
                {...item}
                isDisabled={!availableRounds.find((r) => r.name === item.name)}
              />
            ))}
          </FlexLayout>
        </FlexLayout>
      </Container>
    </Box>
  );
};

export function getRoundLocale(roundPath: string, localeJSON: Record<string, string>) {
  const localesMap: Record<string, string> = {
    '/brackets/1': localeJSON['bracketsRoundOne'],
    '/brackets/2': localeJSON['bracketsRoundTwo'],
    '/brackets/3': localeJSON['bracketsRoundThree'],
    '/brackets/4': localeJSON['bracketsRoundFour'],
    '/brackets/5': localeJSON['bracketsRoundFive'],
    '/brackets/6': localeJSON['bracketsRoundSix'],
    '/brackets/finals': localeJSON['bracketsFinals'],
  };

  return localesMap[roundPath];
}
