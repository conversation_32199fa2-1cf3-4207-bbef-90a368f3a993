'use client';

import { MouseEvent } from 'react';

import { Button } from '@/ui';

export const SECOND_HEAT_ANCHOR_ID = 'second-heat-anchor';

export const JumpToHeatButton = () => {
  function scrollToAnchor(e: MouseEvent) {
    e.preventDefault();
    document.getElementById(SECOND_HEAT_ANCHOR_ID)?.scrollIntoView({ behavior: 'smooth', block: 'center' });
  }

  return (
    <Button
      label="JUMP TO HEAT 2"
      sx={{ position: 'fixed', bottom: '75px', left: 0, right: 0, mx: 'auto' }}
      onClick={scrollToAnchor}
    />
  );
};
