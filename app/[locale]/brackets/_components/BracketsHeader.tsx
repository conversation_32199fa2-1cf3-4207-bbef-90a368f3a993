'use client';

import { usePathname } from 'next/navigation';

import { useSiteConfig } from '@/context/SiteConfig';
import { FlexLayout, Text } from '@/ui';
import { getRoundLocale } from './RoundNavigation';

export const BracketsHeader = () => {
  const { localeJSON } = useSiteConfig();
  const path = usePathname();

  return (
    <FlexLayout space={4} flexDirection={'column'}>
      <Text as="h1" variant="h1" color="white" upperCase>
        {localeJSON['navBrackets']}
      </Text>
      <Text as="h2" variant="h4" color="white" upperCase>
        {getRoundLocale(path, localeJSON)}
      </Text>
    </FlexLayout>
  );
};
