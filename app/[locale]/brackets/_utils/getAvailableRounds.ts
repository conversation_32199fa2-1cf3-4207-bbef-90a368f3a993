// import 'server-only';

// if (!process.env.FORGE_API_URL) {
//   throw new Error('Missing env var FORGE_API_URL!');
// }

// const bracketsUrl = `${process.env.FORGE_API_URL}/brackets`;

const ALL_ROUNDS = [
  { id: '1', name: 'Round 1 - 512' },
  { id: '2', name: 'Round 2 - 256' },
  { id: '3', name: 'Round 3 - 128' },
  { id: '4', name: 'Round 4 - 64' },
  { id: '5', name: 'Round 5 - 32' },
  { id: '6', name: 'Round 6 - 16' },
  { id: 'finals', name: 'Finals - 8' },
];

export async function getAvailableRounds() {
  try {
    const response = await fetch(`https://tft-static-prod.s3.us-east-2.amazonaws.com/available-rounds.json`);

    const data = (await response.json()) as string[];
    const availableRounds = ALL_ROUNDS.filter((r) => data.includes(r.name));

    return availableRounds;
  } catch (error) {
    throw new Error('Error fetching brackets data.');
  }
}
