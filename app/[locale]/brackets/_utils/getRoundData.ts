import { LobbyPlayerData } from '../_components/LobbyLeaderboard/LobbyTable/types';

// import 'server-only';

// if (!process.env.FORGE_API_URL) {
//   throw new Error('Missing env var FORGE_API_URL!');
// }

const bracketsUrl = `${process.env.NEXT_PUBLIC_FORGE_API_URL}/brackets`;

export interface BracketsResponse {
  id: number;
  title: string;
  isSecondHeat: boolean;
  stationNumber: number;
  data: LobbyPlayerData[];
}

export async function getRoundData(roundId: string) {
  const normalizedId = roundId === 'finals' ? 7 : roundId;
  try {
    const response = await fetch(`${bracketsUrl}/${normalizedId}`);
    if (!response.ok) {
      throw new Error('Invalid response');
    }

    const data = (await response.json()) as BracketsResponse[];
    return data;
  } catch (error) {
    throw new Error('Error fetching brackets data.');
  }
}
