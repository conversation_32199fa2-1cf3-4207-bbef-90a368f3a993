import { LobbyPlayerData } from '../_components/LobbyLeaderboard/LobbyTable/types';

import 'server-only';

if (!process.env.FORGE_API_URL) {
  throw new Error('Missing env var FORGE_API_URL!');
}

export interface BracketsResponse {
  id: number;
  title: string;
  isSecondHeat: boolean;
  stationNumber: number;
  data: LobbyPlayerData[];
}

export async function getRoundDataServer(roundId: string) {
  const normalizedId = roundId === 'finals' ? 7 : roundId;
  try {
    const response = await fetch(`${process.env.FORGE_API_URL}/brackets/${normalizedId}`, {
      next: { revalidate: 1200 },
    });
    if (!response.ok) {
      throw new Error('Invalid response');
    }

    const data = (await response.json()) as BracketsResponse[];
    return data;
  } catch (error) {
    throw new Error('Error fetching brackets data.');
  }
}
