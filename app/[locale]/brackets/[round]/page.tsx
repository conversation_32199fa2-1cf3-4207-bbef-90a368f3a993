import { redirect } from 'next/navigation';
import React from 'react';

import { Box } from '@/ui';
import { LobbyLeaderboard } from '../_components';
import { FINALS_ROUTE } from '../_components/const';
import { SECOND_HEAT_ANCHOR_ID } from '../_components/JumpToHeatButton';
import { getAvailableRounds } from '../_utils/getAvailableRounds';
import { BracketsResponse } from '../_utils/getRoundDataServer';

export default async function BracketsPage({ params }: { params: { round: string } }) {
  const availableRounds = await getAvailableRounds();

  const isAvailableRound = availableRounds.find((r) => r.id === params.round);
  if (!isAvailableRound) {
    redirect('/404');
  }

  const roundId = params.round === 'finals' ? '7' : params.round;
  const response = await fetch(`https://tft-static-prod.s3.us-east-2.amazonaws.com/brackets${roundId}.json`);
  const data = (await response.json()) as BracketsResponse[];

  const secondHeatLobby = data.find((d) => d.isSecondHeat);

  return data.map((d) => (
    <Box id={d.id === secondHeatLobby?.id ? SECOND_HEAT_ANCHOR_ID : undefined} key={d.id}>
      <LobbyLeaderboard {...d} isFinalsLeaderboard={params.round === FINALS_ROUTE} />
    </Box>
  ));
}
