import { Container } from '@/components';
import { Box, FlexLayout } from '@/ui';
import { BracketAvatar, BracketsHeader, RoundNavigation, ScoringRules } from '../_components';
import { JumpToHeatButton } from '../_components/JumpToHeatButton';
import { getAvailableRounds } from '../_utils/getAvailableRounds';

export default async function BracketsPageLayout({
  children,
  params,
}: React.PropsWithChildren<{ params: { round: string } }>) {
  const availableRounds = await getAvailableRounds();

  const isFirstRound = params.round === '1';

  return (
    <Box
      sx={{
        position: 'relative',
        overflow: 'clip',
        backgroundImage: 'url(/images/bracketsBackground.webp)',
        backgroundPosition: '0 -200px',
        backgroundSize: '100% clamp(1700px, 200vw, 2700px)',
      }}
    >
      <RoundNavigation availableRounds={availableRounds} />
      <Container>
        <Box px={[0, 0, 20]} pt={[6, 6, 12]} pb={20}>
          <FlexLayout flexDirection={'column'} space={[0, 8]} sx={{ position: 'relative' }}>
            <Box px={[4, 6, 0]} sx={{ display: ['none', 'none', 'block'] }}>
              <BracketsHeader />
            </Box>
            <Box px={[4, 6, 0]}>
              <ScoringRules />
            </Box>
            <Box
              sx={{
                position: ['relative', 'relative', 'absolute'],
                right: [0, 0, '-133px'],
                top: [0, '-60px', '-30px'],
                mx: 'auto',
                width: 'fit-content',
              }}
            >
              <BracketAvatar />
            </Box>
            <Box mt={[-5, '-153px', 0]}>
              <FlexLayout flexDirection={'column'} space={[12, 12, 8]}>
                {children}
              </FlexLayout>
            </Box>
          </FlexLayout>
        </Box>
      </Container>
      {isFirstRound && <JumpToHeatButton />}
    </Box>
  );
}
