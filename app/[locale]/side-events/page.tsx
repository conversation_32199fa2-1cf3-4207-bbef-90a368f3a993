import { isEmpty } from 'lodash';

import { getCollectionType, getSiteConfig } from '@/apis/strapi';
import { ScheduleInfoBlock } from '@/components';
import { EmptySideEventsBlock, SideEventsBlock, SideEventsHowToBlock } from '@/components/contentstack/SideEvents';
import { getEmptyPageProps, getPageEventsProps, getPageInfoProps } from '@/data/side-events';
import { PageProps } from '@/types/router';
import { SideEvent } from '@/types/strapi';
import { Box, FlexLayout } from '@/ui';

export * from '@/config/page-cache';

export default async function SideEvents({ params: { locale } }: PageProps) {
  const [siteConfig, sideEventsCollection] = await Promise.all([
    getSiteConfig(locale),
    getCollectionType<SideEvent[]>('side-events', locale),
  ]);

  const { imageAlt, imageUrl } = getPageInfoProps({ siteConfig });
  const { sideEvents, howTo } = getPageEventsProps({ siteConfig, sideEventsCollection });

  const areSideEventsEmpty = isEmpty(sideEvents.events);

  let pageBodyContent = (
    <EmptySideEventsBlock {...getEmptyPageProps({ siteConfig })} imageUrl={imageUrl} imageAlt={imageAlt} />
  );

  if (!areSideEventsEmpty) {
    pageBodyContent = (
      <FlexLayout space={[8, 12, 8]} flexDirection={['column', 'column', 'row']}>
        <SideEventsBlock sideEvents={sideEvents} />
        <Box sx={{ display: ['block', 'block', 'none'], height: '1px', width: '100%', backgroundColor: 'white10' }} />
        <SideEventsHowToBlock howTo={howTo} />
      </FlexLayout>
    );
  }

  return (
    <FlexLayout
      flexGrow={1}
      sx={{
        backgroundImage: 'url(/images/eventScheduleBackground.png)',
        backgroundSize: 'cover',
        backgroundRepeat: 'no-repeat',
      }}
    >
      <Box sx={{ position: 'relative', width: '100%' }} mt={[4, 8, 14]} mb={20} mx={[4, 6, 20]}>
        {/* <Image
          src={imageUrl}
          alt={imageAlt}
          sx={{
            width: [0, 286, 340],
            minWidth: [0, 286, 340],
            height: [0, 286, 340],
            minHeight: [0, 286, 340],
            position: 'absolute',
            top: [0, '75px', '-30px'],
            right: [0, '15px', '40px'],
            display: areSideEventsEmpty ? ['none', 'block', 'block'] : ['none', 'none', 'block'],
          }}
        /> */}
        <FlexLayout
          space={areSideEventsEmpty ? [8, 22, 22] : [8, 8, 11]}
          flexDirection="column"
          sx={{ position: 'relative' }}
        >
          <Box sx={{ maxWidth: ['100%', '374px', '100%'], width: '100%' }}>
            <ScheduleInfoBlock {...getPageInfoProps({ siteConfig })} />
          </Box>
          {pageBodyContent}
        </FlexLayout>
      </Box>
    </FlexLayout>
  );
}
