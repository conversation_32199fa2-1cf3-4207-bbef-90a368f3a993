'use client';

import { useEffect } from 'react';

import { FlexLayout, LoadingIndicator } from '@/ui';

export const RedirectComponent = () => {
  useEffect(() => {
    // Redirect after a delay
    setTimeout(() => {
      window.location.href = 'https://paris.competetft.com';
    }, 1000);
  }, []);

  return (
    <FlexLayout alignItems="center" justifyContent="center" sx={{ height: ['30vh', '50vh', '75vh'] }}>
      <LoadingIndicator />
    </FlexLayout>
  );
};
