import { Metadata } from 'next';

import { RedirectComponent } from './RedirectComponent';

export * from '@/config/page-cache';

export default function GetYourCompetitorBundle() {
  return <RedirectComponent />;
}

const title = 'I’m going to TFT Paris Open! | Dec 12-14, 2025';

const description = `Join us for the 3rd annual TFT Open taking place at Paris from December 12 - 14! There will be a jam-packed schedule each day including the main tournament matches, side events, artist alley, influencer meet and greets, Riot dev panels, and tons of opportunities to meet new people.`;

export const metadata: Metadata = {
  title,
  openGraph: {
    url: 'https://paris.competetft.com/get-your-competitor-bundle',
    type: 'website',
    title,
    description,
    images: [
      {
        url: 'https://paris.competetft.com/social-share-og-photo.png',
        width: 1200,
        height: 630,
      },
    ],
  },
  twitter: {
    card: 'summary_large_image',
    title,
    description,
    images: ['https://paris.competetft.com/social-share-og-photo.png'],
  },
};
