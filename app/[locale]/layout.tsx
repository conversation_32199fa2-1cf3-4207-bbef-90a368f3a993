import { Metadata } from 'next';
import { headers } from 'next/headers';
import { getServerSession } from 'next-auth';
import { PropsWithChildren } from 'react';

import { getSiteConfig } from '@/apis/strapi';
import {
  Footer,
  Header,
  // OsanoScript,
  PlausibleScript,
  RegistrationFlow,
  SessionProvider,
  ThemeUIProvider,
} from '@/components';
import { LegalDocumentsModals } from '@/components/LegalDocumentsModals';
import { authOptions } from '@/config/next-auth';
import { DropInfoProvider } from '@/context/DropInfoProvider';
import { SiteConfigProvider } from '@/context/SiteConfig';
import { getModals } from '@/data/legalDocumentsModals';
import { getDropInfo, getUserContext } from '@/services/drops';
import { LayoutProps } from '@/types/router';
import { theme, Toaster, TooltipProvider } from '@/ui';

import '@/styles/global.css';
import '@/styles/typekit.css';

export default async function RootLayout({ params: { locale }, children }: PropsWithChildren<LayoutProps>) {
  const headersList = headers();
  const isTvSchedulePage = headersList.get('x-pathname') === '/tv-schedule';

  const [session, siteConfig, dropInfo] = await Promise.all([
    getServerSession(authOptions),
    getSiteConfig(locale),
    getDropInfo(),
  ]);

  const isUserSignedIn = !!session;
  const isUserRegistered = !!session?.user.extRegistred;

  const userContext = session?.user ? await getUserContext(session?.user) : null;

  const modalsData = getModals({ siteConfig });

  return (
    <html lang={locale}>
      <SessionProvider session={session}>
        <ThemeUIProvider theme={theme}>
          <DropInfoProvider general={dropInfo} user={userContext}>
            <SiteConfigProvider data={siteConfig}>
              <TooltipProvider>
                <body>
                  {isUserSignedIn && !isUserRegistered ? (
                    <RegistrationFlow />
                  ) : isTvSchedulePage ? (
                    children
                  ) : (
                    <>
                      <Header />
                      {children}
                      <Footer />
                    </>
                  )}
                  <div id="modal-container" />
                  <Toaster />
                  {isUserSignedIn && isUserRegistered && <LegalDocumentsModals modalsData={modalsData} />}
                </body>
              </TooltipProvider>
            </SiteConfigProvider>
          </DropInfoProvider>
        </ThemeUIProvider>
      </SessionProvider>
      {/* // !Important: For some strange reason NextScript breaks the app, we keep getting chunkLoadTimeout
// https://github.com/vercel/next.js/issues/66526
	 */}
      <PlausibleScript />
      {/* <OsanoScript /> */}
    </html>
  );
}

const description = `Join us for the 3rd annual TFT Open taking place at the Paris Expo Porte de Versailles from December 12 - 14! There will be a jam-packed schedule each day including the main tournament matches, side events, artist alley, influencer meet and greets, Riot dev panels, and tons of opportunities to meet new friends.`;

export const metadata: Metadata = {
  title: 'TFT Paris Open | Dec 12-14, 2025',
  description,
  metadataBase: new URL('https://paris.competetft.com'),
  openGraph: {
    title: 'TFT Paris Open | Dec 12-14, 2025',
    description,
    images: [
      {
        url: 'https://paris.competetft.com/og-photo.png',
        width: 1200,
        height: 630,
      },
    ],
  },
};
