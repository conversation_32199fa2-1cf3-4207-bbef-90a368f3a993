import isEmpty from 'lodash/isEmpty';

import { getPageConfig, getSiteConfig } from '@/apis/strapi';
import { EmptyEventScheduleBlock, ScheduleInfoBlock } from '@/components';
import { getEmptyPageProps, getPageInfoProps } from '@/data/event-schedule';
import { PageProps } from '@/types/router';
import { EventSchedulePage } from '@/types/strapi';
import { Box, FlexLayout } from '@/ui';
import { MainContent, StreamList } from './_components';
import { LobbyData } from './_components/LobbyList/types';

const getLobbyData = async (): Promise<LobbyData[]> => {
  try {
    const response = await fetch('https://tft-static-prod.s3.us-east-2.amazonaws.com/lobbies.json');
    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }
    return (await response.json()) as LobbyData[];
  } catch (error) {
    console.error('Failed to fetch lobby data:', error);
    return [];
  }
};

export default async function EventSchedule({ params: { locale } }: PageProps) {
  const [siteConfig, pageConfig, lobbies] = await Promise.all([
    getSiteConfig(locale),
    getPageConfig<EventSchedulePage>('page-event-schedule', locale),
    getLobbyData(),
  ]);

  const streams = pageConfig?.streams ?? [];

  return (
    <FlexLayout
      flexGrow={1}
      sx={{
        backgroundImage: 'url(/images/eventScheduleBackground.png)',
        backgroundSize: 'cover',
        backgroundRepeat: 'no-repeat',
      }}
    >
      <Box px={[4, 6, 20]} py={[12, 8, 18]} sx={{ width: '100%' }}>
        <FlexLayout flexDirection={['column', 'column', 'row']} space={[8, 12, 8]}>
          <FlexLayout flexDirection={'column'} space={[8, 12, 20]} sx={{ flex: 'auto' }}>
            <ScheduleInfoBlock {...getPageInfoProps({ siteConfig })} />
            <EmptyEventScheduleBlock {...getEmptyPageProps({ siteConfig })} />
            {lobbies && lobbies.length > 0 && <MainContent lobbies={lobbies} />}
          </FlexLayout>
          {!isEmpty(streams) && (
            <Box sx={{ mt: [0, 0, '200px'], flex: ['auto', 'auto', '1 1 392px'], flexShrink: 0 }}>
              <StreamList streams={streams} />
            </Box>
          )}
        </FlexLayout>
      </Box>
    </FlexLayout>
  );
}
