import { Box, Text } from '@/ui';

interface Props {
  label: string;
  isActive: boolean;
  isDisabled: boolean;
  onSelect: () => void;
}

export const DateSelect: React.FC<Props> = ({ label, isActive, isDisabled, onSelect }) => {
  const isClickable = !isActive && !isDisabled;

  return (
    <Box
      px={[4, 6]}
      pt={['6px', 3]}
      pb={['10px', 4]}
      backgroundColor={isActive ? 'purpleLavander500' : isDisabled ? 'brightBlue30' : '#AC6CDD4D'}
      onClick={isClickable ? onSelect : undefined}
      sx={{
        flexGrow: 1,
        flexShrink: 0,
        transition: 'background-color 250ms',
        ...(isClickable && {
          cursor: 'pointer',
          ':hover': { backgroundColor: 'purpleLavander400' },
        }),
      }}
    >
      <Text
        as={'p'}
        textVariant={['h7', 'h5']}
        isCentered
        upperCase
        color={isActive ? 'white' : 'white50'}
        sx={{ whiteSpace: 'nowrap' }}
      >
        {label}
      </Text>
    </Box>
  );
};
