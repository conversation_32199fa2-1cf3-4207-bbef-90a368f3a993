'use client';

import { useSiteConfig } from '@/context/SiteConfig';
import { FlexLayout } from '@/ui';
import { disabledScrollbarStyles } from '@/ui/const';
import { DEC_13_DATE, DEC_14_DATE, DEC_15_DATE } from '../const';
import { DateSelect } from './DateSelect';

interface Props {
  activeDate: string;
  availableDates: string[];
  onDateSelect: (date: string) => void;
}

export const DateSelectList = ({ activeDate, availableDates, onDateSelect }: Props) => {
  const { localeJSON } = useSiteConfig();

  const navItems = [
    { date: DEC_13_DATE, label: localeJSON['sideEventFirstTab'] },
    { date: DEC_14_DATE, label: localeJSON['sideEventSecondTab'] },
    { date: DEC_15_DATE, label: localeJSON['sideEventThirdTab'] },
  ];

  return (
    <FlexLayout
      space={[3, 6]}
      flexGrow={1}
      px={[4, 0]}
      mx={[-4, 0]}
      sx={{ overflow: 'auto', ...disabledScrollbarStyles }}
    >
      {navItems.map(({ date, label }) => (
        <DateSelect
          key={date}
          isDisabled={!availableDates.includes(date)}
          onSelect={() => onDateSelect(date)}
          label={label}
          isActive={activeDate === date}
        />
      ))}
    </FlexLayout>
  );
};
