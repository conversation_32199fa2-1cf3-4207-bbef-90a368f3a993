'use client';

import { QueryClient, QueryClientProvider, useQuery } from '@tanstack/react-query';

import { getLobbyData } from '../_api';
import { MainContent } from './MainContent';

const queryClient = new QueryClient();

export function LobbyListData() {
  return (
    <QueryClientProvider client={queryClient}>
      <DataComponent />
    </QueryClientProvider>
  );
}

export function DataComponent() {
  const { data } = useQuery({ queryFn: getLobbyData, queryKey: ['event-schedule'] });

  return <MainContent lobbies={data ?? []} />;
}
