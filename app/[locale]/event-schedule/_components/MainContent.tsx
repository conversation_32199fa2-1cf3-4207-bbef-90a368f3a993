'use client';

import { isEmpty, sortBy, uniq } from 'lodash';

import { FlexLayout, HorizontalDivider } from '@/ui';
import { macaoFormat } from '@/utils';
import { DateSelectList } from './DateSelectList';
import { useActiveDate } from './hooks';
import { LobbyList } from './LobbyList';
import { LobbyData } from './LobbyList/types';

interface Props {
  lobbies: LobbyData[];
}

export const MainContent = ({ lobbies }: Props) => {
  const { activeDate, setActiveDate } = useActiveDate();

  const currentLobbies = lobbies.filter((l) => macaoFormat(l.startTime, 'yyyy-MM-dd') === activeDate);
  const sortedLobbies = sortBy(currentLobbies, 'startTime');
  const featuredLobbies = sortedLobbies.filter((l) => l.streamUrl);

  const lobbyDates = lobbies.map((l) => macaoFormat(l.startTime, 'yyyy-MM-dd'));
  const uniqueDates = uniq(lobbyDates);

  return (
    <FlexLayout flexDirection={'column'} space={[6, 10]}>
      <DateSelectList activeDate={activeDate} onDateSelect={setActiveDate} availableDates={uniqueDates} />
      <FlexLayout flexDirection={'column'} space={[8, 12, 10]}>
        {!isEmpty(featuredLobbies) && (
          <>
            <LobbyList lobbies={featuredLobbies} isFeatured />
            <HorizontalDivider />
          </>
        )}
        <LobbyList lobbies={sortedLobbies} />
      </FlexLayout>
    </FlexLayout>
  );
};
