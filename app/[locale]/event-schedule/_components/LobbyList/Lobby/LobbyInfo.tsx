'use client';

import { formatInTimeZone } from 'date-fns-tz';

import { useSiteConfig } from '@/context/SiteConfig';
import { Box, FlexLayout, Icon, Text, Tooltip, useScreenType } from '@/ui';
import { CEST_TIMEZONE_OFFSET, ET_TIMEZONE_OFFSET, macaoFormat, PT_TIMEZONE_OFFSET } from '@/utils';

interface Props {
  title: string;
  startTime: string;
  station: number;
}

export const LobbyInfo = ({ title, startTime, station }: Props) => {
  const { isMobile } = useScreenType();
  const { localeJSON } = useSiteConfig();

  return (
    <FlexLayout flexDirection={'column'} space={4}>
      <Text as={'h3'} textVariant={['paragraph-l-bold', 'paragraph-xl-bold']} upperCase>
        {title}
      </Text>
      <FlexLayout space={8}>
        <InfoBlock
          header={localeJSON['mainEventScheduleStartTime']}
          description={macaoFormat(startTime, 'hh:mm a')}
          tooltipContent={
            <FlexLayout flexDirection={'column'} space={3} alignItems={'center'}>
              <Text color="midnight900" variant="paragraph-m-medium">
                Other Time Zones
              </Text>
              <FlexLayout flexDirection={['column', 'row']} space={[0, 1]}>
                <Text color="midnight900" variant="paragraph-s-bold">
                  {formatInTimeZone(startTime, PT_TIMEZONE_OFFSET, 'h:mm a (eee)')} PT {!isMobile && '/'}
                </Text>
                <Text color="midnight900" variant="paragraph-s-bold">
                  {formatInTimeZone(startTime, ET_TIMEZONE_OFFSET, 'h:mm a (eee)')} ET {!isMobile && '/'}
                </Text>
                <Text color="midnight900" variant="paragraph-s-bold">
                  {formatInTimeZone(startTime, CEST_TIMEZONE_OFFSET, 'h:mm a (eee)')} CEST
                </Text>
              </FlexLayout>
            </FlexLayout>
          }
        />
        <InfoBlock header={localeJSON['mainEventScheduleStation']} description={station.toString()} />
      </FlexLayout>
    </FlexLayout>
  );
};

interface InfoBlockProps {
  header: string;
  description: string;
  tooltipContent?: React.ReactNode;
}

const InfoBlock = ({ header, description, tooltipContent }: InfoBlockProps) => (
  <FlexLayout flexDirection={'column'}>
    <Text upperCase as={'p'} variant="paragraph-xs-medium" color="white70">
      {header}
    </Text>
    <FlexLayout sx={{ gap: '6px' }} alignItems={'center'}>
      <Text upperCase as={'p'} variant="paragraph-l-medium" suppressHydrationWarning>
        {description}
      </Text>
      {tooltipContent && (
        <Tooltip
          content={
            <Box p={6} pt={4} bg="white">
              {tooltipContent}
            </Box>
          }
          children={<Icon color="white" size="s" icon={'info'} />}
          arrowColor={'white'}
        />
      )}
    </FlexLayout>
  </FlexLayout>
);
