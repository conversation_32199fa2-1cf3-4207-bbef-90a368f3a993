'use client';

import React from 'react';

import { useSiteConfig } from '@/context/SiteConfig';
import { Box, FlexLayout, HorizontalDivider, Text } from '@/ui';
import { Player } from '../types';

interface Props {
  players: Player[];
  isExpanded: boolean;
}

export const PlayerBreakdown = ({ players, isExpanded }: Props) => {
  if (isExpanded) {
    return <PointsView players={players} />;
  }
  return <PlayersView players={players} />;
};

const PlayersView = ({ players }: Pick<Props, 'players'>) => {
  const { localeJSON } = useSiteConfig();

  return (
    <FlexLayout flexDirection={'column'} space={2}>
      <Text as={'p'} upperCase variant="paragraph-xs-medium" color="white70">
        {localeJSON['mainEventSchedulePlayers']}
      </Text>
      <FlexLayout sx={{ rowGap: 1, columnGap: [1, 2] }} flexWrap={'wrap'} alignItems={'center'}>
        {players.map((p, i, arr) => (
          <React.Fragment key={p.id}>
            <Text as={'p'} textVariant={['paragraph-s-medium', 'paragraph-m-medium']}>
              {p.name}
            </Text>
            {i !== arr.length - 1 && (
              <Box
                sx={{
                  height: ['2px', '4px'],
                  width: ['8px', '10px'],
                  borderRadius: '20px',
                  backgroundColor: 'white20',
                }}
              />
            )}
          </React.Fragment>
        ))}
      </FlexLayout>
    </FlexLayout>
  );
};

const PointsView = ({ players }: Pick<Props, 'players'>) => {
  const { localeJSON } = useSiteConfig();

  return (
    <FlexLayout flexDirection={'column'} space={4}>
      <FlexLayout justifyContent={'space-between'}>
        <Text as={'p'} upperCase variant="paragraph-xs-medium" color="white70">
          {localeJSON['mainEventSchedulePlayers']}
        </Text>
        <Text as={'p'} upperCase variant="paragraph-xs-medium" color="white70">
          {localeJSON['mainEventSchedulePoints']}
        </Text>
      </FlexLayout>
      <FlexLayout flexDirection={'column'} space={2}>
        {players.map((p, i, arr) => (
          <React.Fragment key={p.id}>
            <FlexLayout justifyContent={'space-between'} space={6}>
              <Text as={'p'} textVariant={['paragraph-s-medium', 'paragraph-m-medium']}>
                {p.name}
              </Text>
              <Text as={'p'} textVariant={['paragraph-s-medium', 'paragraph-m-medium']}>
                {p.totalPoints}
              </Text>
            </FlexLayout>
            {i !== arr.length - 1 && <HorizontalDivider />}
          </React.Fragment>
        ))}
      </FlexLayout>
    </FlexLayout>
  );
};
