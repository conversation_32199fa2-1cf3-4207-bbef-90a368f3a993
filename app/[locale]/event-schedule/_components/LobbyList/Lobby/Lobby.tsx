'use client';

import { useToggle } from '@uidotdev/usehooks';
import { isAfter } from 'date-fns';
import { isWithinInterval } from 'date-fns/isWithinInterval';

import { useSiteConfig } from '@/context/SiteConfig';
import { Box, Button, FlexLayout, HorizontalDivider } from '@/ui';
import { Player } from '../types';
import { LobbyInfo } from './LobbyInfo';
import { PlayerBreakdown } from './PlayerBreakdown';

interface Props {
  name: string;
  players: Player[];
  startTime: string;
  endTime: string;
  station: number;
  streamUrl: string | null;
  isFeatured?: boolean;
}

export const Lobby = ({ players, name, startTime, endTime, station, isFeatured, streamUrl }: Props) => {
  const { localeJSON } = useSiteConfig();
  const [isExpanded, toggleIsExpanded] = useToggle(false);

  const isPlaying = isWithinInterval(Date.now(), { start: startTime, end: endTime });
  const hasEnded = isAfter(Date.now(), endTime);

  return (
    <Box px={[4, 8]} py={[4, 6]} backgroundColor="rgba(8,0,77,0.4)" sx={{ backdropFilter: 'blur(4px)' }}>
      <FlexLayout space={[4, 6]} flexDirection={'column'}>
        <FlexLayout flexDirection={['column', 'row']} justifyContent={['start', 'space-between']} space={[4, 0]}>
          <LobbyInfo title={name} startTime={startTime} station={station} />
          <Box mt={[0, 2]}>
            {isFeatured && !hasEnded && streamUrl ? (
              <Button
                variant="primary"
                sx={{ width: '100%' }}
                href={streamUrl}
                as="a"
                label={localeJSON['mainEventScheduleWatchLive']}
                isDisabled={!isPlaying}
                target="_blank"
                size="medium"
              />
            ) : (
              <Button
                variant="primary"
                sx={{ width: '100%' }}
                label={isExpanded ? localeJSON['mainEventScheduleHideScore'] : localeJSON['mainEventScheduleSeeScore']}
                icon={isExpanded ? 'chevronUp' : 'chevronDown'}
                onClick={() => toggleIsExpanded()}
                size="medium"
              />
            )}
          </Box>
        </FlexLayout>
        <HorizontalDivider color="white10" />
        <PlayerBreakdown players={players} isExpanded={isExpanded} />
      </FlexLayout>
    </Box>
  );
};
