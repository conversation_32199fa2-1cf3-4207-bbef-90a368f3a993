export interface LobbyData {
  id: number;
  name: string;
  station: number;
  streamUrl: string | null;
  startTime: string;
  endTime: string;
  round: string;
  players: Player[];
}

export interface Player {
  id: number;
  name: string;
  riotId: string;
  totalPoints: number;
}

export interface TvLobbyData {
  id: number;
  name: string;
  stationId: number;
  startTime?: string;
  players: TvPlayer[];
}

export interface TvPlayer {
  id: number;
  displayName: string;
}
