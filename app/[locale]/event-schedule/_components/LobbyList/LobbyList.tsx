'use client';

import { useSiteConfig } from '@/context/SiteConfig';
import { Box, Button, FlexLayout, Text, useScreenType } from '@/ui';
import { useVisibleItems } from '../hooks';
import { Lobby } from './Lobby';
import { LobbyData } from './types';

interface Props {
  lobbies: LobbyData[];
  isFeatured?: boolean;
}

export const LobbyList = ({ isFeatured, lobbies }: Props) => {
  const { localeJSON } = useSiteConfig();
  const { visibleLobbies, areAllLobbiesVisible, showAllLobbies } = useVisibleLobbies(lobbies);

  return (
    <FlexLayout space={8} flexDirection={'column'}>
      <Text as="h2" textVariant={['h4', 'h3']} upperCase>
        {isFeatured ? localeJSON['mainEventScheduleFeaturedLobbies'] : localeJSON['mainEventScheduleLobbies']}
      </Text>
      <FlexLayout space={[6, 12]} flexDirection={'column'}>
        {visibleLobbies.map((l) => (
          <Lobby key={l.id} {...l} isFeatured={isFeatured} />
        ))}
        {!areAllLobbiesVisible && (
          <Box sx={{ alignSelf: 'center' }}>
            <Button label={localeJSON['seeMoreButton']} onClick={showAllLobbies} size="small" />
          </Box>
        )}
      </FlexLayout>
    </FlexLayout>
  );
};

function useVisibleLobbies(lobbies: LobbyData[]) {
  const { isMobile } = useScreenType();
  const {
    visibleItems: visibleLobbies,
    areAllItemsVisible: areAllLobbiesVisible,
    showAllItems: showAllLobbies,
  } = useVisibleItems(lobbies, isMobile ? 3 : lobbies.length);

  return { visibleLobbies, areAllLobbiesVisible, showAllLobbies };
}
