import { isBefore, isSameDay } from 'date-fns';
import { useEffect, useState } from 'react';

import { DEC_13_DATE, DEC_14_DATE, DEC_15_DATE } from './const';

export function useVisibleItems<T>(items: T[], maxVisibleItems: number) {
  const [visibleItems, setVisibleItems] = useState(() => items.slice(0, maxVisibleItems));
  useEffect(() => setVisibleItems(items.slice(0, maxVisibleItems)), [items, maxVisibleItems]);

  return {
    visibleItems,
    areAllItemsVisible: visibleItems.length === items.length,
    showAllItems: () => setVisibleItems(items),
  };
}

export function useActiveDate() {
  const [activeDate, setActiveDate] = useState(() => {
    const now = Date.now();

    if (isBefore(now, DEC_13_DATE) || isSameDay(now, DEC_13_DATE)) {
      return DEC_13_DATE;
    } else if (isSameDay(now, DEC_14_DATE)) {
      return DEC_14_DATE;
    }
    return DEC_15_DATE;
  });

  return { activeDate, setActiveDate };
}
