'use client';

import { useSiteConfig } from '@/context/SiteConfig';
import { Stream } from '@/types/strapi';
import { Box, Button, FlexLayout, Text, useScreenType } from '@/ui';
import { useVisibleItems } from '../hooks';
import { StreamBox } from './StreamBox';

interface Props {
  streams: Stream[];
}

export const StreamList = ({ streams }: Props) => {
  const { localeJSON } = useSiteConfig();
  const { visibleStreams, areAllStreamsVisible, showAllStreams } = useVisibleStreams(streams);

  return (
    <Box backgroundColor="rgba(8,0,77,0.3)" p={4} sx={{ backdropFilter: 'blur(5px)' }}>
      <FlexLayout alignItems={'center'} flexDirection={'column'} space={6}>
        <FlexLayout alignItems={'center'} flexDirection={'column'} sx={{ width: '100%' }}>
          <Box pt={4} px={6} pb={6}>
            <Text as={'h3'} variant="h4" upperCase>
              {localeJSON['mainEventScheduleStreams']}
            </Text>
          </Box>
          <FlexLayout flexDirection={'column'} space={2} sx={{ width: '100%' }}>
            {visibleStreams.map((s) => (
              <StreamBox key={s.id} {...s} />
            ))}
          </FlexLayout>
        </FlexLayout>
        {!areAllStreamsVisible && <Button label={localeJSON['seeMoreButton']} onClick={showAllStreams} size="medium" />}
      </FlexLayout>
    </Box>
  );
};

function useVisibleStreams(streams: Stream[]) {
  const { isDesktop } = useScreenType();
  const {
    visibleItems: visibleStreams,
    areAllItemsVisible: areAllStreamsVisible,
    showAllItems: showAllStreams,
  } = useVisibleItems(streams, isDesktop ? 10 : 5);

  return { visibleStreams, areAllStreamsVisible, showAllStreams };
}
