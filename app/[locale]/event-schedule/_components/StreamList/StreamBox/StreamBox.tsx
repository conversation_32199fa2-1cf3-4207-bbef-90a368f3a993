import { Stream, StreamPlatform } from '@/types/strapi';
import { Box, FlexLayout, Icon, Link, Text } from '@/ui';
import {
  DouyinIcon,
  DouyuIcon,
  HuyaIcon,
  KuaishouIcon,
  NaverIcon,
  SoopIcon,
  TiktokIcon,
  TwitchIcon,
  YoutubeIcon,
} from './icons';

export const StreamBox = ({ label, platform, url }: Stream) => {
  return (
    <Box
      p={4}
      backgroundColor="rgba(172,108,221,0.4)"
      sx={{
        cursor: 'pointer',
        transition: 'background-color 250ms',
        ':hover': { backgroundColor: 'rgba(172,108,221,0.7)' },
      }}
    >
      <Link href={url} target="_blank">
        <FlexLayout space={4} alignItems={'center'}>
          <PlatformIcon platform={platform} />
          <FlexLayout justifyContent={'space-between'} alignItems={'center'} flexGrow={1} space={4} color="white">
            <Text as="p" variant="paragraph-m-medium" upperCase>
              {label}
            </Text>
            <Icon icon="arrowRight" size="s" />
          </FlexLayout>
        </FlexLayout>
      </Link>
    </Box>
  );
};

const PlatformIcon = ({ platform }: Pick<Stream, 'platform'>) => {
  switch (platform) {
    case StreamPlatform.DOUYIN:
      return <DouyinIcon />;
    case StreamPlatform.DOUYU:
      return <DouyuIcon />;
    case StreamPlatform.HUYA:
      return <HuyaIcon />;
    case StreamPlatform.KUAISHOU:
      return <KuaishouIcon />;
    case StreamPlatform.NAVER:
      return <NaverIcon />;
    case StreamPlatform.SOOP:
      return <SoopIcon />;
    case StreamPlatform.TIKTOK:
      return <TiktokIcon />;
    case StreamPlatform.TWITCH:
      return <TwitchIcon />;
    default:
      return <YoutubeIcon />;
  }
};
