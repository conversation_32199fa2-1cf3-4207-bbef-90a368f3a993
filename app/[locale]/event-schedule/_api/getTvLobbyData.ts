// import 'server-only';

import { TvLobbyData } from '../_components/LobbyList/types';

// if (!process.env.FORGE_API_URL) {
//   throw new Error('Missing env var FORGE_API_URL!');
// }

const lobbiesUrl = `https://tft-static-prod.s3.us-east-2.amazonaws.com/active-lobby.json`;

export async function getTvLobbyData() {
  try {
    const response = await fetch(lobbiesUrl);
    if (!response.ok) {
      console.log('response', response);
      throw new Error('Invalid response');
    }

    const data = (await response.json()) as TvLobbyData[];
    return data;
  } catch (error) {
    throw new Error('Error fetching brackets data.');
  }
}
