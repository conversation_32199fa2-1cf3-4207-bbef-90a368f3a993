import { LobbyData } from '../_components/LobbyList/types';

// import 'server-only';

// if (!process.env.FORGE_API_URL) {
//   throw new Error('Missing env var FORGE_API_URL!');
// }

const lobbiesUrl = `${process.env.NEXT_PUBLIC_FORGE_API_URL}/lobbies`;

export async function getLobbyData() {
  try {
    const response = await fetch(lobbiesUrl);
    if (!response.ok) {
      console.log('response', response);
      throw new Error('Invalid response');
    }

    const data = (await response.json()) as LobbyData[];
    return data;
  } catch (error) {
    throw new Error('Error fetching brackets data.');
  }
}
