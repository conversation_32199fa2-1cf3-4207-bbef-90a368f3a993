import { LobbyData } from '../_components/LobbyList/types';

import 'server-only';

if (!process.env.FORGE_API_URL) {
  throw new Error('Missing env var FORGE_API_URL!');
}

const lobbiesUrl = `${process.env.FORGE_API_URL}/lobbies`;

export async function getLobbyDataServer() {
  try {
    const response = await fetch(lobbiesUrl, { next: { revalidate: 1200 } });
    if (!response.ok) {
      throw new Error('Invalid response');
    }

    const data = (await response.json()) as LobbyData[];
    return data;
  } catch (error) {
    throw new Error('Error fetching brackets data.');
  }
}
