import { getSiteConfig } from '@/apis/strapi';
import { getDashboardLocales } from '@/data/dashboard';
import { PageProps } from '@/types/router';
import { DesktopDashboard, TabletMobileDashboard } from './_components';
import { getDataServer } from './_utils/getDataServer';

export default async function Dashboard({ params: { locale } }: PageProps) {
  const [siteConfig, data] = await Promise.all([getSiteConfig(locale), getDataServer()]);
  const localeData = getDashboardLocales({ siteConfig });

  const props = {
    localeData,
    lobbiesData: data.lobbies,
    leaderboardData: data.leaderboard,
    status: data.status,
  };

  return (
    <>
      <DesktopDashboard {...props} />
      <TabletMobileDashboard {...props} />
    </>
  );
}
