import { getServerSession } from 'next-auth';
import isEmpty from 'lodash/isEmpty';

import { LobbyData } from '@/app/[locale]/event-schedule/_components/LobbyList/types';
import { LeaderboardDto, toLeaderboardData } from '@/app/[locale]/leaderboard/_api/getLeaderboardDataServer';
import { authOptions } from '@/config/next-auth';
import { CompetitorStatus } from '../_components/types';

import 'server-only';

// if (!process.env.FORGE_API_URL) {
//   throw new Error('Missing env var FORGE_API_URL!');
// }

// const leaderboardUrl = `${process.env.FORGE_API_URL}/leaderboard`;
// const lobbiesUrl = `${process.env.FORGE_API_URL}/lobbies`;
// const players = `${process.env.FORGE_API_URL}/players`;

async function fetchPlayerLeaderboard(riotId: string) {
  const response = await fetch('https://tft-static-prod.s3.us-east-2.amazonaws.com/leaderboard.json');
  const data = (await response.json()) as LeaderboardDto[];
  const leaderboardData = data.map(toLeaderboardData);

  const playerLeaderboard = leaderboardData.find((l) => l.riotId === riotId);
  if (!playerLeaderboard) {
    return null;
  }

  const playerData = {
    points: playerLeaderboard.points,
    //todo calculate regional rank based on country (currently not added to seeding data)
    regionRank: playerLeaderboard.rank,
    overallRank: playerLeaderboard.rank,
  };
  return playerData;
}

async function fetchLobbies(riotId: string) {
  const response = await fetch('https://tft-static-prod.s3.us-east-2.amazonaws.com/lobbies.json');
  const lobbies = (await response.json()) as LobbyData[];
  const playerLobbies = lobbies.filter((l) => l.players.some((p) => p.riotId === riotId));

  return playerLobbies;
}

async function fetchCompetitorStatus(riotId: string) {
  const response = await fetch('https://tft-static-prod.s3.us-east-2.amazonaws.com/competitor-statuses.json');
  const statuses = (await response.json()) as { riotId: string; status: CompetitorStatus }[];
  const playerStatus = statuses.find((s) => s.riotId === riotId)?.status;

  return playerStatus ?? CompetitorStatus.OBSERVER;
}

export async function getDataServer() {
  const session = await getServerSession(authOptions);

  if (!session?.user.extHasTicket) {
    return { leaderboard: null, lobbies: null, status: CompetitorStatus.OBSERVER };
  }

  try {
    const riotId = session?.user.extRiotId as string;

    const leaderboardFetch = fetchPlayerLeaderboard(riotId);
    const lobbiesFetch = fetchLobbies(riotId);
    const statusFetch = fetchCompetitorStatus(riotId);

    const [leaderboard, lobbies, status] = await Promise.all([leaderboardFetch, lobbiesFetch, statusFetch]);

    if (!leaderboard || isEmpty(lobbies) || !status) {
      return { leaderboard: null, lobbies: null, status: CompetitorStatus.OBSERVER };
    }

    const lobbiesWithFilteredPlayers = lobbies.map((l) => ({
      ...l,
      players: l.players.filter((p) => p.riotId !== session.user.extRiotId),
    }));
    return { leaderboard, lobbies: lobbiesWithFilteredPlayers, status };
  } catch (error) {
    throw new Error('Error fetching leaderboard data.');
  }
}
