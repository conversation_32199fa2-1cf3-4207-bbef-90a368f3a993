import { User } from 'next-auth';

import { LobbyData } from '@/app/[locale]/event-schedule/_components/LobbyList/types';
import { CompetitorStatus, LeaderboardData } from '../_components/types';

// import 'server-only';

// if (!process.env.FORGE_API_URL) {
//   throw new Error('Missing env var FORGE_API_URL!');
// }

const leaderboardUrl = `${process.env.NEXT_PUBLIC_FORGE_API_URL}/leaderboard`;
const lobbiesUrl = `${process.env.NEXT_PUBLIC_FORGE_API_URL}/lobbies`;
const players = `${process.env.NEXT_PUBLIC_FORGE_API_URL}/players`;

export async function getData(user?: User) {
  if (!user) {
    return { leaderboard: null, lobbies: null, player: null, status: CompetitorStatus.OBSERVER };
  }

  if (!user.extHasTicket) {
    return { leaderboard: null, lobbies: null, player: null, status: CompetitorStatus.OBSERVER };
  }

  try {
    const riotId = encodeURIComponent(user.extRiotId as string);
    const leaderboardPromise = fetch(`${leaderboardUrl}/${riotId}`);
    const lobbiesPromise = fetch(`${lobbiesUrl}?playerRiotId=${riotId}`);
    const statusPromise = fetch(`${players}/${riotId}/competitor-status`);

    const [leaderboardResponse, matchesResponse, statusResponse] = await Promise.all([
      leaderboardPromise,
      lobbiesPromise,
      statusPromise,
    ]);

    if (!leaderboardResponse.ok || !matchesResponse.ok || !statusResponse.ok) {
      console.log('Error fetching dashboard data');
      console.log('response', leaderboardResponse, matchesResponse, statusResponse);

      return { leaderboard: null, lobbies: null, player: null, status: CompetitorStatus.OBSERVER };
    }

    const lobbies = (await matchesResponse.json()) as LobbyData[];
    const lobbiesWithFilteredPlayers = lobbies.map((l) => ({
      ...l,
      players: l.players.filter((p) => p.riotId !== user.extRiotId),
    }));

    const leaderboard = (await leaderboardResponse.json()) as LeaderboardData;
    const status = (await statusResponse.json()) as CompetitorStatus;

    return { leaderboard, lobbies: lobbiesWithFilteredPlayers, status };
  } catch (error) {
    throw new Error('Error fetching leaderboard data.');
  }
}
