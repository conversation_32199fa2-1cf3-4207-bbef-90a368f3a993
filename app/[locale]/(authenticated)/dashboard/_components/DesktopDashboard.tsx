import { LobbyData } from '@/app/[locale]/event-schedule/_components/LobbyList/types';
import { Container } from '@/components';
import { DashboardLocaleData } from '@/types/strapi';
import { Box, Button, FlexLayout, Image, Text } from '@/ui';
import { Description } from './Description';
import { Leaderboard } from './Leaderboard';
import { MatchesTimeline } from './MatchesTimeline';
import { MobileQrCode } from './MobileQrCode';
// import { ProfileCompleteStatus } from './ProfileCompleteStatus';
import { TournamentRealmLogin } from './TournamentRealmLogin';
import { CompetitorStatus, LeaderboardData } from './types';

export interface DashboardComponentProps {
  localeData: DashboardLocaleData;
  leaderboardData: LeaderboardData | null;
  lobbiesData: LobbyData[] | null;
  status: CompetitorStatus;
}

export const DesktopDashboard = ({ localeData, leaderboardData, lobbiesData, status }: DashboardComponentProps) => {
  const hasCompetingData = !!(leaderboardData && lobbiesData);

  return (
    <Box
      sx={{
        display: ['none', 'none', 'flex'],
        backgroundColor: 'midnight900',
        backgroundImage: 'url(/images/dashboardBackground.png)',
        backgroundRepeat: 'no-repeat',
        backgroundSize: '100%',
        backgroundPosition: ['center -72px', 'center -72px', 'center -98px'],
        flexGrow: 1,
      }}
    >
      <Container>
        <FlexLayout justifyContent="center" space={20} my="70px" sx={{ width: '100%' }}>
          <FlexLayout flexDirection="column" sx={{ width: '808px', minWidth: '808px' }}>
            <FlexLayout justifyContent="space-between" alignItems="center">
              <Text color="primary-midnight" textVariant={['h7', 'h5', 'h4']}>
                {localeData.title}
              </Text>
              <Button label={localeData.settingsCTA} as="a" href="/settings" size="medium" />
            </FlexLayout>
            {/* <ProfileCompleteStatus /> */}
            <Description localeData={localeData} competitorStatus={status} />
            {hasCompetingData && <MatchesTimeline dashboardData={localeData} lobbiesData={lobbiesData} />}
          </FlexLayout>
          <FlexLayout sx={{ width: '392px', minWidth: '392px' }}>
            <Box sx={{ position: 'relative', width: 0, zIndex: 1 }}>
              <Image
                src="/images/gloop.png"
                alt="dashboard hero image"
                sx={{
                  width: 420,
                  minWidth: 420,
                  height: 400,
                  minHeight: 400,
                  position: 'absolute',
                  left: '-30px',
                  top: '-50px',
                }}
              />
            </Box>
            <FlexLayout
              flexDirection="column"
              backgroundColor="white"
              space={14}
              mt={268}
              py={6}
              px={4}
              sx={{ backdropFilter: 'blur(4px)', zIndex: 2, height: 'fit-content' }}
            >
              <Leaderboard localeData={localeData} data={leaderboardData} />
              <TournamentRealmLogin localeData={localeData} />
              <MobileQrCode localeData={localeData} isEmptyState={!hasCompetingData} />
            </FlexLayout>
          </FlexLayout>
        </FlexLayout>
      </Container>
    </Box>
  );
};
