import { DashboardLocaleData } from '@/types/strapi';
import { Button, FlexLayout, GridLayout, Text } from '@/ui';
import { LeaderboardData } from './types';

interface DashboardLeaderboardProps {
  localeData: DashboardLocaleData;
  data: LeaderboardData | null;
}

export const Leaderboard = ({ localeData, data }: DashboardLeaderboardProps) => {
  const isCompetingDataAvailable = !!data;
  return (
    <FlexLayout flexDirection="column" sx={{ maxWidth: '336px', zIndex: 2 }} space={6}>
      <FlexLayout space={4} flexDirection="column">
        <Text textVariant={['h7', 'h6']} color="primary-midnight">
          {localeData.leaderboardsTitle}
        </Text>
        <Text textVariant={['paragraph-xs-medium', 'paragraph-s-medium']} color="midnight80">
          {localeData.leaderboardsDesc}
        </Text>
      </FlexLayout>
      <GridLayout gridTemplateColumns="repeat(3, 1fr)" gap={2}>
        <FlexLayout justifyContent="space-between" flexDirection="column" space={2}>
          <Text textVariant="paragraph-xs-medium" color="primary-midnight">
            {localeData.leaderboardPts}
          </Text>
          <Text textVariant="h8" color="primary-midnight">
            {isCompetingDataAvailable ? data.points : '--'}
          </Text>
        </FlexLayout>
        <FlexLayout justifyContent="space-between" flexDirection="column" space={2}>
          <Text textVariant="paragraph-xs-medium" color="primary-midnight">
            {localeData.leaderboardRegRank}
          </Text>
          {/* todo hide until region rank is calculated */}
          {/* <Text textVariant="h6">{isCompetingDataAvailable ? data.regionRank : '--'}</Text> */}
          <Text textVariant="h8" color="primary-midnight">
            {'--'}
          </Text>
        </FlexLayout>
        <FlexLayout justifyContent="space-between" flexDirection="column" space={2}>
          <Text textVariant="paragraph-xs-medium" color="primary-midnight">
            {localeData.leaderboardOverallRank}
          </Text>
          <Text textVariant="h8" color="primary-midnight">
            {isCompetingDataAvailable ? data.overallRank : '--'}
          </Text>
        </FlexLayout>
      </GridLayout>
      <Button
        variant="secondary"
        label={localeData.leaderboardCTA}
        as="a"
        href="/leaderboard"
        size="small"
        isDisabled={!isCompetingDataAvailable}
      />
    </FlexLayout>
  );
};
