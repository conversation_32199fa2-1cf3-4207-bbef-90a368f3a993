'use client';

import { isBefore } from 'date-fns';
import { partition } from 'lodash';
import { useState } from 'react';

import { LobbyData } from '@/app/[locale]/event-schedule/_components/LobbyList/types';
import { DashboardLocaleData } from '@/types/strapi';
import { FlexLayout, Link, Tab, Text, useScreenType } from '@/ui';
import { Match } from './Match';

interface MatchesTimelineProps {
  dashboardData: DashboardLocaleData;
  lobbiesData: LobbyData[];
}

export const MatchesTimeline = ({ dashboardData, lobbiesData }: MatchesTimelineProps) => {
  const [selectedMatchesTab, setSelectedMatchesTab] = useState(dashboardData.upcomingTab);
  const { isMobile } = useScreenType();

  const [previousMatches, upcomingMatches] = partition(lobbiesData, (l) => isBefore(l.startTime, Date.now()));
  const matches = selectedMatchesTab === dashboardData.previousTab ? previousMatches : upcomingMatches;
  return (
    <FlexLayout
      flexDirection="column"
      backgroundColor={['none', 'midnight40', 'midnight40']}
      mt={8}
      py={8}
      px={[0, 6, 6]}
      space={8}
    >
      <FlexLayout justifyContent="center">
        <Tab
          isSelected={selectedMatchesTab === dashboardData.upcomingTab}
          label={dashboardData.upcomingTab}
          onClick={() => setSelectedMatchesTab(dashboardData.upcomingTab)}
          size={isMobile ? 'small' : 'large'}
        />
        <Tab
          isSelected={selectedMatchesTab === dashboardData.previousTab}
          label={dashboardData.previousTab}
          onClick={() => setSelectedMatchesTab(dashboardData.previousTab)}
          size={isMobile ? 'small' : 'large'}
        />
      </FlexLayout>
      <FlexLayout flexDirection="column" space={8}>
        {matches.map((m) => (
          <Match dashboardData={dashboardData} data={m} key={m.id} />
        ))}
      </FlexLayout>
      <FlexLayout
        justifyContent="center"
        flexDirection={['column', 'row', 'row']}
        alignItems="center"
        p={3}
        space={1}
        backgroundColor={isMobile ? 'white10' : 'none'}
      >
        <Text color="midnight80" textVariant="paragraph-xs-regular">
          {dashboardData.matchesFooter}
        </Text>
        <Link as="a" href={`mailto:${dashboardData.supportMail}`} color="linkColor2">
          <Text color="midnight80" textVariant="paragraph-xs-regular">
            {dashboardData.supportMail}
          </Text>
        </Link>
      </FlexLayout>
    </FlexLayout>
  );
};
