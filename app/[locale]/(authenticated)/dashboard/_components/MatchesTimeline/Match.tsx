'use client';

import { formatInTimeZone } from 'date-fns-tz';

import { LobbyData } from '@/app/[locale]/event-schedule/_components/LobbyList/types';
import { DashboardLocaleData } from '@/types/strapi';
import { Box, FlexLayout, Icon, Text, Tooltip, useScreenType } from '@/ui';
import { CEST_TIMEZONE_OFFSET, ET_TIMEZONE_OFFSET, macaoFormat, PT_TIMEZONE_OFFSET } from '@/utils';

interface Props {
  dashboardData: DashboardLocaleData;
  data: LobbyData;
}

export const Match = ({ dashboardData, data }: Props) => {
  const { isMobile } = useScreenType();
  return (
    <FlexLayout flexDirection="column" space={4}>
      <FlexLayout
        alignItems={['start', 'center', 'center']}
        justifyContent={['normal', 'space-between', 'space-between']}
        flexDirection={['column', 'row', 'row']}
        space={2}
      >
        <Text upperCase textVariant="h4">
          {data.round}
        </Text>
        <FlexLayout alignItems="center" space={2}>
          <Icon icon="info" color="midnight100" size="xs" />
          <Text textVariant="paragraph-xs-regular" color="midnight100">
            {dashboardData.matchEstimatesInfo}
          </Text>
        </FlexLayout>
      </FlexLayout>
      <FlexLayout backgroundColor="white10" px={8} py={6}>
        <FlexLayout flexDirection="column" space={6} sx={{ width: '100%' }}>
          <FlexLayout space={12}>
            <FlexLayout flexDirection="column">
              <Text upperCase textVariant="paragraph-xs-medium" color="white70">
                {dashboardData.matchDate}
              </Text>
              <Text upperCase textVariant="paragraph-s-medium">
                {macaoFormat(data.startTime, 'MMM dd')}
              </Text>
            </FlexLayout>
            <FlexLayout flexDirection="column">
              <Text upperCase textVariant="paragraph-xs-medium" color="white70">
                {dashboardData.matchTime}
              </Text>
              <FlexLayout sx={{ gap: '6px' }} alignItems={'center'}>
                <Text upperCase textVariant="paragraph-s-medium">
                  {macaoFormat(data.startTime, 'hh:mm a')}
                </Text>
                <Tooltip
                  content={
                    <Box p={6} pt={4} bg="white">
                      <FlexLayout flexDirection={'column'} space={3} alignItems={'center'}>
                        <Text color="midnight900" variant="paragraph-m-medium">
                          Other Time Zones
                        </Text>
                        <FlexLayout flexDirection={['column', 'row']} space={[0, 1]}>
                          <Text color="midnight900" variant="paragraph-s-bold">
                            {formatInTimeZone(data.startTime, PT_TIMEZONE_OFFSET, 'h:mm a (eee)')} PT {!isMobile && '/'}
                          </Text>
                          <Text color="midnight900" variant="paragraph-s-bold">
                            {formatInTimeZone(data.startTime, ET_TIMEZONE_OFFSET, 'h:mm a (eee)')} ET {!isMobile && '/'}
                          </Text>
                          <Text color="midnight900" variant="paragraph-s-bold">
                            {formatInTimeZone(data.startTime, CEST_TIMEZONE_OFFSET, 'h:mm a (eee)')} CEST
                          </Text>
                        </FlexLayout>
                      </FlexLayout>
                    </Box>
                  }
                  children={<Icon color="white" size="xs" icon={'info'} />}
                  arrowColor={'white'}
                />
              </FlexLayout>
            </FlexLayout>
            <FlexLayout flexDirection="column">
              <Text upperCase textVariant="paragraph-xs-medium" color="white70">
                {dashboardData.matchStation}
              </Text>
              <Text upperCase textVariant="paragraph-s-medium">
                {data.station}
              </Text>
            </FlexLayout>
          </FlexLayout>
          <Box backgroundColor="white10" sx={{ height: '1px', width: '100%' }} />
          <FlexLayout
            space={[2, 6, 6]}
            alignItems={['center', 'normal', 'normal']}
            flexDirection={['column', 'row', 'row']}
          >
            <Text textVariant="paragraph-m-medium" color="purpleLavander500" upperCase>
              {dashboardData.matchYou}
            </Text>
            <Text textVariant="paragraph-s-medium" color="white50" upperCase>
              {dashboardData.matchVs}
            </Text>
            <FlexLayout sx={{ maxWidth: '453px' }} flexWrap="wrap" justifyContent={['center', 'normal', 'normal']}>
              {data.players.map((player, index, arr) => (
                <FlexLayout alignItems="center" key={player.id}>
                  <Text textVariant="paragraph-m-medium" upperCase>
                    {player.name}
                  </Text>
                  {index !== arr.length - 1 && (
                    <Box backgroundColor="white20" mx={2} sx={{ height: '4px', width: '10px', borderRadius: '20px' }} />
                  )}
                </FlexLayout>
              ))}
            </FlexLayout>
          </FlexLayout>
        </FlexLayout>
      </FlexLayout>
    </FlexLayout>
  );
};
