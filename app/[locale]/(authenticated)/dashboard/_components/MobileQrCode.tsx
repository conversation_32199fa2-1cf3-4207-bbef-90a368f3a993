import { DashboardLocaleData } from '@/types/strapi';
import { FlexLayout, Image, Text } from '@/ui';

export interface MobileQrCodeProps {
  localeData: DashboardLocaleData;
  isEmptyState: boolean;
}

export const MobileQrCode = ({ localeData, isEmptyState }: MobileQrCodeProps) => {
  //todo disable temporarily until QR code is decided on
  return null;

  return (
    <FlexLayout flexDirection="column" space={6} alignItems="center">
      <Text textVariant="h7" color="primary-midnight" sx={{ textAlign: 'center' }}>
        {localeData.checkInTitle}
      </Text>
      {isEmptyState ? (
        <Text textVariant="paragraph-xs-medium" color="midnight80">
          {localeData.emptyStateCheckIn}
        </Text>
      ) : (
        <Image
          src="/images/qrCode.png"
          alt="qr code"
          sx={{
            width: 244,
            minWidth: 244,
            height: 244,
            minHeight: 244,
          }}
        />
      )}
    </FlexLayout>
  );
};
