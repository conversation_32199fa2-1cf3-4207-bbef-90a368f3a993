'use client';

import { useSession } from 'next-auth/react';
import { QueryClient, QueryClientProvider, useQuery } from '@tanstack/react-query';

import { getDashboardLocales } from '@/data/dashboard';
import { getData } from '../_utils/getData';
import { DesktopDashboard } from './DesktopDashboard';
import { TabletMobileDashboard } from './TabletMobileDashboard';
import { CompetitorStatus } from './types';

const queryClient = new QueryClient();

interface Props {
  localeData: ReturnType<typeof getDashboardLocales>;
}

export function DashboardData({ localeData }: Props) {
  return (
    <QueryClientProvider client={queryClient}>
      <DataComponent localeData={localeData} />
    </QueryClientProvider>
  );
}

export function DataComponent({ localeData }: Props) {
  const { data: session } = useSession();
  const { data } = useQuery({
    queryFn: () => getData(session?.user),
    queryKey: ['dashboard', session?.user.extRiotId],
    enabled: !!session?.user.extRiotId,
  });

  const props = {
    localeData,
    lobbiesData: data?.lobbies ?? null,
    leaderboardData: data?.leaderboard ?? null,
    status: data?.status ?? CompetitorStatus.OBSERVER,
  };

  return (
    <>
      <DesktopDashboard {...props} />
      <TabletMobileDashboard {...props} />
    </>
  );
}
