import { Container } from '@/components';
import { Box, Button, FlexLayout, Text } from '@/ui';
import { Description } from './Description';
import { DashboardComponentProps } from './DesktopDashboard';
import { Leaderboard } from './Leaderboard';
import { MatchesTimeline } from './MatchesTimeline';
import { MobileQrCode } from './MobileQrCode';
// import { ProfileCompleteStatus } from './ProfileCompleteStatus';
import { TournamentRealmLogin } from './TournamentRealmLogin';

export const TabletMobileDashboard = ({
  localeData: dashboardData,
  leaderboardData,
  lobbiesData,
  status,
}: DashboardComponentProps) => {
  const hasCompetingData = !!(leaderboardData && lobbiesData);

  return (
    <Box
      sx={{
        display: ['flex', 'flex', 'none'],
        backgroundColor: 'secondary-lemonade',
        backgroundImage: 'url(/images/dashboardBackground.png)',
        backgroundRepeat: 'no-repeat',
        backgroundSize: 'cover',
      }}
    >
      <Container>
        <FlexLayout justifyContent="center" sx={{ width: '100%' }}>
          <FlexLayout
            alignItems="center"
            flexDirection="column"
            justifyContent="center"
            mt={[4, 8, 0]}
            mx={[0, 6, 0]}
            mb={'117px'}
          >
            <FlexLayout
              px={[4, 'inherit']}
              justifyContent="space-between"
              flexDirection={['column', 'row', 'row']}
              alignItems={['start', 'center', 'center']}
              space={3}
              sx={{ width: '100%' }}
            >
              <Text color="primary-midnight" textVariant={['h7', 'h5']}>
                {dashboardData.title}
              </Text>
              <Button label={dashboardData.settingsCTA} as="a" href="/settings" size="small" />
            </FlexLayout>
            {/* <ProfileCompleteStatus /> */}
            <Box sx={{ width: '100%' }}>
              <Description localeData={dashboardData} competitorStatus={status} />
            </Box>
            <FlexLayout flexDirection="column" space={6} alignItems="center" sx={{ width: '100%' }}>
              <FlexLayout
                alignItems="center"
                backgroundColor="white"
                flexDirection="column"
                mt={[10, 8, 0]}
                sx={{ width: '100%' }}
                py={[8, 6, 6]}
                px={[8, 6, 6]}
                space={12}
              >
                <FlexLayout
                  justifyContent="space-between"
                  flexGrow={1}
                  flexDirection={['column', 'column', 'row']}
                  sx={{ width: '100%' }}
                  space={[12, 6, 0]}
                >
                  <FlexLayout flexBasis={0} flexGrow={1}>
                    <Leaderboard localeData={dashboardData} data={leaderboardData} />
                  </FlexLayout>
                  <FlexLayout sx={{ display: ['none', 'flex', 'none'] }}>
                    <Box backgroundColor="white20" sx={{ width: '1px' }} />
                  </FlexLayout>
                  <FlexLayout flexBasis={0} flexGrow={1}>
                    <TournamentRealmLogin localeData={dashboardData} />
                  </FlexLayout>
                </FlexLayout>
                <FlexLayout sx={{ display: ['none', 'flex', 'none'] }}>
                  <MobileQrCode localeData={dashboardData} isEmptyState={!hasCompetingData} />
                </FlexLayout>
              </FlexLayout>
              {/* TODO disable temporarily until QR code is decided on */}
              {/* {hasCompetingData && (
                <>
                  <Box
                    backgroundColor="white20"
                    sx={{ height: '1px', width: '100%', display: ['block', 'none', 'none'] }}
                  />
                  <FlexLayout
                    sx={{ display: ['flex', 'none', 'none'], width: '100%' }}
                    p={6}
                    justifyContent={'center'}
                    backgroundColor="midnight40"
                  >
                    <MobileQrCode localeData={dashboardData} isEmptyState={!hasCompetingData} />
                  </FlexLayout>
                </>
              )} */}
            </FlexLayout>
            {hasCompetingData && (
              <Box sx={{ width: '100%' }}>
                <MatchesTimeline dashboardData={dashboardData} lobbiesData={lobbiesData} />
              </Box>
            )}
          </FlexLayout>
        </FlexLayout>
      </Container>
    </Box>
  );
};
