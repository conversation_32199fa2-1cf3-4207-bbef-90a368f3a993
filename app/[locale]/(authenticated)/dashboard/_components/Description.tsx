import { DashboardLocaleData } from '@/types/strapi';
import { Box, FlexLayout, Link, Text, TextProps } from '@/ui';
import { CompetitorStatus } from './types';

const titleVariant = ['h7', 'h6'] as TextProps['textVariant'];
const descVariant = ['paragraph-s-medium', 'paragraph-m-medium'] as TextProps['textVariant'];

interface Props {
  localeData: DashboardLocaleData;
  competitorStatus: CompetitorStatus;
}

export const Description = ({ localeData, competitorStatus }: Props) => {
  switch (competitorStatus) {
    case CompetitorStatus.OBSERVER:
      return <EmptyStateDesc dashboardData={localeData} />;
    case CompetitorStatus.OUT_OF_COMPETITION:
      return <OutOfCompetitionDesc dashboardData={localeData} />;
    default:
      return (
        <Box backgroundColor="white" mt={8} py={4} px={[10, 6, 6]}>
          <Text textVariant={descVariant} color="primary-midnight">
            {localeData.desc}
          </Text>
          <Link as="a" href={`mailto:${localeData.supportMail}`} color="linkColor2">
            <Text color="midnight60" textVariant="paragraph-s-medium">
              {localeData.supportMail}
            </Text>
          </Link>
        </Box>
      );
  }
};

const OutOfCompetitionDesc = ({ dashboardData }: { dashboardData: DashboardLocaleData }) => {
  return (
    <FlexLayout flexDirection="column" alignItems="center" backgroundColor="white" mt={8} py={6} px={[10, 6, 6]}>
      <Text textVariant={titleVariant} color="primary-midnight">
        {dashboardData.outOfCompetitionTitle}
      </Text>
      <Text textVariant={descVariant} isCentered color="midnight60" mt={4}>
        {dashboardData.outOfCompetitionDesc}
      </Text>
      {/* <Button variant="tertiary" mt={6} label={dashboardData.scheduleCTA} as="a" href="/event-schedule" size="small" /> */}
    </FlexLayout>
  );
};

const EmptyStateDesc = ({ dashboardData }: { dashboardData: DashboardLocaleData }) => {
  return (
    <FlexLayout
      flexDirection="column"
      alignItems="center"
      backgroundColor="white"
      mt={[10, 12, 8]}
      py={20}
      px={[10, 6, 6]}
    >
      <Text textVariant={titleVariant} sx={{ textTransform: 'uppercase' }} isCentered color="primary-midnight">
        {dashboardData.emptyStateDescTitle}
      </Text>
      <Text textVariant={descVariant} isCentered mt={4} color="midnight60">
        {dashboardData.emptyStateDesc}
      </Text>
      {/* <Button variant="tertiary" mt={6} label={dashboardData.scheduleCTA} as="a" href="/event-schedule" size="small" /> */}
    </FlexLayout>
  );
};
