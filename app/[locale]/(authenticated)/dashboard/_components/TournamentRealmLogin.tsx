'use client';

import { useSession } from 'next-auth/react';
import { useState } from 'react';

import { DashboardLocaleData } from '@/types/strapi';
import { FlexLayout, Icon, Text } from '@/ui';

interface DashboardTournamentRealmLoginProps {
  localeData: DashboardLocaleData;
}

export function TournamentRealmLogin({ localeData }: DashboardTournamentRealmLoginProps) {
  const session = useSession();
  const [isPasswordHidden, setIsPasswordHidden] = useState(true);

  const username = session.data?.user.extRealmUsername as string;
  const password = session.data?.user.extRealmPassword as string;
  const isEmptyState = !username && !password;

  return (
    <FlexLayout flexDirection="column" space={4}>
      <Text textVariant={['h7', 'h6']} color="primary-midnight">
        {localeData.realmLoginTitle}
      </Text>
      {isEmptyState ? (
        <Text textVariant={['paragraph-xs-medium', 'paragraph-s-medium']} color="midnight80">
          {localeData.emptyStateRealmLogin}
        </Text>
      ) : (
        <>
          <Text textVariant="paragraph-m-medium" color="primary-midnight">
            {localeData.realmLoginUsername}: {username}
          </Text>
          <FlexLayout alignItems="center" justifyContent="space-between" mt={4} sx={{ width: '80%' }}>
            <Text textVariant="paragraph-m-medium" color="primary-midnight">
              {localeData.realmLoginPassword}: {isPasswordHidden ? '•'.repeat(password.length) : password}
            </Text>
            <Icon
              icon={isPasswordHidden ? 'eyeOn' : 'eyeOff'}
              color="midnight80"
              sx={{
                cursor: 'pointer',
                ':hover': {
                  color: '#C597E7',
                },
              }}
              onClick={() => setIsPasswordHidden(!isPasswordHidden)}
            />
          </FlexLayout>
        </>
      )}
    </FlexLayout>
  );
}
