'use client';

import { useSiteConfig } from '@/context/SiteConfig';
import { Box, Button, ButtonProps, FlexLayout, Text, Tooltip, useScreenType } from '@/ui';
import { useProfileCompletedSteps } from './hooks';

export const ProfileCompleteStatus = () => {
  const { localeJSON, featureFlags } = useSiteConfig();
  const { isMobile } = useScreenType();

  const { numberOfSteps, completedSteps, nextStep } = useProfileCompletedSteps();

  if (!featureFlags.isProfileProgressEnabled) {
    return null;
  }

  if (completedSteps >= numberOfSteps) {
    return null;
  }

  return (
    <FlexLayout
      mt={12}
      flexDirection="column"
      space={[6, 5]}
      px={[4, 6]}
      py={4}
      backgroundColor="primary-denim"
      sx={{ width: '100%' }}
    >
      <FlexLayout justifyContent="space-between" alignItems="center">
        <FlexLayout space={2} flexDirection="column">
          <Text textVariant={['paragraph-s-medium', 'paragraph-m-medium', 'paragraph-l-medium']}>
            {completedSteps * 20}
            {localeJSON['dashboardProfileIncomplete']}
          </Text>
          <Text textVariant={['paragraph-xs-regular', 'paragraph-s-regular', 'paragraph-m-regular']} color="white70">
            {nextStep?.text}
          </Text>
        </FlexLayout>
        {!isMobile && nextStep && <CompleteProfileButton {...nextStep.ctaProps} />}
      </FlexLayout>
      <FlexLayout space={2}>
        {Array.from(Array(numberOfSteps).keys()).map((step) => (
          <Box
            key={step}
            backgroundColor={step < completedSteps ? 'primary-macaron' : 'primary-clouds'}
            sx={{ height: '8px', flexGrow: 1, borderRadius: 24 }}
          />
        ))}
      </FlexLayout>
      {isMobile && nextStep && <CompleteProfileButton isFullWidth {...nextStep.ctaProps} />}
    </FlexLayout>
  );
};

type CompleteButtonProps = ButtonProps & { isFullWidth?: boolean; tooltipContent?: string };

const CompleteProfileButton = ({ isFullWidth, tooltipContent, ...rest }: CompleteButtonProps) => {
  const ButtonComponent = (
    <Button variant="secondary" size="small" {...rest} {...(isFullWidth && { sx: { width: '100%' } })} />
  );

  return tooltipContent ? (
    <Tooltip
      arrowColor="#E5FFCA"
      content={
        <Box p={2} backgroundColor="paleYellow500" sx={{ maxWidth: 256, textAlign: 'center' }}>
          <Text variant="label-s-countdown" color="midnight900" sx={{ lineHeight: '18px' }}>
            {tooltipContent}
          </Text>
        </Box>
      }
    >
      {ButtonComponent}
    </Tooltip>
  ) : (
    ButtonComponent
  );
};
