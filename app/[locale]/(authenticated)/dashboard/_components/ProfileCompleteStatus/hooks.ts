import { useSession } from 'next-auth/react';

import { updateUser } from '@/apis/user';
import { useDropInfo } from '@/context/DropInfoProvider';
import { useSiteConfig } from '@/context/SiteConfig';
import { ButtonProps } from '@/ui';

interface NextStep {
  text: string;
  ctaProps: ButtonProps & { tooltipContent?: string };
}

export function useProfileCompletedSteps() {
  const { localeJSON } = useSiteConfig();
  const { data: session, update: updateSession } = useSession();
  const dropInfo = useDropInfo();

  let completedSteps = 0;
  let nextStep: NextStep | null = null;

  async function updateKbygRead() {
    await updateUser(session?.user.id as string, { kbygRead: true });
    await updateSession();
  }

  if (session?.user.extKbygRead) {
    completedSteps++;
  } else {
    nextStep = {
      text: localeJSON['dashboardKbygDesc'],
      ctaProps: {
        label: localeJSON['dashboardKbygCTA'],
        onClick: updateKbygRead,
      },
    };
  }

  if (session?.user.extQuestionnaire) {
    completedSteps++;
  } else {
    nextStep = {
      text: localeJSON['dashboardProfileIncompleteDesc'],
      ctaProps: {
        as: 'a',
        label: localeJSON['dashboardProfileIncompleteCTA'],
        href: '/complete-profile',
      },
    };
  }

  //todo add datetime & ranks logic
  if (session?.user.extHasTicket) {
    completedSteps++;
  } else {
    nextStep = {
      text: localeJSON['dashboardPurchaseTicketsDesc'],
      ctaProps: {
        as: 'a',
        label: localeJSON['dashboardPurchaseTicketsCTA'],
        href: '/competitor-bundle',
        isDisabled: !dropInfo.user?.saleStartedForRank,
      },
    };
  }

  if (session?.user.extRulesAccepted) {
    completedSteps++;
  } else {
    nextStep = {
      text: localeJSON['dashboardReadRulesDesc'],
      ctaProps: {
        as: 'a',
        label: localeJSON['dashboardReadRulesCTA'],
        href: '/competitor-bundle',
        isDisabled: !dropInfo.user?.saleStartedForRank,
      },
    };
  }

  if (session?.user.extRegistred) {
    completedSteps++;
  }

  return { numberOfSteps: 5, completedSteps, nextStep };
}
