import { Session } from 'next-auth';

import { ModalType } from '@/components/LegalDocumentsModals';
import { DropInfoProviderProps } from '@/context/DropInfoProvider';
import { TicketPurchaseState } from '@/enums/drops';
import { PurchaseView } from './PurchaseFlow/types';

export function getCurrentView(dropInfo: DropInfoProviderProps) {
  if (!dropInfo || !dropInfo.general || !dropInfo.user) {
    return PurchaseView.Error;
  }

  if (dropInfo.user.ticket?.purchased) {
    return PurchaseView.Completed;
  }

  if (dropInfo.general.ticketFlags.soldOut) {
    return PurchaseView.SoldOut;
  }

  if (dropInfo.general?.ticketFlags.waiting && dropInfo.user?.state !== TicketPurchaseState.Reserved) {
    return PurchaseView.SoldOut;
  }

  if (!dropInfo.user?.saleStartedForRank) {
    return PurchaseView.Error;
  }

  return PurchaseView.Pending;
}

export function getCurrentModal(session: Session | null): ModalType | null {
  if (session?.user.extRulesAccepted) {
    return null;
  }

  return ModalType.OfficialRules;
}
