import { RulesModal } from '@/components/shared/RulesModal';
import { WelcomeBoxData } from '@/components/WelcomeBox/types';
import { CompetitorBundleInfo, CompetitorBundleStrings, PurchaseErrorInfo } from '@/interfaces';
import { PurchaseInfoProps } from '@/types/strapi';

export interface PurchaseFlowProps {
  purchaseInfo: PurchaseInfoProps;
  purchaseCompletedInfo: WelcomeBoxData;
  purchaseErrorInfo: PurchaseErrorInfo;
  purchaseTimeoutInfo: PurchaseErrorInfo;
  purchaseSoldOutInfo: PurchaseErrorInfo;
  competitorBundleInfo: CompetitorBundleInfo;
  modals: RulesModal[];
  strings: CompetitorBundleStrings;
}

export enum PurchaseView {
  Timeout = 'Timeout',
  Error = 'Error',
  SoldOut = 'SoldOut',
  Completed = 'Completed',
  Pending = 'Pending',
}
