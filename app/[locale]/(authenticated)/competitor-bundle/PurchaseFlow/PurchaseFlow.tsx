'use client';

import { useRouter } from 'next/navigation';
import { useSession } from 'next-auth/react';
import { useEffect, useState } from 'react';

import { acquireTicket as acquireTicketRequest } from '@/apis/drops';
import { updateUser } from '@/apis/user';
import { ModalType } from '@/components/LegalDocumentsModals';
import { RulesModal } from '@/components/shared/RulesModal';
import { useDropInfo } from '@/context/DropInfoProvider';
import { TicketPurchaseState } from '@/enums/drops';
import { Box } from '@/ui';
import WelcomeBox from '../../../../../components/WelcomeBox/WelcomeBox';
import { getCurrentModal, getCurrentView } from '../utils';
import { PurchaseFlowProps, PurchaseView } from './types';
import PurchaseError from './views/PurchaseError';
import PurchasePending from './views/PurchasePending';
import PurchaseTimeout from './views/PurchaseTimeout';

export function PurchaseFlow(props: PurchaseFlowProps) {
  const dropInfo = useDropInfo();
  const router = useRouter();
  const { data: session } = useSession();
  const [view, setView] = useState<PurchaseView>(() => getCurrentView(dropInfo));
  const [modal, setModal] = useState<ModalType | null>(() => getCurrentModal(session));
  const [ticketAcquired, setTicketAcquired] = useState(false);

  const [officialRulesModal, participationAgreementModal] = props.modals;

  useEffect(() => {
    // If the rules have already been accepted, acquire the ticket immediately.
    // Otherwise, the ticket should be acquired in the onRulesAccepted callback.
    if (session?.user.extRulesAccepted) acquireTicket();
  }, []);

  useEffect(() => {
    setView(getCurrentView(dropInfo));
  }, [dropInfo]);

  async function acquireTicket() {
    try {
      if (!dropInfo.general) {
        throw new Error('Failed to resolve drop info');
      }

      // Avoid attempting to acqurie a ticket if ticket is already purchased
      if (dropInfo.user?.ticket?.purchased) {
        return;
      }

      // Avoid attempting to acquire a ticket if all tickets are sold out
      if (dropInfo.general.ticketFlags.soldOut) {
        return;
      }

      // Avoid attempting to acquire a ticket if all tickets are reserved (unless it's reserved by current user)
      if (dropInfo.general?.ticketFlags.waiting && dropInfo.user?.state !== TicketPurchaseState.Reserved) {
        return;
      }

      await acquireTicketRequest(dropInfo.general.dropId);

      setTicketAcquired(true);

      // The `router.refresh()` call ensures dropInfo reloads correctly
      // after acquiring a ticket. The client will merge the updated RSC
      // payload without losing unaffected client-side React.
      router.refresh();
    } catch (err) {
      console.error('Failed to acquire a ticket', err);

      setView(PurchaseView.Error);
    }
  }

  async function onRulesAccepted() {
    try {
      if (!session) {
        throw new Error('Failed to resolve session');
      }

      // Hide the modal right away, assuming the request will succeed
      setModal(null);

      await updateUser(session.user.id, { rulesAccepted: true });

      await acquireTicket();
    } catch (err) {
      console.error('Failed to set rules as accepted', err);

      setView(PurchaseView.Error);
    }
  }

  function onRulesDeclined() {
    return router.push('/');
  }

  return (
    <Box sx={{ position: 'relative' }} mt={[8, 12, 20]} mb={30} mx={[4, 6, 20]}>
      {
        {
          [PurchaseView.Pending]: <PurchasePending data={props} ticketAcquired={ticketAcquired} setView={setView} />,
          [PurchaseView.Error]: <PurchaseError data={props.purchaseErrorInfo} />,
          [PurchaseView.SoldOut]: <PurchaseError data={props.purchaseSoldOutInfo} />,
          [PurchaseView.Timeout]: <PurchaseTimeout data={props.purchaseTimeoutInfo} />,
          [PurchaseView.Completed]: <WelcomeBox data={props.purchaseCompletedInfo} />,
        }[view]
      }
      {modal === ModalType.OfficialRules && (
        <RulesModal
          modalData={officialRulesModal}
          onModalAccept={() => setModal(ModalType.ParticipationAgreement)}
          onModalDecline={onRulesDeclined}
        />
      )}
      {modal === ModalType.ParticipationAgreement && (
        <RulesModal
          modalData={participationAgreementModal}
          onModalAccept={onRulesAccepted}
          onModalDecline={onRulesDeclined}
        />
      )}
    </Box>
  );
}
