import { PurchaseErrorInfo } from '@/interfaces';
import { Button, FlexLayout, Image, Text } from '@/ui';

interface PurchaseErrorProps {
  data: PurchaseErrorInfo;
}

export default function PurchaseError({ data }: PurchaseErrorProps) {
  return (
    <FlexLayout
      flexDirection="column"
      space={8}
      alignItems="center"
      justifyContent="center"
      backgroundColor="midnight40"
      pt={8}
      pb={12}
      px={6}
      sx={{ backdropFilter: 'blur(7px)', width: '100%' }}
    >
      <FlexLayout flexDirection="column" space={3} alignItems="center" justifyContent="center">
        {data.imageUrl && <Image src={data.imageUrl} sx={{ width: [290, 290, 473], height: [260, 260, 368] }} />}
        <Text textVariant={['h4', 'h4', 'h3']} color="white" sx={{ mt: 4, mb: 1 }} isCentered upperCase>
          {data.title}
        </Text>
        <Text
          textVariant={['paragraph-m-medium', 'paragraph-m-medium', 'paragraph-l-medium']}
          color="white"
          sx={{ maxWidth: 680 }}
          isCentered
        >
          {data.body}
        </Text>
      </FlexLayout>
      <Button label={data.strings.refreshPage} as="a" href="/competitor-bundle" sx={{ textAlign: 'center' }} />
    </FlexLayout>
  );
}
