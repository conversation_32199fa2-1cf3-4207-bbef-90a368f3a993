'use client';

import { useSiteConfig } from '@/context/SiteConfig';
import { useCurrentLocale } from '@/hooks/i18n';
import { Link, Text } from '@/ui';

export default function TermsOfService() {
  const { localeJSON } = useSiteConfig();
  const locale = useCurrentLocale();

  return (
    <Text textVariant="paragraph-s-regular" color="primary500">
      {localeJSON['termsOfServiceByCompletingText']}{' '}
      <Link href="https://assets.esportsengine.co/docs/terms.pdf" target="_blank">
        <Text textVariant="paragraph-s-regular" color="purpleLavander500" sx={{ cursor: 'pointer' }}>
          {localeJSON['bottomNavToS']}
        </Text>
      </Link>{' '}
      {localeJSON['termsOfServiceAsWellAsText']}{' '}
      <Link
        href={
          locale === 'en'
            ? 'https://www.riotgames.com/en/terms-of-service'
            : 'https://www.riotgames.com/fr/terms-of-service-FR'
        }
        target="_blank"
      >
        <Text textVariant="paragraph-s-regular" color="purpleLavander500" sx={{ cursor: 'pointer' }}>
          {localeJSON['bottomNavToS']}
        </Text>
      </Link>
      ,{' '}
      <Link
        href={
          locale === 'en'
            ? 'https://www.riotgames.com/en/privacy-notice'
            : 'https://www.riotgames.com/fr/privacy-notice-FR'
        }
        target="_blank"
      >
        <Text textVariant="paragraph-s-regular" color="purpleLavander500" sx={{ cursor: 'pointer' }}>
          {localeJSON['bottomNavPP']}
        </Text>
      </Link>{' '}
      {localeJSON['and']}{' '}
      <Link
        href={
          locale === 'en' ? 'https://www.riotgames.com/en/cookie-policy' : 'https://www.riotgames.com/fr/cookie-policy'
        }
        target="_blank"
      >
        <Text textVariant="paragraph-s-regular" color="purpleLavander500" sx={{ cursor: 'pointer' }}>
          {localeJSON['cookieManagement']}
        </Text>
      </Link>
    </Text>
  );
}
