import { NoSsr } from '@/components/shared/ NoSsr';
import { useDropInfo } from '@/context/DropInfoProvider';
import { useSiteConfig } from '@/context/SiteConfig';
import useCountdown from '@/hooks/useCountdown';
import { FlexLayout, Icon, Text } from '@/ui';

interface CountdownTimerProps {
  onCountdownEnd?: () => void;
}

const getCountdownLabel = (minutes: number, seconds: number, localeJson: Record<string, string>) => {
  const baseText =
    localeJson['finishPurchaseBaseText'] ??
    'You have {{minutes}} minutes and {{seconds}} seconds to finish your purchase';

  return baseText.replace('{{minutes}}', minutes.toString()).replace('{{seconds}}', seconds.toString());
};

export default function CountdownTimer({ onCountdownEnd }: CountdownTimerProps) {
  const dropInfo = useDropInfo();
  const { localeJSON } = useSiteConfig();

  const { minutes, seconds } = useCountdown({
    untilDate: dropInfo.user?.ticket?.reservedUntil ?? null,
    onCountdownEnd,
  });

  return (
    <FlexLayout
      sx={{ gap: '10px', minHeight: 62, maxHeigt: 62 }}
      alignItems="center"
      px={[4, 6, 6]}
      py={4}
      bg="dreamBlue"
      color="white"
    >
      <Icon icon="clock" />
      <Text textVariant={['paragraph-s-regular', 'paragraph-m-regular', 'paragraph-m-regular']}>
        <NoSsr fallback={getCountdownLabel(0, 0, localeJSON)}>{getCountdownLabel(minutes, seconds, localeJSON)}</NoSsr>
      </Text>
    </FlexLayout>
  );
}
