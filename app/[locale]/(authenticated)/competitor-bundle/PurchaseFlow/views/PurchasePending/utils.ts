import { differenceInMilliseconds } from 'date-fns';

import { DropInfoProviderProps } from '@/context/DropInfoProvider';

export function getInitialTimerMs(dropInfo: DropInfoProviderProps) {
  if (!dropInfo.user?.ticket?.reservedUntil) {
    return 0;
  }

  const differenceMs = differenceInMilliseconds(dropInfo.user?.ticket.reservedUntil, new Date());

  return differenceMs > 0 ? differenceMs : 0;
}
