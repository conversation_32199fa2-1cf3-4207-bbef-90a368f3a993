import { TextWithLines } from '@/components/shared';
import { useDropInfo } from '@/context/DropInfoProvider';
import { Box, colors, FlexLayout, Text } from '@/ui';
import { formatCurrency } from '@/utils/currency';
import { PurchaseFlowProps } from '../../types';

const amountPlaceholder = '$XXX';
const currency = 'USD';

interface PriceBreakdownProps {
  strings: PurchaseFlowProps['strings'];
}

export default function PriceBreakdown({ strings }: PriceBreakdownProps) {
  const dropInfo = useDropInfo();
  const price = dropInfo.general?.currentPeriod?.price ?? 0;
  const tax = dropInfo.general?.currentPeriod?.tax ?? 0;

  return (
    <FlexLayout flexDirection="column" space={6}>
      <TextWithLines text={strings.priceBreakdown} hasLeftLine={false} textVariant="h5" upperCase />
      <FlexLayout space={[4, 6, 6]} flexDirection="column">
        <FlexLayout flexDirection="column" space={4}>
          <PriceRow text={strings.competitorPassTicket} amount={price} currency={currency} />
          {tax > 0 ? <PriceRow text={strings.tax} amount={tax} currency={currency} /> : null}
        </FlexLayout>
        <Box sx={{ width: '100%', height: '1px', backgroundColor: colors.black20 }} />
        <TotalPriceRow text={strings.total} amount={price} currency={currency} />
      </FlexLayout>
    </FlexLayout>
  );
}

function PriceRow({ text, amount, currency }: { text: string; amount?: number; currency?: string }) {
  return (
    <FlexLayout space={1} alignItems="end">
      <Text
        textVariant={['paragraph-s-regular', 'paragraph-m-regular', 'paragraph-m-regular']}
        color="black70"
        sx={{ maxWidth: ['170px', '100%', '100%'] }}
      >
        {text}
      </Text>
      <Text sx={{ flex: 1, overflow: 'hidden' }} color="black20">
        {'.'.repeat(1000)}
      </Text>
      <Text textVariant="paragraph-s-regular" color="black70">
        {typeof amount === 'number' ? formatCurrency(amount, currency) : amountPlaceholder}
      </Text>
    </FlexLayout>
  );
}

function TotalPriceRow({ text, amount, currency }: { text: string; amount?: number; currency?: string }) {
  return (
    <FlexLayout justifyContent="space-between">
      <Text
        textVariant={['paragraph-s-bold', 'paragraph-m-bold', 'paragraph-m-bold']}
        color="black"
        sx={{ maxWidth: ['170px', '100%', '100%'] }}
      >
        {text}
      </Text>
      <Text textVariant="paragraph-s-regular" color="black">
        {typeof amount === 'number' ? formatCurrency(amount, currency) : amountPlaceholder}
      </Text>
    </FlexLayout>
  );
}
