'use client';
import { CompetitorBundleInfo } from '@/interfaces';
import { FlexLayout, Icon, Text, useScreenType } from '@/ui';

export default function PassInfo({ competitorBundleInfoTitle, ...rest }: CompetitorBundleInfo) {
  const { isMobile } = useScreenType();

  const info = Object.values(rest);

  return (
    <FlexLayout
      alignItems="center"
      flexDirection="column"
      space={8}
      p={[4, 6, 6]}
      flexShrink={0}
      sx={{ width: ['100%', '100%', '392px'], minHeight: 0, height: '100%', backdropFilter: 'blur(3px)' }}
      backgroundColor="midnight40"
    >
      <Text upperCase={!isMobile} textVariant={['paragraph-m-bold', 'h4', 'h4']} sx={{ textAlign: 'center' }}>
        {competitorBundleInfoTitle}
      </Text>
      <FlexLayout space={8} flexDirection="column">
        {info.map((item, index) => (
          <FlexLayout space={3} as="li" key={index}>
            <Icon size="s" icon="polygon" mt="2px" color="#AC6CDD" />
            <Text textVariant="paragraph-s-medium" color="white">
              {item}
            </Text>
          </FlexLayout>
        ))}
      </FlexLayout>
    </FlexLayout>
  );
}
