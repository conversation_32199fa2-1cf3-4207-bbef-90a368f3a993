import { useRouter } from 'next/navigation';
import { useRef, useState } from 'react';
import { v4 as uuidv4 } from 'uuid';

import { purchaseTicket } from '@/apis/drops';
import { createPayPalOrder as createPayPalOrderRequest } from '@/apis/drops';
import { PayPalForm } from '@/components/PayPal';
import { TextWithLines } from '@/components/shared';
import SquareForm, { SquareFormHandle } from '@/components/SquareForm';
import { useDropInfo } from '@/context/DropInfoProvider';
import { PaymentProcessor } from '@/enums/payments';
import { useCurrentLocale } from '@/hooks';
import { Box, Button, FlexLayout, Image, LoadingIndicator, Text } from '@/ui';
import { PurchaseFlowProps, PurchaseView } from '../../types';
import CountdownTimer from './CountdownTimer';
import PassInfo from './PassInfo';
import PriceBreakdown from './PriceBreakdown';
import PurchaseHero from './PurchaseHero';
import TermsOfService from './TermsOfService';

const networkDelayAdjustmentMs = 2000;

interface PurchasePendingProps {
  data: PurchaseFlowProps;
  ticketAcquired: boolean;
  setView: (view: PurchaseView) => void;
}

export default function PurchasePending({ data, ticketAcquired, setView }: PurchasePendingProps) {
  const squareFormRef = useRef<SquareFormHandle | null>(null);
  const [isDisabled, setIsDisabled] = useState(false);
  const [isPurchasing, setIsPurchasing] = useState(false);
  const dropInfo = useDropInfo();
  const router = useRouter();
  const [idempotencyKey] = useState(uuidv4());
  const locale = useCurrentLocale();

  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  const createSquarePayment = async (event: any) => {
    try {
      if (isDisabled) return;

      event.preventDefault();

      setIsDisabled(true);

      if (!squareFormRef || !squareFormRef.current) {
        throw new Error('Square form error');
      }

      if (!dropInfo.general) {
        throw new Error('Failed to resolve drop info');
      }

      if (!dropInfo.general.currentPeriod) {
        throw new Error('Failed to resolve current period');
      }

      if (!process.env.NEXT_PUBLIC_SQUARE_LOCATION_ID) {
        throw new Error('Invalid Square Location ID');
      }

      const nonce = await squareFormRef.current.tokenize();
      if (!nonce) {
        console.error('Failed to generate nonce');
        setIsDisabled(false);
        return;
      }

      if (!dropInfo.general?.dropId) return;

      setIsPurchasing(true);
      await purchaseTicket(
        dropInfo.general.dropId,
        {
          processor: PaymentProcessor.Square,
          processorData: {
            sourceId: nonce as string,
            locationId: process.env.NEXT_PUBLIC_SQUARE_LOCATION_ID,
            idempotencyKey: idempotencyKey,
          },
          meta: {
            // For verification on the server side that the purchase amount
            // matches what the user sees.
            price: dropInfo.general.currentPeriod.price,
            tax: dropInfo.general.currentPeriod.tax,
          },
        },
        { locale },
      );

      // The `router.refresh()` call ensures dropInfo reloads correctly
      // after purchasing a ticket. The client will merge the updated RSC
      // payload without losing unaffected client-side React.
      router.refresh();
    } catch (err) {
      console.error('Ticket purchase error', err);

      setIsDisabled(false);

      setView(PurchaseView.Error);
    } finally {
      setIsPurchasing(false);
    }
  };

  async function createPayPalOrder() {
    try {
      if (!dropInfo.general?.dropId) {
        throw new Error('Failed to resolve drop info');
      }

      if (!dropInfo.general.currentPeriod) {
        throw new Error('Failed to resolve current period');
      }

      const order = await createPayPalOrderRequest(dropInfo.general?.dropId, {
        meta: {
          // For verification on the server side that the purchase amount
          // matches what the user sees.
          price: dropInfo.general.currentPeriod.price,
          tax: dropInfo.general.currentPeriod.tax,
        },
      });

      return order?.id;
    } catch (err) {
      console.error('Failed to create paypal order', err);

      setView(PurchaseView.Error);
    }
  }

  async function onApprovePayPalOrder(data: { orderID: string }) {
    try {
      if (isDisabled) return;

      setIsDisabled(true);

      if (!squareFormRef || !squareFormRef.current) {
        throw new Error('Square form error');
      }

      if (!dropInfo.general) {
        throw new Error('Failed to resolve drop info');
      }

      if (!dropInfo.general.currentPeriod) {
        throw new Error('Failed to resolve current period');
      }

      if (!dropInfo.general?.dropId) return;

      setIsPurchasing(true);

      await purchaseTicket(
        dropInfo.general.dropId,
        {
          processor: PaymentProcessor.PayPal,
          processorData: {
            orderId: data.orderID,
          },
          meta: {
            // For verification on the server side that the purchase amount
            // matches what the user sees.
            price: dropInfo.general.currentPeriod.price,
            tax: dropInfo.general.currentPeriod.tax,
          },
        },
        { locale },
      );

      // The `router.refresh()` call ensures dropInfo reloads correctly
      // after purchasing a ticket. The client will merge the updated RSC
      // payload without losing unaffected client-side React.
      router.refresh();
    } catch (err) {
      console.error('Ticket purchase error', err);

      setIsDisabled(false);

      setView(PurchaseView.Error);
    } finally {
      setIsPurchasing(false);
    }
  }

  function onCountdownEnd() {
    if (isPurchasing) return;

    setTimeout(() => {
      setView(PurchaseView.Timeout);
    }, networkDelayAdjustmentMs);
  }

  return (
    <>
      <Image
        src={data.purchaseInfo.imageUrl}
        alt={data.purchaseInfo.imageAlt}
        sx={{
          width: 672,
          height: 672,
          position: 'absolute',
          top: '-180px',
          right: '45px',
          display: ['none', 'none', 'block'],
        }}
      />
      <FlexLayout
        space={[12, 12, 20]}
        flexDirection="column"
        justifyContent="center"
        sx={{ margin: '0 auto', maxWidth: 1200 }}
      >
        <PurchaseHero {...data.purchaseInfo} />
        <FlexLayout space={[8, 8, 20]} flexDirection={['column', 'column', 'row']} sx={{ position: 'relative' }}>
          <FlexLayout flexDirection="column" space={[6, 8, 12]}>
            <CountdownTimer onCountdownEnd={onCountdownEnd} />
            <FlexLayout
              flexDirection="column"
              backgroundColor="white"
              px={[4, 6, 6]}
              py={[4, 8, 8]}
              space={[8, 12, 12]}
            >
              <FlexLayout flexDirection="column">
                <TextWithLines text={data.strings.paymentMethod} hasLeftLine={false} textVariant="h5" upperCase />
                <SquareForm ref={squareFormRef} />
                <Text variant="paragraph-s-medium" color="black70" isCentered>
                  {data.strings.or}
                </Text>
                <Box sx={{ mt: '34px' }}>
                  <PayPalForm
                    createOrder={createPayPalOrder}
                    onApprove={onApprovePayPalOrder}
                    isDisabled={isDisabled}
                  />
                </Box>
              </FlexLayout>
              <PriceBreakdown strings={data.strings} />
              <TermsOfService />
              <FlexLayout justifyContent="center">
                <Box sx={{ position: 'relative' }}>
                  <Button
                    label={data.strings.completePurchase}
                    variant="primary"
                    sx={{ margin: '0 auto' }}
                    onClick={createSquarePayment}
                    isDisabled={isDisabled || isPurchasing || !ticketAcquired}
                  />
                  <Box sx={{ position: 'absolute', top: 'calc(50% - 16px)', right: '-56px' }}>
                    {isPurchasing && <LoadingIndicator colorOne="white" colorTwo="midnight800" sizePx={32} />}
                  </Box>
                </Box>
              </FlexLayout>
            </FlexLayout>
          </FlexLayout>
          <PassInfo {...data.competitorBundleInfo} />
        </FlexLayout>
      </FlexLayout>
    </>
  );
}
