'use client';

import { PurchaseInfoProps } from '@/types/strapi';
import { FlexLayout, Image, Text, useScreenType } from '@/ui';

type PurchaseHeroProps = Pick<PurchaseInfoProps, 'date' | 'location' | 'logoAlt' | 'logoUrl' | 'logoMobileUrl'>;

export default function PurchaseHero({ date, location, logoAlt, logoUrl, logoMobileUrl }: PurchaseHeroProps) {
  const { isMobile } = useScreenType();

  return (
    <FlexLayout space={[4, 6, 6]} flexDirection="column" alignItems={['center', 'normal', 'normal']}>
      {isMobile ? (
        <Image
          src={logoMobileUrl}
          alt={logoAlt}
          sx={{
            width: 264,
            height: 158,
          }}
        />
      ) : (
        <Image
          src={logoUrl}
          alt={logoAlt}
          sx={{
            width: 600,
            height: 72,
          }}
        />
      )}
      <FlexLayout space={3}>
        <Text variant={isMobile ? 'paragraph-s-medium' : 'paragraph-l-medium'} color="white70">
          {date}
        </Text>
        <Text variant={isMobile ? 'paragraph-s-medium' : 'paragraph-l-medium'} color="white70">
          •
        </Text>
        <Text variant={isMobile ? 'paragraph-s-medium' : 'paragraph-l-medium'} color="white70">
          {location}
        </Text>
      </FlexLayout>
    </FlexLayout>
  );
}
