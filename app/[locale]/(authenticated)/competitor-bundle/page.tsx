import { redirect } from 'next/navigation';

import { getSiteConfig } from '@/apis/strapi';
import { PayPalScriptProvider } from '@/components/PayPal';
import { providerProps } from '@/config/pay-pal';
import {
  getCompetitorBundleErrorProps,
  getCompetitorBundleInfoProps,
  getCompetitorBundleSoldOutProps,
  getCompetitorBundleTimeoutProps,
  getPurchaseInfoProps,
  getStrings,
} from '@/data/competitor-bundle';
import { getModals } from '@/data/legalDocumentsModals';
import { getWelcomeBoxProps } from '@/data/success';
import { PageProps } from '@/types/router';
import { Box } from '@/ui';
import PurchaseFlow from './PurchaseFlow';
import { PurchaseFlowProps } from './PurchaseFlow/types';

export default async function CompetitorBundlePage({ params: { locale } }: PageProps) {
  const siteConfig = await getSiteConfig(locale);

  if (!siteConfig.featureFlags.isPurchaseEnabled) {
    return redirect('/not-found');
  }

  const props: PurchaseFlowProps = {
    purchaseInfo: getPurchaseInfoProps({ siteConfig }),
    modals: getModals({ siteConfig }),
    strings: getStrings({ siteConfig }),
    competitorBundleInfo: getCompetitorBundleInfoProps({ siteConfig }),
    purchaseErrorInfo: getCompetitorBundleErrorProps({ siteConfig }),
    purchaseTimeoutInfo: getCompetitorBundleTimeoutProps({ siteConfig }),
    purchaseSoldOutInfo: getCompetitorBundleSoldOutProps({ siteConfig }),
    purchaseCompletedInfo: getWelcomeBoxProps({ siteConfig }),
  };

  return (
    <PayPalScriptProvider {...providerProps}>
      <Box
        sx={{
          backgroundImage: [
            `url(/images/rulesBackground.png), url(/images/rulesBackground2.png)`,
            `url(/images/homeBackground3.png)`,
            `url(/images/homeBackground3.png)`,
          ],
          backgroundRepeat: 'no-repeat',
          backgroundSize: ['100% 300px, 100%', '100%', '100%'],
          backgroundPosition: ['50% -10px, 50% 300px', '50%', '50%'],
        }}
      >
        <PurchaseFlow {...props} />
      </Box>
    </PayPalScriptProvider>
  );
}
