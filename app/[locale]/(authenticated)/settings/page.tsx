import { getServerSession } from 'next-auth';

import { getSiteConfig } from '@/apis/strapi';
import { Container } from '@/components';
import { authOptions } from '@/config/next-auth';
import { NotificationSettings } from '@/pages-components/settings';
import { Questionnaire } from '@/pages-components/settings/Questionnaire';
import { getQuestionnaire } from '@/services/user';
import { PageProps } from '@/types/router';
import { FlexLayout, Image, Text } from '@/ui';

export default async function SettingsPage({ params: { locale } }: PageProps) {
  const { localeJSON } = await getSiteConfig(locale);
  const session = await getServerSession(authOptions);
  const initialAnswers = await getQuestionnaire(session?.user?.id ?? '');

  return (
    <FlexLayout
      backgroundColor="midnight800"
      sx={{
        backgroundImage: `url(/images/accountSettingsBackground.png)`,
        backgroundRepeat: 'no-repeat',
        backgroundSize: 'cover',
        flexGrow: 1,
      }}
    >
      <Container>
        <FlexLayout
          mt={[8, 10, 20]}
          mb={14}
          flexDirection="column"
          sx={{ width: ['320px', '720px', '1280px'], maxWidth: '100%', position: 'relative' }}
          space={[10, 10, 16]}
        >
          <Image
            src="/images/molediver-with-bulb.png"
            alt="accoung settings hero image"
            sx={{
              position: 'absolute',
              right: '-45px',
              top: '-45px',
              display: ['none', 'none', 'flex'],
            }}
          />
          <FlexLayout flexDirection={['column', 'row', 'row']} alignItems={['unset', 'center', 'center']} space={2}>
            <Text textVariant={['h7', 'h6', 'h4']} upperCase color="primary-midnight">
              {localeJSON['dashboardPageSettingsCTA'] ?? 'Account Settings'}
            </Text>
          </FlexLayout>
          {/* <ProfileInformation /> */}
          <Questionnaire initialAnswers={initialAnswers} />
          <NotificationSettings />
        </FlexLayout>
      </Container>
    </FlexLayout>
  );
}
