import { getSiteConfig } from '@/apis/strapi';
import { WelcomeBox } from '@/components';
import { getWelcomeBoxProps } from '@/data/success';
import { PageProps } from '@/types/router';
import { Box } from '@/ui';

export default async function SuccessPage({ params: { locale } }: PageProps) {
  const siteConfig = await getSiteConfig(locale);
  const props = getWelcomeBoxProps({ siteConfig });

  return (
    <Box
      sx={{
        backgroundImage: [
          `url(/images/rulesBackground.png), url(/images/rulesBackground2.png)`,
          `url(/images/homeBackground3.png)`,
          `url(/images/homeBackground3.png)`,
        ],
        backgroundRepeat: 'no-repeat',
        backgroundSize: ['100% 300px, 100%', '100%', '100%'],
        backgroundPosition: ['50% -10px, 50% 300px', '50%', '50%'],
      }}
    >
      <Box sx={{ position: 'relative' }} mt={[8, 12, 20]} mb={30} mx={[4, 6, 20]}>
        <WelcomeBox data={props} />
      </Box>
    </Box>
  );
}
