import { redirect } from 'next/navigation';
import { getServerSession } from 'next-auth';
import { PropsWithChildren } from 'react';

import { getSiteConfig } from '@/apis/strapi';
import { authOptions } from '@/config/next-auth';
import { LayoutProps } from '@/types/router';

export default async function Layout({ params: { locale }, children }: PropsWithChildren<LayoutProps>) {
  const [session, siteConfig] = await Promise.all([getServerSession(authOptions), getSiteConfig(locale)]);

  if (!siteConfig.featureFlags.isLoginEnabled) {
    return redirect('/not-found');
  }

  if (!session) {
    return redirect('/api/auth/signin/rso');
  }

  return <>{children}</>;
}
