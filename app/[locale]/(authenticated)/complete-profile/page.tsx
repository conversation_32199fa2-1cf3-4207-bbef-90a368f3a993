import { redirect } from 'next/navigation';
import { getServerSession } from 'next-auth';

import { getSiteConfig } from '@/apis/strapi';
import { Questionnaire } from '@/components/contentstack/Questionnaire';
import { authOptions } from '@/config/next-auth';
import { getPageInfoProps, getQuestionnaireProps } from '@/data/complete-profile';
import { getQuestionnaire } from '@/services/user';
import { PageProps } from '@/types/router';
import { Box, FlexLayout, Image } from '@/ui';
import PurchaseHero from '../competitor-bundle/PurchaseFlow/views/PurchasePending/PurchaseHero';

export * from '@/config/page-cache';

export default async function CompleteProfile({ params: { locale } }: PageProps) {
  const siteConfig = await getSiteConfig(locale);
  const session = await getServerSession(authOptions);

  const initialAnswers = await getQuestionnaire(session!.user.id);

  if (!siteConfig.featureFlags.isProfileProgressEnabled) {
    return redirect('/not-found');
  }

  const { imageAlt, imageUrl, ...rest } = getPageInfoProps({ siteConfig });

  return (
    <Box
      sx={{
        backgroundColor: 'midnight900',
        backgroundImage: 'url(/images/scheduleBackground.png)',
        backgroundRepeat: 'no-repeat',
        backgroundSize: '100% 100%',
      }}
    >
      <Box sx={{ position: 'relative' }} mx={[4, 6, 20]} my={[4, 8, 18]}>
        <Image
          src={imageUrl}
          alt={imageAlt}
          sx={{
            width: 672,
            height: 672,
            position: 'absolute',
            top: '-180px',
            right: '45px',
            display: ['none', 'none', 'block'],
          }}
        />
        <FlexLayout
          space={[12, 12, 20]}
          flexDirection="column"
          justifyContent="center"
          sx={{ margin: '0 auto', maxWidth: 1200 }}
        >
          <PurchaseHero {...rest} />
          <Box sx={{ position: 'relative' }}>
            <Questionnaire initialAnswers={initialAnswers} {...getQuestionnaireProps({ siteConfig })} />
          </Box>
        </FlexLayout>
      </Box>
    </Box>
  );
}
