'use client';

// The global error page is implemented via a special error.tsx file located
// under the [locale] segment, and it must be a client component. As a result,
// the locale should be accessed using the useCurrentLocale hook instead of PageProps.

// https://nextjs.org/docs/app/building-your-application/routing/error-handling

// Another limitation of the current error page implementation is that it doesn't handle
// errors occurring in the [locale]/layout.tsx file well. The error page should be capable
// of managing any critical errors that may arise in the layout, such as CMS request failures,
// authentication issues, and so on.

// Translations should be hardcoded rather than fetched from the CMS to avoid potential issues if the CMS fails.

// TODO: Explore alternative methods for implementing the error page in a multi-language
// environment that can gracefully handle errors originating from the layout file.

import SomethingWentWrong from '@/components/shared/SomethingWentWrong';
import { useCurrentLocale } from '@/hooks';

export default function Error() {
  const locale = useCurrentLocale();

  return <SomethingWentWrong locale={locale} />;
}
