'use client';

import { QueryClient, QueryClientProvider, useQuery } from '@tanstack/react-query';

import { getTvLobbyData } from '../../event-schedule/_api/getTvLobbyData';
import Content from './Content';

const queryClient = new QueryClient();

export function TvScheduleData() {
  return (
    <QueryClientProvider client={queryClient}>
      <DataComponent />
    </QueryClientProvider>
  );
}

export function DataComponent() {
  const { data } = useQuery({ queryFn: getTvLobbyData, queryKey: ['tv-schedule'] });

  return <Content initialData={data ?? []} />;
}
