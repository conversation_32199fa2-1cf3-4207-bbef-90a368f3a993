'use client';

import { useOrientation } from '@uidotdev/usehooks';
import React, { useState } from 'react';
import { Box } from 'theme-ui';

import { FlexLayout, Image, Text } from '../../../../ui';
import { macaoFormat } from '../../../../utils';
import { TvLobbyData } from '../../event-schedule/_components/LobbyList/types';

interface Props {
  initialData: TvLobbyData[];
}
export default function Content({ initialData }: Props) {
  const [data, _setData] = useState<TvLobbyData[]>(initialData);
  const orientation = useOrientation();
  const isPortrait = orientation.type.includes('portrait');

  // useEffect(() => {
  //   const fetchInterval = 60000;

  //   const fetchData = async () => {
  //     try {
  //       const response = await fetch('/api/tv-lobby-data');
  //       if (!response.ok) {
  //         throw new Error('Network response was not ok');
  //       }
  //       const newData = await response.json();
  //       setData(newData);
  //     } catch (error) {
  //       console.error('Failed to fetch data:', error);
  //     }
  //   };

  //   const intervalId = setInterval(fetchData, fetchInterval);

  //   return () => clearInterval(intervalId); // Cleanup interval on component unmount
  // }, []);

  return (
    <>
      <FlexLayout
        sx={{
          backgroundColor: 'midnight900',
          backgroundImage: 'url(/images/dashboardBackground2.webp)',
          backgroundRepeat: 'no-repeat',
          backgroundSize: '100%',
          flexDirection: 'column',
          gap: isPortrait ? '80px' : '40px',
          paddingX: '56px',
          paddingY: '51px',
        }}
      >
        <FlexLayout justifyContent={'space-between'}>
          <FlexLayout flexDirection={'column'} sx={{ gap: '16px' }}>
            <Text
              sx={{
                fontSize: isPortrait ? '90px' : '80px',
                fontWeight: 900,
                lineHeight: '72px',
                fontFamily: 'Transducer',
              }}
            >
              EVENT SCHEDULE
            </Text>

            <Text
              sx={{
                color: 'rgba(255, 255, 255, 0.7)',
                fontFamily: 'Transducer',
              }}
            >
              Dec 13-15, 2024 • Macao
            </Text>
          </FlexLayout>

          <Image src={'images/heroLogo.png'} alt={'logo'} sx={{ height: '120px' }} />
        </FlexLayout>

        <Box
          sx={{
            display: 'grid',
            gridTemplateColumns: isPortrait
              ? 'repeat(auto-fill, minmax(300px, 1fr))'
              : 'repeat(auto-fill, minmax(300px, 1fr))',

            columnGap: isPortrait ? '18px' : '42px',
            rowGap: isPortrait ? ' 32px' : '16px',
          }}
        >
          {data
            .sort((a, b) => a.id - b.id)
            .map((lobby) => (
              <FlexLayout
                key={lobby.id}
                sx={{
                  padding: '10px',
                  gap: '8px',
                  bg: 'midnight40',
                  flexDirection: 'column',
                }}
              >
                <FlexLayout justifyContent={'space-between'} sx={{ width: '100%', alignItems: 'center' }}>
                  <Text
                    sx={{
                      fontSize: '20px',
                      lineHeight: '24px',
                      fontWeight: '600',
                      textTransform: 'uppercase',
                      fontFamily: 'Transducer',
                    }}
                  >
                    {lobby.name}
                  </Text>
                  <FlexLayout sx={{ gap: '10px' }}>
                    <FlexLayout sx={{ gap: '4px', alignItems: 'center' }}>
                      <Text sx={{ fontFamily: 'Transducer', fontWeight: '500', fontSize: '8px', color: 'white70' }}>
                        START TIME:
                      </Text>
                      <Text
                        sx={{
                          fontFamily: 'Transducer',
                          fontWeight: '600',
                          fontSize: '8px',
                          lineHeight: '32px',
                          color: 'white',
                        }}
                      >
                        {lobby.startTime ? macaoFormat(lobby.startTime, 'hh:mm a') : undefined}
                      </Text>
                    </FlexLayout>
                    <FlexLayout sx={{ gap: '4px', alignItems: 'center' }}>
                      <Text sx={{ fontFamily: 'Transducer', fontWeight: '500', fontSize: '8px', color: 'white70' }}>
                        STATION
                      </Text>
                      <Text
                        sx={{
                          fontFamily: 'Transducer',
                          fontWeight: '600',
                          fontSize: '8px',
                          lineHeight: '32px',
                          color: 'white',
                        }}
                      >
                        {lobby.stationId}
                      </Text>
                    </FlexLayout>
                  </FlexLayout>
                </FlexLayout>

                <Box as="hr" sx={{ bg: 'white10' }} />
                <FlexLayout flexDirection={'column'}>
                  <Text
                    sx={{
                      fontFamily: 'Transducer',
                      fontWeight: '500',
                      fontSize: '8px',
                      lineHeight: '20px',
                      color: 'white70',
                    }}
                  >
                    PLAYERS:
                  </Text>
                  <FlexLayout sx={{ gap: '4px', flexWrap: 'wrap' }}>
                    {lobby.players
                      .sort((a, b) => a.id - b.id)
                      .map((player, index) => (
                        <React.Fragment key={player.id}>
                          <Text sx={{ fontFamily: 'Transducer', fontSize: '10px', lineHeight: '17px', color: 'white' }}>
                            {player.displayName}
                          </Text>
                          {index < lobby.players.length - 1 && (
                            <Text sx={{ color: 'white20', lineHeight: '17px' }}>•</Text>
                          )}
                        </React.Fragment>
                      ))}
                  </FlexLayout>
                </FlexLayout>
              </FlexLayout>
            ))}
        </Box>
      </FlexLayout>
    </>
  );
}
