import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';

import { authOptions } from '@/config/next-auth';
import prisma from '@/services/db';
import {
  getKbygReadUpdateSchema,
  getMarketingUpdateSchema,
  getQuestionnaireUpdateSchema,
  getReleaseFormUpdateSchema,
  getRulesAcceptedUpdateSchema,
} from '@/utils';
import { requiredApiKey } from '../const';

export async function GET(req: NextRequest, { params }: { params: { id: string } }) {
  const apiKey = req.headers.get('X-API-Key');
  if (apiKey !== requiredApiKey) {
    return NextResponse.json({ error: { message: 'Invalid credentials!' } }, { status: 401 });
  }

  try {
    const user = await prisma.user.findUnique({
      where: { id: params.id },
      select: {
        id: true,
        extEmail: true,
        extDisplayName: true,
        extRiotId: true,
      },
    });

    if (!user) {
      return NextResponse.json({ error: 'User not found' }, { status: 404 });
    }

    return NextResponse.json(user);
  } catch (err) {
    console.log('Get user error:', err);

    return NextResponse.json({ error: 'Failed to fetch user' }, { status: 500 });
  }
}

// TODO: Refactor, swap Yup with Zod (no discriminated unions...)

const marketingUpdateSchema = getMarketingUpdateSchema();
const rulesAcceptedUpdateSchema = getRulesAcceptedUpdateSchema();
const questionnaireUpdateSchema = getQuestionnaireUpdateSchema();
const releaseFormUpdateSchema = getReleaseFormUpdateSchema();
const kbygReadUpdateSchema = getKbygReadUpdateSchema();

export async function PUT(request: NextRequest, { params }: { params: { id: string } }) {
  try {
    const session = await getServerSession(authOptions);

    if (!session) {
      return NextResponse.json(null, { status: 401 });
    }

    if (session.user.id !== params.id) {
      return NextResponse.json(null, { status: 403 });
    }

    const body = await request.json();
    const [isMarketingUpdate, isRulesAcceptedUpdate, isQuestionnaireUpdate, isReleaseFormUpdate, isKbygReadUpdate] =
      await Promise.all([
        marketingUpdateSchema.isValid(body),
        rulesAcceptedUpdateSchema.isValid(body),
        questionnaireUpdateSchema.isValid(body),
        releaseFormUpdateSchema.isValid(body),
        kbygReadUpdateSchema.isValid(body),
      ]);

    if (
      !isMarketingUpdate &&
      !isRulesAcceptedUpdate &&
      !isQuestionnaireUpdate &&
      !isReleaseFormUpdate &&
      !isKbygReadUpdate
    ) {
      return NextResponse.json(null, { status: 400 });
    }

    let data;
    if (isMarketingUpdate) {
      data = { extMarketing: (body.marketing ?? session.user.extMarketing) as boolean };
    } else if (isRulesAcceptedUpdate) {
      data = { extRulesAccepted: (body.rulesAccepted ?? session.user.extRulesAccepted) as boolean };
    } else if (isReleaseFormUpdate) {
      data = { extReleaseForm: (body.releaseForm ?? session.user.extReleaseForm) as boolean };
    } else if (isKbygReadUpdate) {
      data = { extKbygRead: (body.kbygRead ?? session.user.extKbygRead) as boolean };
    } else {
      data = { extQuestionnaire: body };
    }

    try {
      await prisma.user.update({
        where: { id: session.user.id },
        data,
      });
      // eslint-disable-next-line @typescript-eslint/no-explicit-any
    } catch (err: any) {
      if (err?.code === 'P2002') {
        return NextResponse.json('Display name is already taken.', { status: 409 });
      }

      throw err;
    }

    return NextResponse.json(null, { status: 200 });
  } catch (err) {
    console.log('Update user error:', err);

    return NextResponse.json(null, { status: 500 });
  }
}
