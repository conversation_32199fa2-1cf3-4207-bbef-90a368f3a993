import { NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';

import { authOptions } from '@/config/next-auth';
import prisma from '@/services/db';

export async function GET() {
  console.log('I am here');

  try {
    const session = await getServerSession(authOptions);

    if (!session) {
      return NextResponse.json(null, { status: 401 });
    }

    const whitelistUser = await prisma.whitelist.findUnique({
      where: { email: session.user.extEmail, riotId: session.user.extRiotId },
    });

    if (whitelistUser) return NextResponse.json({ status: true }, { status: 200 });
    else return NextResponse.json({ status: false }, { status: 200 });
  } catch (err) {
    console.log('Whitelist status  error:', err);

    return NextResponse.json(null, { status: 500 });
  }
}
