import { NextRequest, NextResponse } from 'next/server';
import { Prisma } from '@prisma/client';

import prisma from '@/services/db';
import { requiredApiKey } from './const';

export async function GET(req: NextRequest): Promise<NextResponse> {
  const apiKey = req.headers.get('X-API-Key');
  if (apiKey !== requiredApiKey) {
    return NextResponse.json({ error: { message: 'Invalid credentials!' } }, { status: 401 });
  }

  try {
    const url = new URL(req.url);
    const search = url.searchParams.get('search')?.trim() ?? '';

    const whereClause = {
      OR: [
        { extRiotId: { contains: search, mode: Prisma.QueryMode.insensitive } },
        { extEmail: { contains: search, mode: Prisma.QueryMode.insensitive } },
      ],
    };

    const users = await prisma.user.findMany({
      where: whereClause,
      select: {
        id: true,
        extRiotId: true,
        extEmail: true,
      },
      orderBy: {
        extRiotId: 'asc',
      },
    });

    const filteredUsers = users.filter((user) => user.extRiotId);

    return NextResponse.json(filteredUsers, { status: 200 });
  } catch (err) {
    console.error('Error fetching users:', err);
    return NextResponse.json({ error: 'Failed to fetch users' }, { status: 500 });
  }
}
