import { revalidatePath } from 'next/cache';
import { NextRequest, NextResponse } from 'next/server';

import { requiredApiKey } from '../users/const';

export async function GET(req: NextRequest) {
  const apiKey = req.headers.get('X-API-Key');
  if (apiKey !== requiredApiKey) {
    return NextResponse.json({ error: { message: 'Invalid credentials!' } }, { status: 401 });
  }
  revalidatePath('/leaderboard');
  revalidatePath('/brackets/1');
  revalidatePath('/brackets/2');
  revalidatePath('/brackets/3');
  revalidatePath('/brackets/4');
  revalidatePath('/brackets/5');
  revalidatePath('/brackets/6');
  revalidatePath('/brackets/finals');
  // revalidatePath('/event-schedule');
  // revalidatePath('/tv-schedule');
  // revalidatePath('/dashboard');

  return NextResponse.json('revalidated');
}
