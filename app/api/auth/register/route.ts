import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';

import { authOptions } from '@/config/next-auth';
import prisma from '@/services/db';
import { sendEmail } from '@/services/mail';
import { getUserDataSchema } from '@/utils/registration';
import { getLocaleFromReqParams } from '../../utils';
import { getMailHtml, getMailSubject, getMailText } from './registration-mail.utils';

export * from '@/config/route-cache';

const bodySchema = getUserDataSchema();

export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);

    if (!session) {
      return NextResponse.json(null, { status: 401 });
    }

    if (!session.user.extCodeVerified) {
      return NextResponse.json('Mail not verified', { status: 500 });
    }

    const body = await request.json();
    const locale = getLocaleFromReqParams(request);
    const isRequestValid = await bodySchema.isValid(body);

    if (!isRequestValid) {
      return NextResponse.json(null, { status: 400 });
    }

    try {
      await prisma.user.update({
        where: { id: session.user.id },
        data: {
          extDisplayName: body.displayName,
          extRegistred: new Date(),
          extData: body,
        },
      });
      // eslint-disable-next-line @typescript-eslint/no-explicit-any
    } catch (err: any) {
      if (err?.code === 'P2002') {
        return NextResponse.json(locale === 'en' ? 'Display name is already taken.' : '显示名称是必填项。', {
          status: 409,
        });
      }

      throw err;
    }

    await sendEmail({
      to: session.user.extEmail as string,
      subject: getMailSubject(locale),
      html: getMailHtml(body.displayName, locale),
      text: getMailText(body.displayName, locale),
    });

    return NextResponse.json(null, { status: 200 });
  } catch (err) {
    console.log('Register error:', err);

    return NextResponse.json(null, { status: 500 });
  }
}
