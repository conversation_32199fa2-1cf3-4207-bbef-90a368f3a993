import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';

import { authOptions } from '@/config/next-auth';
import prisma from '@/services/db';
import { getBodySchema } from './utils';

export * from '@/config/route-cache';

const bodySchema = getBodySchema();

export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);

    if (!session) {
      return NextResponse.json(null, { status: 401 });
    }

    const body = await request.json();
    const isRequestValid = await bodySchema.isValid(body);

    if (!isRequestValid) {
      return NextResponse.json('Invalid request body.', { status: 400 });
    }

    const user = await prisma.user.findUnique({ where: { id: session.user.id } });

    if (!user) {
      return NextResponse.json({ message: 'User not found.' }, { status: 404 });
    }

    if (user.extCode !== body.code) {
      return NextResponse.json({ message: 'Invalid code.' }, { status: 400 });
    }

    if (user.extCodeExpires && user.extCodeExpires < new Date()) {
      return NextResponse.json({ message: 'Code expired.' }, { status: 400 });
    }

    await prisma.user.update({
      where: { id: session.user.id },
      data: {
        extCodeVerified: new Date(),
      },
    });

    return NextResponse.json(null, { status: 200 });
  } catch (err) {
    console.log('Submit verification code error:', err);

    return NextResponse.json(null, { status: 500 });
  }
}
