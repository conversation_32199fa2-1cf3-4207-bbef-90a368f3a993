import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';

import { authOptions } from '@/config/next-auth';
import prisma from '@/services/db';
import { sendEmail } from '@/services/mail';
import { getLocaleFromReqParams } from '../../utils';
import { createCode, getBodySchema, getCodeExpiryDate } from './utils';
import { getMailHtml, getMailSubject, getMailText } from './verification-mail.utils';

export * from '@/config/route-cache';

const bodySchema = getBodySchema();

export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);

    if (!session) {
      return NextResponse.json(null, { status: 401 });
    }

    const body = await request.json();
    const locale = getLocaleFromReqParams(request);
    const isRequestValid = await bodySchema.isValid(body);

    if (!isRequestValid) {
      return NextResponse.json(null, { status: 400 });
    }

    const verificationCode = createCode();
    const verificationCodeExpires = getCodeExpiryDate();

    try {
      await prisma.user.update({
        where: { id: session.user.id },
        data: {
          extEmail: (body.email || '').toLowerCase(),
          extCode: verificationCode,
          extCodeExpires: verificationCodeExpires,
        },
      });
      // eslint-disable-next-line @typescript-eslint/no-explicit-any
    } catch (err: any) {
      if (err?.code === 'P2002') {
        return NextResponse.json(locale === 'en' ? 'Email is already taken.' : '电子邮件已被占用。', { status: 409 });
      }

      throw err;
    }

    await sendEmail({
      to: body.email,
      subject: getMailSubject(locale),
      html: getMailHtml(verificationCode, locale),
      text: getMailText(verificationCode, locale),
    });

    return NextResponse.json(null, { status: 200 });
  } catch (err) {
    console.log('Request verification code error:', err);

    return NextResponse.json(null, { status: 500 });
  }
}
