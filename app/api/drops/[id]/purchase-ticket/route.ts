import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';

import { authOptions } from '@/config/next-auth';
import { purchaseTicket } from '@/services/drops/purchase-ticket';
import { paypalPayloadSchema, squarePayloadSchema } from '@/utils/payment';
import { getLocaleFromReqParams } from '../../../utils';

export * from '@/config/route-cache';

export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);

    if (!session) {
      return NextResponse.json({ message: 'Session not found.' }, { status: 401 });
    }

    const paymentData = await request.json();
    const isPaymentDataValid =
      (await squarePayloadSchema.isValid(paymentData)) || (await paypalPayloadSchema.isValid(paymentData));

    if (!isPaymentDataValid) {
      return NextResponse.json({ message: 'Invalid request payload' }, { status: 400 });
    }

    const locale = getLocaleFromReqParams(request);

    const ticket = await purchaseTicket(session.user, paymentData, { locale });

    return NextResponse.json(ticket, { status: 200 });
  } catch (err) {
    return NextResponse.json({ message: 'Something went wrong.' }, { status: 500 });
  }
}
