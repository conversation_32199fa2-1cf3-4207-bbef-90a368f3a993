import { NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';

import { authOptions } from '@/config/next-auth';
import { acquireTicket } from '@/services/drops';

export * from '@/config/route-cache';

export async function POST() {
  try {
    const session = await getServerSession(authOptions);

    if (!session) {
      return NextResponse.json({ message: 'Session not found.' }, { status: 401 });
    }

    const ticket = await acquireTicket(session.user);

    return NextResponse.json(ticket, { status: 200 });
  } catch (err) {
    return NextResponse.json({ message: 'Something went wrong.' }, { status: 500 });
  }
}
