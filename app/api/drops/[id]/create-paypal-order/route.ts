import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';

import { authOptions } from '@/config/next-auth';
import { createPaypalOrder } from '@/services/drops/create-paypal-order';
import { createPayPalOrderSchema } from '@/utils/payment';

export * from '@/config/route-cache';

export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);

    if (!session) {
      return NextResponse.json({ message: 'Session not found.' }, { status: 401 });
    }

    const payload = await request.json();
    const isPayloadValid = await createPayPalOrderSchema.isValid(payload);
    if (!isPayloadValid) {
      return NextResponse.json({ message: 'Invalid request payload' }, { status: 400 });
    }

    const order = await createPaypalOrder(session.user, payload);

    return NextResponse.json(order, { status: 200 });
  } catch (err) {
    return NextResponse.json({ message: 'Failed to create paypal order' }, { status: 500 });
  }
}
