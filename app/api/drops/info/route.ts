import { NextResponse } from 'next/server';

import { getDropInfo } from '@/services/drops';

export const dynamic = 'force-dynamic';
export * from '@/config/route-cache';

export async function GET() {
  try {
    const drop = await getDropInfo();

    return new NextResponse(
      JSON.stringify({
        updatedAt: new Date(),
        drop,
      }),
      {
        status: 200,
        headers: {
          'Content-Type': 'application/json',
          'Cache-Control': 'no-store, max-age=0, must-revalidate',
        },
      },
    );
  } catch (err) {
    return NextResponse.json('Drop not found', { status: 404 });
  }
}
