// app/api/lobbyData/route.ts

import { NextResponse } from 'next/server';

import { getTvLobbyData } from '../../[locale]/event-schedule/_api/getTvLobbyData';

import '@/config/route-cache';

export async function GET() {
  try {
    const data = await getTvLobbyData();
    return NextResponse.json(data);
  } catch (error) {
    return NextResponse.json({ error: 'Failed to fetch lobby data' }, { status: 500 });
  }
}
