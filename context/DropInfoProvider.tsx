'use client';

import { createContext, PropsWithChildren, useContext } from 'react';

import { DropInfo, UserContext } from '@/services/drops';

type DropInfoType = {
  general: DropInfo;
  user: UserContext;
};

const DropInfoContext = createContext<DropInfoType>({ general: null, user: null });

export interface DropInfoProviderProps {
  general: DropInfo;
  user: UserContext;
}

export function DropInfoProvider({ general, user, children }: PropsWithChildren<DropInfoProviderProps>) {
  return <DropInfoContext.Provider value={{ general, user }}>{children}</DropInfoContext.Provider>;
}

// For client components
export const useDropInfo = () => {
  return useContext(DropInfoContext);
};
