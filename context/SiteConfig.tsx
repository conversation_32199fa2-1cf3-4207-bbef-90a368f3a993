'use client';

import { createContext, PropsWithChildren, useContext } from 'react';

import { SiteConfig } from '@/types/strapi';

// eslint-disable-next-line @typescript-eslint/ban-ts-comment
// @ts-expect-error
const SiteConfigContext = createContext<SiteConfig>(null);

export interface SiteConfigProviderProps {
  data: SiteConfig;
}

export function SiteConfigProvider({ data, children }: PropsWithChildren<SiteConfigProviderProps>) {
  return <SiteConfigContext.Provider value={data}>{children}</SiteConfigContext.Provider>;
}

// For client components
export const useSiteConfig = () => {
  return useContext(SiteConfigContext);
};
