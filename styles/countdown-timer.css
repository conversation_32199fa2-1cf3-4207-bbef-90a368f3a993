@media screen and (min-width: 850px) {
  .countdown_timer {
    display: flex;
  }

  .hour-container,
  .minute-container,
  .second-container {
    display: flex;
    align-items: center;
    justify-content: center;
    flex-direction: column;
    min-width: 175px;
    padding: 16px 19px;
    background-color: var(--theme-ui-colors-primary600);
    position: relative;
  }

  .hour-container,
  .minute-container {
    margin-right: 64px;
  }

  .hour-container::after,
  .minute-container::after {
    content: ':';
    font-size: 70px;
    line-height: 72px;
    font-weight: 900;
    font-family: 'transducer-extended';
    color: var(--theme-ui-colors-white);
    position: absolute;
    right: -43px;
    top: 16px;
  }

  .hour-digits,
  .minute-digits,
  .second-digits {
    font-size: 70px;
    line-height: 72px;
    font-weight: 900;
    font-family: 'transducer-extended';
    color: var(--theme-ui-colors-white);
  }

  .hour-label,
  .minute-label,
  .second-label {
    font-size: 12px;
    line-height: 20px;
    letter-spacing: 0.3px;
    font-weight: 621;
    margin-top: 6px;
    font-family: 'transducer-extended';
    color: var(--theme-ui-colors-white);
  }
}

@media screen and (max-width: 849px) {
  .countdown_timer {
    display: flex;
  }

  .hour-container,
  .minute-container,
  .second-container {
    display: flex;
    align-items: center;
    justify-content: center;
    flex-direction: column;
    min-width: 96px;
    padding: 12px;
    background-color: var(--theme-ui-colors-primary600);
    position: relative;
  }

  .hour-container,
  .minute-container {
    margin-right: 18px;
  }

  .hour-container::after,
  .minute-container::after {
    content: ':';
    font-size: 36px;
    line-height: 50px;
    font-weight: 900;
    font-family: 'transducer-extended';
    color: var(--theme-ui-colors-white);
    position: absolute;
    top: 11px;
    right: -15px;
  }

  .hour-digits,
  .minute-digits,
  .second-digits {
    font-size: 36px;
    line-height: 50px;
    font-weight: 900;
    font-family: 'transducer-extended';
    color: var(--theme-ui-colors-white);
  }

  .hour-label,
  .minute-label,
  .second-label {
    font-size: 12px;
    line-height: 18px;
    font-weight: 621;
    font-family: 'transducer-extended';
    color: var(--theme-ui-colors-white);
  }
}
