import { AuthOptions } from 'next-auth';
import type { Adapter } from 'next-auth/adapters';
import { PrismaAdapter } from '@auth/prisma-adapter';

import prisma from '@/services/db';
import { checkQuestionnaireCompleteness } from '@/utils';
import registeredUsers from './data/chinesePlayers.json';
import registrationInfos from './data/registrationInfos.json';
import { RegisteredUser } from './data/types';
import RSO from './providers/rso';

export const authOptions: AuthOptions = {
  providers: [
    RSO({
      clientId: process.env.RSO_CLIENT_ID ?? '',
      clientSecret: process.env.RSO_CLIENT_SECRET ?? '',
    }),
  ],
  callbacks: {
    session({ session, user }) {
      // Adding extra properties from the User model to the session object
      // to ensure they are accessible via getServerSession or useSession.
      // Type safety is ensured by extending the Session and User interfaces
      // in `types/next-auth.d.ts`.
      session.user.id = user.id;
      session.user.extPuuid = user.extPuuid;
      session.user.extGameName = user.extGameName;
      session.user.extDisplayName = user.extDisplayName;
      session.user.extEmail = user.extEmail;
      session.user.extCodeVerified = user.extCodeVerified;
      session.user.extRegistred = user.extRegistred;
      session.user.extMarketing = user.extMarketing;
      session.user.extRulesAccepted = user.extRulesAccepted;
      session.user.extQuestionnaire = checkQuestionnaireCompleteness(user.extQuestionnaire);
      session.user.extReleaseForm = user.extReleaseForm;
      session.user.extRiotId = user.extRiotId;
      session.user.extHasTicket = user.extHasTicket;
      session.user.extRealmUsername = user.extRealmUsername;
      session.user.extRealmPassword = user.extRealmPassword;
      session.user.extKbygRead = user.extKbygRead;
      return session;
    },
  },
  adapter: PrismaAdapter(prisma) as Adapter,
  events: {
    async createUser({ user }) {
      const registeredUser = registeredUsers.find(
        (u) => u.PUUID === user.extPuuid || `${u.Name}${u.TagLine}` === user.extRiotId,
      );
      if (!registeredUser) {
        return;
      }

      const data = getRegistrationData(registeredUser);
      const createdUser = await prisma.user.update({ data, where: { id: user.id } });

      await prisma.whitelist.create({
        data: { email: createdUser.extEmail as string, riotId: createdUser.extRiotId as string },
      });
    },
    async signIn({ user }) {
      try {
        const whitelistUser = await prisma.whitelist.findFirst({
          // Ideally we also check for email match, but we don't have user's email at this point
          where: { riotId: user.extRiotId },
        });

        if (!(whitelistUser && !user.extHasTicket)) return;

        await prisma.ticket.create({
          data: {
            userId: user.id,
            reserved: true,
            purchased: true,
            purchaseData: { type: 'whitelist' },
          },
        });

        await prisma.user.update({
          where: { id: user.id },
          data: {
            extHasTicket: true,
          },
        });
      } catch (error) {
        console.error('Error in signIn event:', error);
      }
    },
  },
};

function getRegistrationData(user: RegisteredUser) {
  const registrationInfo = registrationInfos.find((i) => i.Email === user.Email);

  const registrationData = {
    extCode: '111111',
    extCodeVerified: new Date(),
    extCodeExpires: new Date(),
    extData: {
      age: true,
      terms: true,
      marketing: true,
      country: registrationInfo?.Country ?? '',
      lastName: registrationInfo?.['Last Name'] ?? '',
      firstName: registrationInfo?.['First Name'] ?? '',
      shirtSize: registrationInfo?.['Shirt Size'] ?? 'Medium',
      gameServer: 'CN',
      displayName: registrationInfo?.['prefered ID'] ?? '',
      preferredContact: registrationInfo?.['Preferred Contact'] ?? '',
    } as any,
    extRegistred: new Date(),
    extDisplayName: user['Display name'],
    extEmail: user.Email,
    extRealmUsername: user['TR Login'],
    extRealmPassword: user['TR Password'],
  };

  return registrationData;
}
