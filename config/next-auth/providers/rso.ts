import { OAuthConfig, OAuthUserConfig } from 'next-auth/providers';

const baseURL = 'https://auth.riotgames.com';

export interface RSOProfile {
  sub: string;
}

export default function RSO<P extends RSOProfile>(options: OAuthUserConfig<P>): OAuthConfig<P> {
  return {
    issuer: baseURL,
    id: 'rso',
    name: 'Riot Sign On',
    type: 'oauth',
    wellKnown: `${baseURL}/.well-known/openid-configuration`,
    userinfo: `${baseURL}/userinfo`,
    jwks_endpoint: `${baseURL}/jwks.json`,
    authorization: {
      url: `${baseURL}/authorize`,
      params: {
        redirect_uri: process.env.RSO_REDIRECT_URI,
        scope: 'openid offline_access cpid',
      },
    },
    token: {
      url: `${baseURL}/token`,
      async request(context) {
        const { provider, params: parameters, checks, client } = context;

        const tokens = await client.grant({
          grant_type: 'authorization_code',
          code: parameters.code,
          redirect_uri: process.env.RSO_REDIRECT_URI,
          code_verifier: checks.code_verifier,
          client_id: provider.clientId,
          client_secret: provider.clientSecret,
        });

        return { tokens };
      },
    },
    async profile(profile, tokens) {
      const riotAccount = await getRiotAccount(tokens.access_token as string);

      return {
        id: profile.sub,
        extPuuid: riotAccount?.puuid,
        extGameName: riotAccount?.gameName,
        extRiotId: `${riotAccount?.gameName}#${riotAccount?.tagLine}`,
      };
    },
    options: options,
  };
}

async function getRiotAccount(
  accessToken: string,
): Promise<{ puuid: string; gameName: string; tagLine: string } | null> {
  try {
    const response = await fetch('https://americas.api.riotgames.com/riot/account/v1/accounts/me', {
      headers: {
        Authorization: `Bearer ${accessToken}`,
        'Content-Type': 'application/json',
      },
    });

    if (!response.ok) {
      throw new Error('Failed to fetch summoner data');
    }

    return response.json();
  } catch {
    return null;
  }
}
