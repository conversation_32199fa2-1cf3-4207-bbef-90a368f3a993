import * as yup from 'yup';

import { PaymentProcessor } from '@/enums/payments';

export type SquarePayload = yup.InferType<typeof squarePayloadSchema>;
export type PayPalPayload = yup.InferType<typeof paypalPayloadSchema>;
export type PaymentMeta = yup.InferType<typeof paymentMetaSchema>;

const paymentMetaSchema = yup.object().shape({
  price: yup.number().required(),
  tax: yup.number().required(),
});

export const squarePayloadSchema = yup.object().shape({
  processor: yup.string().oneOf([PaymentProcessor.Square]),
  processorData: yup.object().shape({
    sourceId: yup.string().required(),
    idempotencyKey: yup.string().required(),
    locationId: yup.string().required(),
  }),
  meta: paymentMetaSchema,
});

export const paypalPayloadSchema = yup.object().shape({
  processor: yup.string().oneOf([PaymentProcessor.PayPal]),
  processorData: yup.object().shape({
    orderId: yup.string().required(),
  }),
  meta: paymentMetaSchema,
});

export type CreatePayPalOrder = yup.InferType<typeof createPayPalOrderSchema>;

export const createPayPalOrderSchema = yup.object().shape({
  meta: paymentMetaSchema,
});
