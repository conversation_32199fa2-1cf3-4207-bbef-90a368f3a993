import * as Yup from 'yup';

export type UserData = Yup.InferType<ReturnType<typeof getUserDataSchema>>;

export function getUserDataSchema(localeJson?: Record<string, string>) {
  return Yup.object().shape({
    displayName: Yup.string()
      .required(localeJson?.['displayNameRequiredError'] ?? 'Display Name is required.')
      .min(3, localeJson?.['displayNameMinCharCountError'] ?? 'Display Name must have 3 or more characters.')
      .max(15, localeJson?.['displayNameCharCountError'] ?? 'Display Name is limited to 15 characters.')
      .matches(
        /^[a-zA-Z0-9 ]+$/gi,
        localeJson?.['displayNameLatinCharsError'] ?? 'Display Name can only contain Latin letters.',
      ),
    gameServer: Yup.string().required(localeJson?.['gameServerRequiredError'] ?? 'Game Server is required.'),
    preferredGameClientLanguage: Yup.string().required(
      localeJson?.['regPreferredGameClientLanguageRequiredError'] ?? 'Preferred Game Client Language is required.',
    ),
    firstName: Yup.string().required(localeJson?.['firstNameRequiredError'] ?? 'First name is required.'),
    lastName: Yup.string().required(localeJson?.['lastNameRequiredError'] ?? 'Last name is required.'),
    country: Yup.string().required(localeJson?.['countryRequiredError'] ?? 'Country is required.'),
    preferredContact: Yup.string().required(
      localeJson?.['preferredContactRequiredError'] ?? 'Preferred contact is required.',
    ),
    shirtSize: Yup.string().required(localeJson?.['shirtSizeRequiredError'] ?? 'Shirt size is required.'),
    runeterraRegion: Yup.string().required(
      localeJson?.['regRuneterraRegionRequiredError'] ?? 'Runeterra Region is required.',
    ),
    age: Yup.bool()
      .required()
      .test((v) => !!v),
    terms: Yup.bool()
      .required()
      .test((v) => !!v),
    marketing: Yup.bool()
      .required()
      .test((v) => !!v),
  });
}
