import parse, { DOMNode, domToReact, Element, HTMLReactParserOptions, Text as TextNode } from 'html-react-parser';
import { CSSProperties } from 'react';

import { Box, Link, Text } from '@/ui';

interface ElementsMapper {
  h1?: React.FC<any>;
  h2?: React.FC<any>;
  h3?: React.FC<any>;
  h4?: React.FC<any>;
  h5?: React.FC<any>;
  h6?: React.FC<any>;
  p?: React.FC<any>;
  a?: React.FC<any>;
  strong?: React.FC<any>;
  text?: React.FC<any>;
  li?: React.FC<any>;
  ol?: React.FC<any>;
  ul?: React.FC<any>;
}

const parseStyleAttribute = (styleAttribute: string): CSSProperties => {
  const stylePairs = styleAttribute.split(';').filter(Boolean);
  const styleObject: Record<string, string> = {};
  for (const pair of stylePairs) {
    const [property, value] = pair.split(':').map((part) => part.trim());
    const camelCaseProperty = property.replace(/-([a-z])/g, (_, letter) => letter.toUpperCase());
    styleObject[camelCaseProperty] = value;
  }
  return styleObject;
};

const DEFAULT_ELEMENTS_MAPPER: ElementsMapper = {
  p: (props) => <Text textVariant={['paragraph-s-regular', 'paragraph-m-regular']} color="white" as="p" {...props} />,
  h1: (props) => <Text textVariant="h1" color="white" my={8} as="h1" {...props} />,
  h2: (props) => <Text textVariant="h2" color="white" my={8} as="h2" {...props} />,
  h3: (props) => <Text textVariant="h3" color="white" my={8} as="h3" {...props} />,
  h4: (props) => <Text textVariant="h4" color="white" my={8} as="h4" {...props} />,
  h5: (props) => <Text textVariant="h5" color="white" my={8} as="h5" {...props} />,
  h6: (props) => <Text textVariant="h6" color="white" my={8} as="h6" {...props} />,
  strong: (props) => (
    <Text textVariant={['paragraph-s-bold', 'paragraph-m-bold']} color="white" as="strong" {...props} />
  ),
  a: (props) => <Link color="linkColor1" {...props} />,
  text: (props) => (
    <Text
      textVariant={['paragraph-xs-medium', 'paragraph-xs-medium', 'paragraph-l-medium']}
      as="span"
      sx={{ color: 'inherit' }}
      {...props}
    />
  ),
  li: (props) => <Box as="li" {...props} color="white" />,
  ol: (props) => (
    <Box
      as="ol"
      {...props}
      sx={{
        counterReset: 'item',
        li: {
          display: 'block',
          ':before': {
            fontWeight: 'bold',
            content: 'counters(item, ". ") ". "',
            counterIncrement: 'item',
          },
        },
      }}
    />
  ),
  ul: (props) => <Box as="ul" {...props} />,
};

export const parseRichTextToReactComponents = (
  content: string,
  styleOverrides: CSSProperties = {},
  elementsMapper: ElementsMapper = DEFAULT_ELEMENTS_MAPPER,
  options: HTMLReactParserOptions = {},
) => {
  const replace = (node: DOMNode) => {
    const mapper = { ...DEFAULT_ELEMENTS_MAPPER, ...elementsMapper };

    if (node instanceof Element) {
      const { attribs, children, name } = node;

      const { style, ...restAtrribs } = attribs;

      const parsedStyle = style ? parseStyleAttribute(style) : {};
      const mergedStyle = { ...parsedStyle, ...styleOverrides };

      const ElementWrapper = mapper[name as keyof typeof elementsMapper];

      if (ElementWrapper) {
        return (
          <ElementWrapper {...restAtrribs} style={mergedStyle}>
            {domToReact(children, { ...options, replace: options.replace ?? replace })}
          </ElementWrapper>
        );
      }

      return null;
    }

    if (node instanceof TextNode) {
      const TextWrapper = elementsMapper['text'];

      if (TextWrapper) {
        return <TextWrapper style={styleOverrides}>{node.data}</TextWrapper>;
      }

      return <>{node.data}</>;
    }

    return null;
  };

  return parse(content, {
    ...options,
    replace,
  });
};
