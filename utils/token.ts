import Cookies from 'js-cookie';

interface SetTokensParams {
  accessToken: string;
  refreshToken: string;
  idToken: string;
  exp?: number;
}

export const setTokens = ({ accessToken, refreshToken, idToken, exp = 86400 }: SetTokensParams) => {
  Cookies.set('accessToken', accessToken, {
    expires: exp,
    secure: process.env.NODE_ENV !== 'development',
    path: '/',
  });

  Cookies.set('refreshToken', refreshToken, {
    expires: exp,
    secure: process.env.NODE_ENV !== 'development',
    path: '/',
  });

  Cookies.set('idToken', idToken, {
    expires: exp,
    secure: process.env.NODE_ENV !== 'development',
    path: '/',
  });
};

export const getAccessToken = () => {
  return Cookies.get('accessToken');
};

export const getRefreshToken = () => {
  return Cookies.get('refreshToken');
};

export const getIdToken = () => {
  return Cookies.get('idToken');
};

export const removeTokens = () => {
  Cookies.remove('accessToken');
  Cookies.remove('refreshToken');
  Cookies.remove('idToken');
};
