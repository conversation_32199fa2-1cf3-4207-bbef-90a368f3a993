import * as Yup from 'yup';

export type MarketingUpdateSchema = Yup.InferType<ReturnType<typeof getMarketingUpdateSchema>>;
export type RulesAcceptedUpdateSchema = Yup.InferType<ReturnType<typeof getRulesAcceptedUpdateSchema>>;
export type QuestionnaireUpdateSchema = Yup.InferType<ReturnType<typeof getQuestionnaireUpdateSchema>>;
export type ReleaseFormUpdateSchema = Yup.InferType<ReturnType<typeof getReleaseFormUpdateSchema>>;
export type KbygReadUpdateSchema = Yup.InferType<ReturnType<typeof getKbygReadUpdateSchema>>;

export function getMarketingUpdateSchema() {
  return Yup.object({
    marketing: Yup.bool().required('Marketing flag is required.'),
  });
}

export function getRulesAcceptedUpdateSchema() {
  return Yup.object({
    rulesAccepted: Yup.bool().required('Rules accepted flag is required.'),
  });
}

export function getReleaseFormUpdateSchema() {
  return Yup.object({
    releaseForm: Yup.bool().required('Release form flag is required.'),
  });
}

export function getKbygReadUpdateSchema() {
  return Yup.object({
    kbygRead: Yup.bool().required('KBYG read flag is required.'),
  });
}

export function getQuestionnaireUpdateSchema() {
  return Yup.object({
    preferredLanguages: Yup.array().of(Yup.string()),
    socialMediaHandles: Yup.string(),
    gamerNameInfo: Yup.string(),
    celebrationInfo: Yup.string(),
    comingWithInfo: Yup.string(),
    playstyleDescription: Yup.string(),
    preparationInfo: Yup.string(),
    otherGamesInfo: Yup.string(),
    playerAdvice: Yup.string(),
    playerFeeling: Yup.string(),
  });
}

export const removeDisplayNameHash = (displayName: string) => {
  return displayName.split('#')[0];
};

export const checkQuestionnaireCompleteness = (questionnaire: any) => {
  if (!questionnaire) return false;

  const stringFields = [
    'playerAdvice',
    'gamerNameInfo',
    'playerFeeling',
    'comingWithInfo',
    'otherGamesInfo',
    'celebrationInfo',
    'preparationInfo',
    'socialMediaHandles',
    'playstyleDescription',
  ];

  const doesAnyFieldHaveData = stringFields.some(
    (question) => typeof questionnaire[question] === 'string' && questionnaire[question].trim() !== '',
  );

  const hasPreferredLanguage =
    Array.isArray(questionnaire.preferredLanguages) && questionnaire.preferredLanguages.length > 0;

  return doesAnyFieldHaveData || hasPreferredLanguage;
};
