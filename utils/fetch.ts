export async function handleError(response: Response): Promise<void> {
  let body;

  try {
    body = await response.json();
  } catch (err) {
    return Promise.reject(new Error(`Server error: ${response.status} ${response.statusText}`));
  }

  if (body?.message) {
    return Promise.reject(new Error(body.message));
  }

  return Promise.reject(new Error(`Server error: ${response.status} ${response.statusText}`));
}
