import { CUSTOM_EVENTS } from '@/enums/custom-events';

type EventDataMap = {
  [CUSTOM_EVENTS.SHOW_LOGIN_MODAL]: object;
  [CUSTOM_EVENTS.LOGOUT]: object;
  [CUSTOM_EVENTS.SHOW_REGISTER_MODAL]: object;
  [CUSTOM_EVENTS.TOGGLE_ACCOUNT_MENU]: {
    isOpen: boolean;
  };
  [CUSTOM_EVENTS.TOGGLE_NAVIGATION_MENU]: {
    isOpen: boolean;
  };
  [CUSTOM_EVENTS.SHOW_TFT_REGISTRATIONS_DISABLED_MODAL]: object;
};

export function onEvent<T extends CUSTOM_EVENTS>(
  eventType: T,
  listener: (event: CustomEvent<EventDataMap[T]>) => void,
) {
  document.addEventListener(eventType, listener as EventListener);
}

export function offEvent<T extends CUSTOM_EVENTS>(
  eventType: T,
  listener: (event: CustomEvent<EventDataMap[T]>) => void,
) {
  document.removeEventListener(eventType, listener as EventListener);
}

export function onceOnEvent(eventType: CUSTOM_EVENTS, listener: (event: Event) => void) {
  onEvent(eventType, handleEventOnce);

  function handleEventOnce(event: Event) {
    listener(event);
    offEvent(eventType, handleEventOnce);
  }
}

export function triggerEvent<T extends CUSTOM_EVENTS>(eventType: T, data?: EventDataMap[T]) {
  const event = new CustomEvent(eventType, { detail: data });
  document.dispatchEvent(event);
}
