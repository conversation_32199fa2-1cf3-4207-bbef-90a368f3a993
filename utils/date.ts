import { formatInTimeZone, FormatOptionsWithTZ } from 'date-fns-tz';

export const calculateAge = (date: Date) => {
  const ageDifMs = Date.now() - date.getTime();
  const ageDate = new Date(ageDifMs); // miliseconds from epoch
  return Math.abs(ageDate.getUTCFullYear() - 1970);
};

export const formatMatchDate = (date: Date) => {
  const pacificDate = new Date(date);
  pacificDate.setUTCHours(pacificDate.getUTCHours() - 8);

  const months = ['JAN', 'FEB', 'MAR', 'APR', 'MAY', 'JUN', 'JUL', 'AUG', 'SEP', 'OCT', 'NOV', 'DEC'];
  const month = months[date.getUTCMonth()];
  const day = pacificDate.getUTCDate();
  const hour = pacificDate.getUTCHours();
  const minute = pacificDate.getUTCMinutes();

  const ampm = hour >= 12 ? 'PM' : 'AM';
  const formattedHour = hour % 12 || 12;

  return `${month} ${day}, ${formattedHour}:${minute.toString().padStart(2, '0')} ${ampm} PT`;
};

export const MACAO_TIMEZONE_OFFSET = '+08:00';
export const PT_TIMEZONE_OFFSET = '-08:00';
export const ET_TIMEZONE_OFFSET = '-05:00';
export const CEST_TIMEZONE_OFFSET = '+02:00';

export function macaoFormat(date: Date | string | number, formatStr: string, options?: FormatOptionsWithTZ) {
  return formatInTimeZone(date, MACAO_TIMEZONE_OFFSET, formatStr, options);
}
