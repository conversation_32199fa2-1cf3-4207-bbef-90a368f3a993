import * as yup from 'yup';

export type Rank = keyof yup.InferType<typeof rankSchema>;
export type Period = yup.InferType<typeof periodSchema>;
export type DropConfig = yup.InferType<typeof dropConfigSchema>;

export const rankSchema = yup.object().shape({
  tactitians_crown: yup.boolean().required(),
  challenger: yup.boolean().required(),
  grandmaster: yup.boolean().required(),
  master: yup.boolean().required(),
  other: yup.boolean().required(),
});

export const allRanks = Object.keys(rankSchema.fields) as unknown as Rank[];

export const periodSchema = yup.object().shape({
  start: yup.date().required(),
  price: yup.number().positive().required(),
  tax: yup.number().min(0).required(),
  allowed_ranks: rankSchema.required(),
});

export const dropConfigSchema = yup.object().shape({
  reservationSeconds: yup.number().integer().positive().min(60).required(),
  orderedPeriods: yup
    .array()
    .of(periodSchema)
    .required()
    .test('is-chronological', 'Each period must have a later start date than the previous one', function (periods) {
      if (!periods || periods.length < 2) return true;

      for (let i = 1; i < periods.length; i++) {
        if (new Date(periods[i].start) <= new Date(periods[i - 1].start)) {
          return false;
        }
      }
      return true;
    })
    .test('ranks-required', 'Each rank must be true in at least one period', function (periods) {
      return allRanks.every((rank) => periods.some((period: Period) => period.allowed_ranks[rank]));
    }),
});
