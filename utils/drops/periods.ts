import { isBefore } from 'date-fns';
import { findLast, isEqual, sortBy } from 'lodash';

import { Period } from './config';

export function getCurrentPeriod(periods: Period[]): Period | null {
  const now = new Date();

  const sortedPeriods = sortBy(periods, (period) => period.start);

  const currentPeriod = findLast(sortedPeriods, (period) => {
    return isBefore(period.start, now) || isEqual(period.start, now);
  });

  return currentPeriod ?? null;
}
