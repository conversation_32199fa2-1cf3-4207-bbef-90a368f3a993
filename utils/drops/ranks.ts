import { Tier } from '@/types/drops';
import { Rank } from './config';

// `Tier` is fetched from the Riot Games API for a specific user.
// `Rank` is used internally for managing tickets.

// TODO: Replace the `Rank` type with an enum.

const tactitiansCrownEmails: string[] = getTactitiansCrownEmails();

export function getUserRank(email?: string, tier?: Tier): Rank {
  // Check if a user has the 'tactitians_crown' rank by
  // cross-referencing with the list of emails
  if (email && tactitiansCrownEmails.includes(email.toLowerCase())) {
    return 'tactitians_crown';
  }

  switch (tier) {
    case 'CHALLENGER':
      return 'challenger';
    case 'GRANDMASTER':
      return 'grandmaster';
    case 'MASTER':
      return 'master';
    default:
      return 'other';
  }
}

function getTactitiansCrownEmails() {
  return [
    // ArsFutura (for testing)
    '<EMAIL>',
    '<EMAIL>',
    'borna.b<PERSON><PERSON>+<EMAIL>',

    // Official
    '<EMAIL>',
    '<EMAIL>',
    '<EMAIL>',
    '<EMAIL>',
    '<EMAIL>',
    '<EMAIL>',
    '<EMAIL>',
    '<EMAIL>',
    '<EMAIL>',
    '<EMAIL>',
    '<EMAIL>',
    '<EMAIL>',
    '<EMAIL>',
    '<EMAIL>',
    '<EMAIL>',
    '<EMAIL>',
    '<EMAIL>',
    '<EMAIL>',
    '<EMAIL>',
    '<EMAIL>',
    '<EMAIL>',
    '<EMAIL>',
    '<EMAIL>',
    '<EMAIL>',
    '<EMAIL>',
    '<EMAIL>',
    '<EMAIL>',
    '<EMAIL>',
    '<EMAIL>',
    '<EMAIL>',
    '<EMAIL>',
    '<EMAIL>',
    '<EMAIL>',
    '<EMAIL>',
    '<EMAIL>',
    '<EMAIL>',
    '<EMAIL>',
    '<EMAIL>',
    '<EMAIL>',
    '<EMAIL>',
    '<EMAIL>',
    '<EMAIL>',
    '<EMAIL>',
    '<EMAIL>',
    '<EMAIL>',
    '<EMAIL>',
    '<EMAIL>',
    '<EMAIL>',
    '<EMAIL>',
    '<EMAIL>',
    '<EMAIL>',
    '<EMAIL>',
    '<EMAIL>',
    '<EMAIL>',
    '<EMAIL>',
    '<EMAIL>',
    '<EMAIL>',
    '<EMAIL>',
    '<EMAIL>',
    '<EMAIL>',
    '<EMAIL>',
    '<EMAIL>',
    '<EMAIL>',
    '<EMAIL>',
    '<EMAIL>',
    '<EMAIL>',
    '<EMAIL>',
    '<EMAIL>',
    '<EMAIL>',
    '<EMAIL>',
    '<EMAIL>',
    '<EMAIL>',
    '<EMAIL>',
    '<EMAIL>',
  ].map((mail) => mail.toLowerCase());
}
