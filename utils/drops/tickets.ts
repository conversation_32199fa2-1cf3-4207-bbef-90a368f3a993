import { User } from 'next-auth';
import { isBefore } from 'date-fns';

import { TicketPurchaseState } from '@/enums/drops';
import { DropConfig, Ticket } from '@/types/drops';
import { allRanks, Rank } from './config';

export type TicketsSaleStats = ReturnType<typeof getTicketsSaleStats>;

export function getTicketsSaleStats(tickets: Ticket[]) {
  return tickets.reduce(
    (acc, ticket) => {
      if (ticket.purchased) {
        acc.purchased += 1;
      } else if (isTicketReserved(ticket)) {
        acc.reserved += 1;
      } else {
        acc.available += 1;
      }
      return acc;
    },
    { purchased: 0, reserved: 0, available: 0 },
  );
}

export function isTicketReserved(ticket: Ticket) {
  if (!ticket.reservedUntil) return false;

  // prettier-ignore
  return (
    ticket.userId &&
    ticket.reserved &&
    ticket.reservedUntil &&
    isBefore(new Date(), ticket.reservedUntil)
  );
}

export function isTicketPurchased(ticket: Ticket) {
  return ticket.purchased;
}

export function findAvailableTicket(tickets: Ticket[]): Ticket | null {
  return tickets.find((ticket) => !isTicketReserved(ticket) && !isTicketPurchased(ticket)) ?? null;
}

export type TicketsSaleFlags = ReturnType<typeof getTicketsSaleFlags>;

export function getTicketsSaleFlags(stats: TicketsSaleStats) {
  const { purchased, reserved, available } = stats;

  return {
    soldOut: available === 0 && reserved === 0 && purchased > 0,
    waiting: available === 0 && reserved > 0,
    canPurchase: available > 0,
  };
}

export type TicketsSaleStartTimes = ReturnType<typeof getTicketsSaleStartTimes>;

export function getTicketsSaleStartTimes(dropConfig: DropConfig) {
  const saleStartTimes = allRanks.reduce((acc, rank) => {
    const period = dropConfig.orderedPeriods.find((p) => p.allowed_ranks[rank]);
    if (period) acc[rank] = period.start;
    return acc;
  }, {} as Record<Rank, Date>);

  // Ensure every rank has a date resolved
  for (const rank of allRanks) {
    if (!saleStartTimes[rank]) {
      throw new Error(`Missing start time for rank: ${rank}`);
    }
  }

  return saleStartTimes;
}

export function getTicketPurchaseState(ticket: Ticket | null) {
  if (ticket?.purchased) {
    return TicketPurchaseState.Purchased;
  }

  if (ticket && isTicketReserved(ticket)) {
    return TicketPurchaseState.Reserved;
  }

  return TicketPurchaseState.Initial;
}

export function isTicketReservedByUser(ticket: Ticket, user: User) {
  return isTicketReserved(ticket) && user.id === ticket.userId;
}

export function isTicketPurchasedByUser(ticket: Ticket, user: User) {
  return isTicketPurchased(ticket) && user.id === ticket.userId;
}
