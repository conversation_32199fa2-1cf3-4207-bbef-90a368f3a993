'use client';

import {
  DISPATCH_ACTION,
  FUNDING,
  PayPalButtons,
  SCRIPT_LOADING_STATE,
  usePayPalScriptReducer,
} from '@paypal/react-paypal-js';
import { useEffect } from 'react';

export default function PayPalForm({ createOrder, onApprove, isDisabled }: any) {
  const [{ isInitial, isPending }, dispatch] = usePayPalScriptReducer();

  useEffect(() => {
    if (isInitial) {
      dispatch({
        type: DISPATCH_ACTION.LOADING_STATUS,
        value: SCRIPT_LOADING_STATE.PENDING,
      });
    }
  }, []);

  if (isPending) {
    return null;
  }

  return (
    <PayPalButtons
      fundingSource={FUNDING.PAYPAL}
      createOrder={createOrder}
      onApprove={onApprove}
      disabled={isDisabled}
      style={{ layout: 'vertical' }}
    />
  );
}
