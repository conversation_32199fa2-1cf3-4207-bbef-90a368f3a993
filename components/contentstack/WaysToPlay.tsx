import { useState } from 'react';

import { Box, colors, FlexLayout, Icon, Image, Tab, Text } from '@/ui';
import { PaddingContainer } from '../shared/PaddingContainer';

interface WaysToPlayProps {
  title: string;
  description: string;
  roles: {
    title: string;
    waysToPlay: {
      title: string;
      description: string;
      imageUrl: string;
      imageAlt: string;
    }[];
  }[];
}

export const WaysToPlay = ({ title, description, roles }: WaysToPlayProps) => {
  const [selectedRole, setSelectedRole] = useState(roles[0]);
  const [imageIndex, setImageIndex] = useState(0);

  return (
    <FlexLayout flexDirection="column" space={[8, 8, 12]} mt={[30, 30, 60]}>
      <PaddingContainer>
        <FlexLayout flexDirection="column" alignItems="center" space={[3, 3, 8]}>
          <Text as="h1" textVariant={['h6', 'h6', 'h3']} upperCase isCentered color="primary-midnight">
            {title}
          </Text>
          <Text
            as="p"
            textVariant={['paragraph-xs-medium', 'paragraph-xs-medium', 'paragraph-l-medium']}
            sx={{ maxWidth: ['100%', 360, 680] }}
            isCentered
            color="midnight80"
          >
            {description}
          </Text>
        </FlexLayout>
      </PaddingContainer>
      <FlexLayout flexDirection="column" alignItems="center">
        <PaddingContainer>
          <FlexLayout flexDirection={['column', 'row']}>
            {roles.map((role) => {
              return (
                <Tab
                  key={role.title}
                  isSelected={selectedRole.title === role.title}
                  label={role.title}
                  onClick={() => setSelectedRole(role)}
                  sx={{
                    zIndex: 1,
                    minWidth: ['100%', 133, 196],
                    position: 'relative',
                    // left: isMobile && index === 0 ? '-35px' : '',
                    // right: isMobile && index === 1 ? '-35px' : '',
                  }}
                />
              );
            })}
          </FlexLayout>
        </PaddingContainer>
        <FlexLayout
          alignItems="flex-start"
          justifyContent="flex-start"
          space={[11, 0]}
          px={[0, 6]}
          sx={{
            display: ['none', 'none', 'flex'],
            overflow: 'auto',
            width: '100%',
            '::-webkit-scrollbar': { display: 'none' },
            msOverflowStyle: 'none',
            scrollbarWidth: 'none',
            mt: 5,
          }}
        >
          {selectedRole.waysToPlay.map((wayToPlay, wayToPlayIndex) => {
            return (
              <WayToPlayCard
                key={wayToPlay.title}
                wayToPlay={wayToPlay}
                wayToPlayIndex={wayToPlayIndex}
                selectedRole={selectedRole}
              />
            );
          })}
        </FlexLayout>

        <FlexLayout
          alignItems="center"
          justifyContent="center"
          px={[0, 6]}
          sx={{
            display: ['flex', 'flex', 'none'],
            position: 'relative',
            width: '100%',
            // mt: 5,
          }}
        >
          <Arrow
            icon="chevronLeft"
            onClick={() => {
              if (selectedRole.waysToPlay[imageIndex - 1]) {
                setImageIndex(imageIndex - 1);
              }
            }}
            sx={{
              left: ['4%', '15%', ''],
            }}
          />
          <Arrow
            icon="chevronRight"
            alignSelf="left"
            onClick={() => {
              if (selectedRole.waysToPlay[imageIndex + 1]) {
                setImageIndex(imageIndex + 1);
              }
            }}
            sx={{
              right: ['4%', '15%', ''],
            }}
          />
          <WayToPlayCard
            wayToPlay={selectedRole.waysToPlay[imageIndex]}
            wayToPlayIndex={imageIndex}
            selectedRole={selectedRole}
          />
        </FlexLayout>
      </FlexLayout>
    </FlexLayout>
  );
};

// eslint-disable-next-line @typescript-eslint/no-explicit-any
function WayToPlayCard({ wayToPlay, wayToPlayIndex, selectedRole }: any) {
  function getCustomOffset() {
    if (selectedRole.title === 'Competitors' && wayToPlayIndex === 0) {
      return ['25px', '15px', 0];
    }

    if (selectedRole.title === 'Competitors' && wayToPlayIndex === 1) {
      return ['15px', '10px', 0];
    }

    if (selectedRole.title === 'Competitors' && wayToPlayIndex === 2) {
      return ['15px', '7px', 0];
    }

    if (selectedRole.title === 'Spectators' && wayToPlayIndex === 0) {
      return ['0px', '-10px', '-15px'];
    }

    if (selectedRole.title === 'Spectators' && wayToPlayIndex === 1) {
      return ['15px', '10px', 0];
    }

    if (selectedRole.title === 'Spectators' && wayToPlayIndex === 2) {
      return ['10px', '0px', '-10px'];
    }
  }
  return (
    <FlexLayout
      flexDirection="column"
      key={wayToPlay.title}
      alignItems="center"
      flex={1}
      sx={{
        minWidth: 'fit-content',
        '&:first-of-type': {
          marginRight: '-32px',
        },
        '&:last-of-type': {
          marginLeft: '-32px',
        },
      }}
      mb={10}
    >
      {wayToPlay.imageUrl && (
        <Box
          sx={{
            width: [335, 375, 335],
            height: [335, 375, 335],
            position: 'relative',
            display: 'flex',
            justifyContent: 'center',
            alignItems: 'flex-end',
            // TODO: remove after receiving final assets
            left: getCustomOffset(),
          }}
        >
          <Image
            src={wayToPlay.imageUrl}
            alt={wayToPlay.imageAlt}
            style={{ objectFit: 'contain', width: '100%', height: '100%' }}
          />
        </Box>
      )}
      <FlexLayout
        alignItems="center"
        flexDirection="column"
        space={[6, 8]}
        sx={{
          maxWidth: ['240px', '400px'],
          minHeight: 188,
          marginLeft: [8, 4, 0],
        }}
      >
        <Text as="h5" textVariant={['h6', 'h6', 'h4']} isCentered upperCase color="primary-midnight">
          {wayToPlay.title}
        </Text>
        <Text
          as="p"
          textVariant={['paragraph-xs-medium', 'paragraph-xs-medium', 'paragraph-m-medium']}
          isCentered
          color="midnight80"
        >
          {wayToPlay.description}
        </Text>
      </FlexLayout>
    </FlexLayout>
  );
}

// eslint-disable-next-line @typescript-eslint/no-explicit-any
function Arrow({ sx, icon, onClick }: any) {
  return (
    <Box
      sx={{
        width: 52,
        height: 40,
        backgroundColor: colors.lilac600,
        display: 'flex',
        justifyContent: 'center',
        alignItems: 'center',
        cursor: 'pointer',
        position: 'absolute',
        top: 'calc(50% - 70px)',
        // left: 'calc(50% - 167px - 24px)',
        zIndex: 1,
        ...sx,
      }}
      onClick={onClick}
    >
      <Icon icon={icon} color="secondary-lemonade" />
    </Box>
  );
}
