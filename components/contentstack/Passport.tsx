import { Box, FlexLayout, Text } from '@/ui';
import { Carousel } from './Carousel';

export const Passport = ({ title }: { title: string }) => {
  const carouselProps = {
    autoplay: true,
    autoplayTimeMs: 7500,
    items: [
      {
        imageAlt: 'Passport - 1',
        imageUrl: '/images/passport1.webp',
      },
      {
        imageAlt: 'Passport - 1',
        imageUrl: '/images/passport2.webp',
      },
    ],
    itemsMobile: [
      {
        imageAlt: 'Passport - 1',
        imageUrl: '/images/passport1.webp',
      },
      {
        imageAlt: 'Passport - 1',
        imageUrl: '/images/passport2.webp',
      },
    ],
  };

  return (
    <FlexLayout flexDirection={'column'} alignItems={'center'} mt={40}>
      <Text variant="h1" color="white">
        {title}
      </Text>
      <Box sx={{ width: '100%' }}>
        <Carousel {...carouselProps}></Carousel>
      </Box>
    </FlexLayout>
  );
};
