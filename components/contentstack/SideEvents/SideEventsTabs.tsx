'use client';

import { FlexLayout, Text } from '@/ui';

interface SideEventsTabsProps {
  tabs: TabProps[];
}

export const SideEventsTabs = ({ tabs }: SideEventsTabsProps) => {
  return (
    <FlexLayout space={6} mx={[-4, 0, 0]} px={[4, 0, 0]} sx={{ overflowY: ['scroll', 'scroll', 'visible'] }}>
      {tabs.map((tab, index) => (
        <Tab {...tab} key={`${tab.label} + ${index}`} />
      ))}
    </FlexLayout>
  );
};

interface TabProps {
  isSelected: boolean;
  label: string;
  onClick: () => void;
}

const Tab = ({ isSelected, label, onClick }: TabProps) => {
  return (
    <FlexLayout
      alignItems="center"
      backgroundColor={isSelected ? 'purpleLavander500' : 'brightBlue30'}
      opacity={isSelected ? 1 : 0.5}
      sx={{ height: ['40px', '40px', '54px'], cursor: 'pointer' }}
      flexShrink={0}
      px={[4, 4, 6]}
      pt={['6px', '6px', 3]}
      pb={['10px', '10px', '16px']}
      onClick={onClick}
    >
      <Text textVariant={['h7', 'h7', 'h5']} upperCase>
        {label}
      </Text>
    </FlexLayout>
  );
};
