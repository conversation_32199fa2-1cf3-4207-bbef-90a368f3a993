import { SideEventsPageProps } from '@/data/side-events';
import { FlexLayout, Text } from '@/ui';

type SideEventsHowToBlockProps = Pick<SideEventsPageProps, 'howTo'>;

export const SideEventsHowToBlock = ({ howTo }: SideEventsHowToBlockProps) => {
  const { title, firstParagraph, secondParagraph, eventTypes, eventTypesFirstRow, eventTypesSecondRow, lastParagraph } =
    howTo;

  return (
    <FlexLayout
      mx="auto"
      mt={[0, 0, 7]}
      pt={4}
      pb={[6, 11, 6]}
      px={[4, 8, 6]}
      color="white"
      flexDirection="column"
      sx={{
        maxWidth: ['100%', '100%', '25%'],
        backgroundColor: 'midnight40',
        backdropFilter: 'blur(7px)',
        gap: 6,
        height: 'fit-content',
      }}
    >
      <Text as="h4" variant="h4" upperCase>
        {title}
      </Text>
      <Text as="p" variant="paragraph-s-medium">
        {firstParagraph}
      </Text>
      <Text as="p" variant="paragraph-s-medium">
        {secondParagraph}
      </Text>
      <FlexLayout flexDirection="column">
        <Text as="ul" p={0} variant="paragraph-s-medium">
          {eventTypes}
          <Text as="li" ml={6} variant="paragraph-s-medium">
            {eventTypesFirstRow}
          </Text>
          <Text as="li" ml={6} variant="paragraph-s-medium">
            {eventTypesSecondRow}
          </Text>
        </Text>
      </FlexLayout>
      <Text variant="paragraph-s-medium">{lastParagraph}</Text>
    </FlexLayout>
  );
};
