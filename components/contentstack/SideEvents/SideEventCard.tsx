import { BlocksRenderer } from '@strapi/blocks-react-renderer';
import { useEffect, useRef, useState } from 'react';

import { useCurrentLocale } from '@/hooks';
import { EventTag, SideEvent } from '@/types/strapi';
import { Box, Color, colors, FlexLayout, Link, Text, useScreenType } from '@/ui';
import { fonts } from '@/ui/theme/typography/fonts';
import { macaoFormat } from '@/utils/date';
import { extractTextFromJson, getSideEventTagColors } from './utils';

interface SideEventCardProps {
  event: SideEvent;
  startTime: string;
  location: string;
  info: string;
  showMore: string;
}

export const SideEventCard = ({ event, startTime, location, info, showMore }: SideEventCardProps) => {
  return (
    <FlexLayout
      pt={[4, 6]}
      pb={[4, 8]}
      px={[4, 8]}
      sx={{ backgroundColor: 'midnight40', backdropFilter: 'blur(7px)', width: '100%' }}
      color="white"
    >
      <FlexLayout space={[4, 6]} flexDirection="column" sx={{ width: '100%' }}>
        <FlexLayout space={4} flexDirection="column">
          <FlexLayout space={[2, 6]} flexDirection={['column-reverse', 'row']} alignItems={['start', 'end']}>
            <Text
              sx={{
                fontSize: ['32px', '40px'],
                lineHeight: ['38.4px', '48px'],
                fontFamily: fonts['Transducer'],
                fontWeight: 600,
              }}
              upperCase
            >
              {event.title}
            </Text>
            <Tag tag={event.tag} />
          </FlexLayout>
          <FlexLayout space={8}>
            <FlexLayout flexDirection="column" space={1} flexBasis={['100%', 'auto']}>
              <Text textVariant="paragraph-xs-medium" color="white70">
                {startTime}:
              </Text>
              <Text textVariant="paragraph-l-medium">{macaoFormat(event.startTime, 'h:mm a')}</Text>
            </FlexLayout>
            <FlexLayout flexDirection="column" space={1} flexBasis={['100%', 'auto']}>
              <Text textVariant="paragraph-xs-medium" color="white70">
                {location}:
              </Text>
              <Text textVariant="paragraph-l-medium">{event.location}</Text>
            </FlexLayout>
          </FlexLayout>
        </FlexLayout>
        {!!event.info && (
          <>
            <Box backgroundColor="white10" sx={{ height: '1px', width: '100%' }} />
            <FlexLayout flexDirection="column" space={1}>
              <Text textVariant="paragraph-xs-medium" color="white70">
                {info}:
              </Text>
              <EventInfoBlock content={event.info} showMoreLabel={showMore} />
            </FlexLayout>
          </>
        )}
      </FlexLayout>
    </FlexLayout>
  );
};

interface TagProps {
  tag: EventTag;
}

const Tag = ({ tag }: TagProps) => {
  const { color, backgroundColor } = getSideEventTagColors(tag);

  return (
    <Text
      textVariant="paragraph-m-medium"
      pt="2px"
      px={3}
      pb={1}
      upperCase
      color={color as Color}
      backgroundColor={backgroundColor}
      sx={{ flexShrink: 0 }}
    >
      {tag}
    </Text>
  );
};

interface EventInfoBlockProps {
  content: unknown;
  showMoreLabel: string;
}

const EventInfoBlock = ({ content, showMoreLabel }: EventInfoBlockProps) => {
  const { isMobile } = useScreenType();

  const [isExpanded, setIsExpanded] = useState(false);
  const [isOverflown, setIsOverflown] = useState(false);
  const [contentCharsLimit, setContentCharsLimit] = useState(110);

  const locale = useCurrentLocale();
  const isEnLocale = locale === 'en';

  const blockRef = useRef<HTMLDivElement>(null);

  if (!content) {
    return null;
  }

  const stringContent = extractTextFromJson(content);

  function calculateContainerOverflow() {
    if (blockRef.current) {
      // if content has more characters than this container can display, set isOverflown to true
      // one char has approx 9px, we should have 4 rows visible on mobile and 2 rows on larger screens,
      // so max chars that container can display is calculated as (container width / char width) * number of visible rows
      const maxStringLength = isEnLocale
        ? (blockRef.current.clientWidth / (isMobile ? 10 : 11)) * (isMobile ? 4 : 2)
        : (blockRef.current.clientWidth / (isMobile ? 16 : 18)) * (isMobile ? 4 : 2);
      const stringCharsLimit = isEnLocale ? maxStringLength - 20 : maxStringLength - 10;
      if (stringContent.length > maxStringLength) {
        setIsOverflown(true);
        // set contentCharsLimit as max num of chars that container can display (calculated above) minus 10/20 chars for displaying the '... Show more' label
        setContentCharsLimit(stringCharsLimit);
      } else {
        setIsOverflown(false);
        setIsExpanded(true);
      }
    }
  }

  useEffect(() => {
    calculateContainerOverflow();
  }, [calculateContainerOverflow]);

  useEffect(() => {
    const onResize = () => calculateContainerOverflow();

    window.addEventListener('resize', onResize);
    onResize();

    return () => window.removeEventListener('resize', onResize);
  }, []);

  return (
    <Box
      color="white"
      sx={{
        maxHeight: isExpanded ? '100%' : ['88px', '60px'],
        overflow: 'hidden',
      }}
      ref={blockRef}
    >
      {isExpanded || !isOverflown ? (
        <BlocksRenderer
          // eslint-disable-next-line @typescript-eslint/no-explicit-any
          content={content as any}
          modifiers={{
            bold: ({ children }) => (
              <Text textVariant={['paragraph-s-medium', 'paragraph-m-medium', 'paragraph-m-medium']}>{children}</Text>
            ),
          }}
          blocks={{
            paragraph: ({ children }) => (
              <Text textVariant={['paragraph-s-medium', 'paragraph-m-medium', 'paragraph-m-medium']}>{children}</Text>
            ),
            list: ({ children }) => (
              <Box as="ul" margin={0}>
                {children}
              </Box>
            ),
            link: ({ url, children }) => (
              <Link href={url} sx={{ color: colors.purpleLavander500 }} target="_blank">
                {children}
              </Link>
            ),
            'list-item': ({ children }) => (
              <Text textVariant={['paragraph-s-medium', 'paragraph-m-medium', 'paragraph-m-medium']} as="li">
                {children}
              </Text>
            ),
          }}
        />
      ) : (
        <Text
          textVariant={['paragraph-s-medium', 'paragraph-m-medium', 'paragraph-m-medium']}
          sx={{
            ':after': {
              content: isOverflown ? '"..."' : '""',
            },
          }}
        >
          {stringContent.slice(0, contentCharsLimit)}
        </Text>
      )}
      {!isExpanded && isOverflown && (
        <Text
          textVariant={['paragraph-s-medium', 'paragraph-m-medium', 'paragraph-m-medium']}
          color="purpleLavander500"
          onClick={() => setIsExpanded(true)}
          sx={{
            cursor: 'pointer',
          }}
        >
          &nbsp;{showMoreLabel}
        </Text>
      )}
    </Box>
  );
};
