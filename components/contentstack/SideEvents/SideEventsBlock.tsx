'use client';

import sortBy from 'lodash/sortBy';
import { useEffect, useMemo, useState } from 'react';

import { SideEventsPageProps } from '@/data/side-events';
import { Button, FlexLayout, useScreenType } from '@/ui';
import { macaoFormat } from '@/utils/date';
import { SideEventCard } from './SideEventCard';
import { SideEventsTabs } from './SideEventsTabs';

enum SideEventsDates {
  FirstDay = '2024-12-13',
  SecondDay = '2024-12-14',
  ThirdDay = '2024-12-15',
}

type SideEventsBlockProps = Pick<SideEventsPageProps, 'sideEvents'>;

export const SideEventsBlock = ({ sideEvents }: SideEventsBlockProps) => {
  const { events, firstTab, secondTab, thirdTab, seeMore } = sideEvents;
  const { isDesktop } = useScreenType();

  const [selectedDate, setSelectedDate] = useState(SideEventsDates.FirstDay);
  const [areAllEventsVisible, toggleAreAllEventsVisible] = useState(true);

  const eventsFilteredByDate = useMemo(() => {
    const filteredEvents = events?.filter((event) => {
      const eventDate = macaoFormat(event.startTime, 'yyyy-MM-dd');
      return eventDate === selectedDate;
    });

    return sortBy(filteredEvents, 'order');
  }, [events, selectedDate]);

  if (!eventsFilteredByDate) {
    return null;
  }

  useEffect(() => {
    toggleAreAllEventsVisible(eventsFilteredByDate?.length <= 5 || isDesktop);
  }, [eventsFilteredByDate, isDesktop, toggleAreAllEventsVisible]);

  const visibleEvents = areAllEventsVisible ? eventsFilteredByDate : eventsFilteredByDate.slice(0, 5);

  return (
    <FlexLayout space={[6, 8, 10]} flexDirection="column" flexGrow={1}>
      <SideEventsTabs
        tabs={[
          {
            label: firstTab,
            isSelected: selectedDate === SideEventsDates.FirstDay,
            onClick: () => setSelectedDate(SideEventsDates.FirstDay),
          },
          {
            label: secondTab,
            isSelected: selectedDate === SideEventsDates.SecondDay,
            onClick: () => setSelectedDate(SideEventsDates.SecondDay),
          },
          {
            label: thirdTab,
            isSelected: selectedDate === SideEventsDates.ThirdDay,
            onClick: () => setSelectedDate(SideEventsDates.ThirdDay),
          },
        ]}
      />
      <FlexLayout space={[8, 12]} flexDirection="column" alignItems="center">
        {visibleEvents.map((event, index) => (
          <SideEventCard key={`${event.title} + ${index}`} event={event} {...sideEvents} />
        ))}
        {!areAllEventsVisible && (
          <Button label={seeMore} onClick={() => toggleAreAllEventsVisible(!areAllEventsVisible)} size="medium" />
        )}
      </FlexLayout>
    </FlexLayout>
  );
};
