'use client';

import { Box, FlexLayout, Image, Text } from '@/ui';

interface EmptySideEventsBlockProps {
  bodyText: string;
  imageUrl: string;
  imageAlt?: string;
}

export const EmptySideEventsBlock = ({ bodyText, imageUrl, imageAlt }: EmptySideEventsBlockProps) => {
  return (
    <Box sx={{ position: 'relative', margin: '0 auto' }}>
      <Image
        src={imageUrl}
        alt={imageAlt}
        sx={{
          position: 'absolute',
          display: ['none', 'block'],
          width: [0, '280px', '380px'],
          height: [0, '280px', '380px'],
          right: [0, '-23px', '-160px'],
          top: [0, '-160px', '-230px'],
          zIndex: [0, 0, 1],
        }}
      />
      <FlexLayout
        mx="auto"
        px={[4, 16, 50]}
        py={[16, 16, 40]}
        color="white"
        flexDirection="column"
        sx={{
          maxWidth: '1090px',
          backgroundColor: 'white',
          position: 'relative',
          zIndex: [1, 1, 0],
        }}
        alignItems="center"
      >
        <Text
          as="p"
          textVariant={['paragraph-s-medium', 'paragraph-m-medium', 'paragraph-l-medium']}
          sx={{ maxWidth: '690px', textAlign: 'center' }}
          color="primary-midnight"
        >
          {bodyText}
        </Text>
      </FlexLayout>
    </Box>
  );
};
