import { EventTag } from '@/types/strapi';

export function getSideEventTagColors(tag: EventTag) {
  switch (tag) {
    case EventTag.Freeplay:
    case EventTag.免费参与:
      return { backgroundColor: 'brightBlue', color: 'white' };
    case EventTag.Panel:
    case EventTag.策划面对面:
      return { backgroundColor: 'paleYellow500', color: 'midnight900' };
    case EventTag.Tournament:
    case EventTag.娱乐赛事:
      return { backgroundColor: 'dreamBlue', color: 'white' };
    default:
      return { backgroundColor: 'dreamBlue', color: 'white' };
  }
}

export function extractTextFromJson(jsonArray: unknown) {
  const extractedText: any[] = [];

  function traverse(node: any) {
    if (Array.isArray(node)) {
      node.forEach((child) => traverse(child));
    } else if (node && typeof node === 'object') {
      if (node.text) {
        extractedText.push(node.text);
      }
      if (node.children) {
        traverse(node.children);
      }
    }
  }

  traverse(jsonArray);
  return extractedText.join(' ');
}
