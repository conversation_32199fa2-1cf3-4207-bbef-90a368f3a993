import { Box, colors, FlexLayout, GridLayout, Icon, Image, Text, useScreenType } from '@/ui';
import { parseRichTextToReactComponents } from '@/utils';
import { ConditionalWrapper } from '../shared';

interface TicketsInfoBlockProps {
  imageUrl: string;
  imageAlt: string;
  title: string;
  description: string;
  list: string[];
  badge?: string;
  reverse?: boolean;
  bottomNote?: string;
  backgroundImage?: string;
}

export const TicketsInfoBlock = (data: TicketsInfoBlockProps) => {
  const { isMobile } = useScreenType();

  // const imagePosition = data.reverse
  //   ? {
  //       left: ['unset', '-20px', '-10px'],
  //       right: ['unset', 'unset', 'unset'],
  //     }
  //   : {
  //       left: ['unset', 'unset', 'unset'],
  //       right: ['unset', '-20px', '-10px'],
  //     };

  return (
    <ConditionalWrapper condition={!isMobile} wrapper={(children) => <Box sx={{ px: [4, 6, 20] }}>{children}</Box>}>
      <FlexLayout
        flexDirection={data.reverse ? ['column', 'row-reverse', 'row-reverse'] : ['column', 'row', 'row']}
        alignItems={['center', 'flex-start', 'center']}
        justifyContent="center"
        mb={10}
        mt={[0, 0, 15]}
      >
        <Box
          sx={{
            display: 'flex',
            justifyContent: 'center',
            alignItems: 'center',
            width: [240, 324, 504],
          }}
        >
          <Image
            src={data.imageUrl}
            alt={data.imageAlt}
            sx={{
              objectFit: 'cover',
              width: [260, 324, 520],
              // minWidth: [280, 324, 550],
              // height: [394, 504, 896],
              // minHeight: [394, 504, 896],
              position: 'relative',
              // marginTop: ['-95px', 'unset', 'unset'],
              // top: ['55px', '-20px', '-20px'],
              // ...imagePosition,
            }}
          />
        </Box>
        <FlexLayout
          flexDirection="column"
          alignItems="center"
          px={[4, 4, 14]}
          py={[4, 4, 12]}
          sx={{
            width: '100%',
            maxWidth: ['unset', 480, 774],
            minWidth: 'auto',
            zIndex: 1,
            position: 'relative',
            backgroundImage: `url(${data.backgroundImage ?? ''})`,
            backgroundRepeat: 'no-repeat',
            backgroundSize: ['cover', 'contain'],
          }}
        >
          {data.badge && (
            <Image
              src={data.badge}
              alt="Badge"
              sx={{
                position: 'absolute',
                top: ['-150px', '-60px', '0'],
                left: ['unset', 'unset', '-70px'],
                right: ['0', '-30px', 'unset'],
                width: ['120px', '120px', '160px'],
                height: ['120px', '120px', '160px'],
              }}
            />
          )}
          <Text as="h1" textVariant={['h7', 'h7', 'h3']} upperCase isCentered color="primary-midnight">
            {data.title}
          </Text>
          <Box mt={[3, 3, 10]} sx={{ maxWidth: 662, color: colors['primary-midnight'], textAlign: 'center' }}>
            {parseRichTextToReactComponents(data.description, { fontSize: '16px' })}
          </Box>
          <GridLayout gridTemplateColumns="repeat(1, 1fr)" columnGap={17} rowGap={8} mt={[8, 8, 16]}>
            {data.list.map((item) => {
              return (
                <FlexLayout space={4} key={item}>
                  <Icon size="s" icon="polygon" mt={1} color={colors['primary-macaron']} />
                  <Text
                    textVariant={['paragraph-xs-medium', 'paragraph-s-medium', 'paragraph-m-medium']}
                    sx={{ maxWidth: '620px' }}
                    color="primary-midnight"
                  >
                    {item}
                  </Text>
                </FlexLayout>
              );
            })}
          </GridLayout>
          {data.bottomNote && (
            <Text
              as="p"
              textVariant={['h10', 'h10', 'h8']}
              mt={[4, 4, 6]}
              p={4}
              color="primary-midnight"
              sx={{
                background: colors.midnight50,
                backgroundImage: `repeating-linear-gradient(0deg, ${colors['secondary-lilac']}, ${colors['secondary-lilac']} 8px, transparent 8px, transparent 16px), repeating-linear-gradient(90deg, ${colors['secondary-lilac']}, ${colors['secondary-lilac']} 8px, transparent 8px, transparent 16px), repeating-linear-gradient(180deg, ${colors['secondary-lilac']}, ${colors['secondary-lilac']} 8px, transparent 8px, transparent 16px), repeating-linear-gradient(270deg, ${colors['secondary-lilac']}, ${colors['secondary-lilac']} 8px, transparent 8px, transparent 16px)`,
                backgroundSize: '1px 100%, 100% 1px, 1px 100%, 100% 1px',
                backgroundPosition: '0 0, 0 0, 100% 0, 0 100%',
                backgroundRepeat: 'no-repeat',
                textAlign: 'center',
              }}
            >
              {data.bottomNote}
            </Text>
          )}
          {/* <Button
              as="a"
              href="/pass-purchase"
              label={'Buy competitor pass'}
              mt={[8, 6]}
              mx={isMobile ? 3 : 0}
              px={5}
            /> */}
        </FlexLayout>
      </FlexLayout>
    </ConditionalWrapper>
  );
};
