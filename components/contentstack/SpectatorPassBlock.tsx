import { Property } from 'csstype';

import { Box, Button, colors, FlexLayout, Image, Text, useScreenType } from '@/ui';
import { parseRichTextToReactComponents } from '@/utils';

interface SpectatorPassBlockProps {
  title: string;
  description: string;
  imageUrl: string;
  imageAlt: string;
  assetPosition: string;
  button: {
    showButton: boolean;
    isOffsite: boolean;
    link: {
      href: string;
      title: string;
      label: string;
    };
  }[];
  assetPositionMobile: string;
  textBelowButtons?: string;
}

export const SpectatorPassBlock = (data: SpectatorPassBlockProps) => {
  const { isDesktop } = useScreenType();

  const getLayoutDirection = (): Property.FlexDirection[] => {
    switch (data.assetPosition) {
      case 'left':
        return ['column-reverse', 'row-reverse'];
      default:
        return [data.assetPositionMobile === 'top' ? 'column-reverse' : 'column', 'row'];
    }
  };

  return (
    <Box px={[0, 6, 10]} id="spectatorPass">
      <FlexLayout flexDirection={getLayoutDirection()} space={[0, 9, 10]} alignItems="center" justifyContent="center">
        <FlexLayout
          flexDirection="column"
          space={[6, 6, 12]}
          flex={1}
          alignItems="center"
          sx={{ maxWidth: [328, '100%', 615], minWidth: ['none', 'none', 568], zIndex: 1 }}
          mx={[4, 0, 0]}
        >
          <FlexLayout flexDirection="column" space={[3, 3, 6]}>
            <Text as="h1" textVariant={['h6', 'h6', 'h3']} upperCase isCentered color="primary-midnight">
              {data.title}
            </Text>
            <Box
              sx={{
                maxWidth: ['100%', '100%', 620],
                alignSelf: 'center',
                color: colors.midnight80,
                textAlign: 'center',
              }}
            >
              {parseRichTextToReactComponents(data.description, {
                textAlign: 'center',
              })}
            </Box>
          </FlexLayout>
          <FlexLayout
            justifyContent="center"
            alignItems="center"
            flexDirection={['column', 'column', 'row']}
            space={[6, 6, 8]}
          >
            {data.button.map((el) => {
              if (el.showButton && el.link.href !== '') {
                return (
                  <Button
                    label={el.link.title}
                    as="a"
                    href={el.link.href}
                    key={el.link.title}
                    target={el.isOffsite ? '_new' : '_self'}
                    size={isDesktop ? 'large' : 'small'}
                    backgroundSize="110%"
                  />
                );
              }

              return null;
            })}
          </FlexLayout>
          {data?.textBelowButtons && (
            <Text color="midnight60" sx={{ maxWidth: 480 }} isCentered textVariant="paragraph-xs-medium">
              {data.textBelowButtons}
            </Text>
          )}
        </FlexLayout>
        {data.imageUrl && (
          <Image
            src={data.imageUrl}
            alt={data.imageAlt}
            width="100%"
            height="100%"
            sx={{ maxWidth: [277, 277, 600], maxHeight: [277, 277, 600], mb: [8, 0, 0], mr: [0, -3, 0] }}
          />
        )}
      </FlexLayout>
    </Box>
  );
};
