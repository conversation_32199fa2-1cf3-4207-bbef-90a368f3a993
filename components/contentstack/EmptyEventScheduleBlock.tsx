import { Box, FlexLayout, Image, Text } from '@/ui';

interface EmptyEventScheduleBlockProps {
  imageUrl: string;
  imageAlt: string;
  bodyText: string;
  firstDay: string;
  secondDay: string;
  thirdDay: string;
  firstColumnFirstRow: string;
  firstColumnSecondRow: string;
  firstColumnThirdRow: string;
  secondColumnFirstRow: string;
  secondColumnSecondRow: string;
  secondColumnThirdRow: string;
  thirdColumnFirstRow: string;
  thirdColumnSecondRow: string;
  thirdColumnThirdRow: string;
}

export const EmptyEventScheduleBlock = ({
  imageUrl,
  imageAlt,
  bodyText,
  firstDay,
  secondDay,
  thirdDay,
  firstColumnFirstRow,
  firstColumnSecondRow,
  firstColumnThirdRow,
  secondColumnFirstRow,
  secondColumnSecondRow,
  secondColumnThirdRow,
  thirdColumnFirstRow,
  thirdColumnSecondRow,
  thirdColumnThirdRow,
}: EmptyEventScheduleBlockProps) => {
  return (
    <Box sx={{ position: 'relative', margin: '0 auto' }}>
      <Image
        src={imageUrl}
        alt={imageAlt}
        sx={{
          position: 'absolute',
          display: ['none', 'block'],
          width: [0, '280px', '380px'],
          height: [0, '280px', '380px'],
          right: [0, '-23px', '-160px'],
          top: [0, '-160px', '-250px'],
          zIndex: [0, 0, 1],
        }}
      />

      <FlexLayout
        mx="auto"
        color="white"
        flexDirection="column"
        sx={{
          maxWidth: '1090px',
          width: ['100%', '720px', '100%'],
          backgroundColor: 'white',
          position: 'relative',
          zIndex: [1, 1, 0],
        }}
        alignItems="center"
      >
        <Text
          as="p"
          textVariant={['paragraph-m-medium', 'paragraph-m-medium', 'paragraph-l-medium']}
          sx={{ maxWidth: '690px', textAlign: 'center' }}
          color="primary-midnight"
          p={[4, 8, 10]}
        >
          {bodyText}
        </Text>
        <Box sx={{ height: 1, width: '100%', backgroundColor: 'midnight20' }} />
        <FlexLayout
          sx={{ gap: [4, 4, 12], width: '100%' }}
          justifyContent="space-between"
          flexDirection={['column', 'row', 'row']}
        >
          <FlexLayout flexDirection={['column', 'column', 'row']} sx={{ width: '100%' }}>
            <ScheduleBlockItem
              title={firstDay}
              listItems={[firstColumnFirstRow, firstColumnSecondRow, firstColumnThirdRow]}
            />
            <Box
              sx={{
                height: [1, 1, '100%'],
                width: ['100%', '100%', 1],
                backgroundColor: 'midnight20',
                ':isLast': { display: 'none' },
              }}
            />
            <ScheduleBlockItem
              title={secondDay}
              listItems={[secondColumnFirstRow, secondColumnSecondRow, secondColumnThirdRow]}
            />
            <Box
              sx={{
                height: [1, 1, '100%'],
                width: ['100%', '100%', 1],
                backgroundColor: 'midnight20',
                ':isLast': { display: 'none' },
              }}
            />
            <ScheduleBlockItem
              title={thirdDay}
              listItems={[thirdColumnFirstRow, thirdColumnSecondRow, thirdColumnThirdRow]}
            />
          </FlexLayout>
        </FlexLayout>
      </FlexLayout>
    </Box>
  );
};

interface ScheduleBlockItemProps {
  title: string;
  listItems: string[];
}

export const ScheduleBlockItem = ({ title, listItems }: ScheduleBlockItemProps) => {
  return (
    <FlexLayout sx={{ gap: [2, 4] }} flexDirection="column" py={[6, 8, 10]} px={[4, 4, 8]}>
      <Text
        as="p"
        textVariant={['paragraph-s-medium', 'paragraph-m-medium']}
        sx={{ textAlign: ['start', 'start', 'center'] }}
        color="primary-midnight"
      >
        {title}
      </Text>
      <FlexLayout as="ul" flexDirection="column" space={1} px={[8, 10, 3]}>
        {listItems?.map((item) => (
          <Text as="li" color="midnight80" textVariant={['paragraph-xs-medium', 'paragraph-m-medium']}>
            {item}
          </Text>
        ))}
      </FlexLayout>
    </FlexLayout>
  );
};
