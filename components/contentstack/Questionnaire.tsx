'use client';
import { useRouter } from 'next/navigation';
import { useSession } from 'next-auth/react';
import { Controller, useForm } from 'react-hook-form';

import { updateUser } from '@/apis/user';
import { QuestionnaireProps } from '@/data/complete-profile';
import { Button, FlexLayout, Input, useScreenType } from '@/ui';
import { QuestionnaireUpdateSchema } from '@/utils';
import { TextWithLines } from '../shared';

export const Questionnaire = ({
  completeProfile,
  skipForNow,
  questionnaire,
  typeYourAnswerHere,
  // chooseLanguage,
  questionnaireQuestions,
  // questionnaireLanguages,
  initialAnswers,
  asModalOptions,
}: QuestionnaireProps) => {
  const router = useRouter();
  const { isMobile } = useScreenType();
  const { data: session, update: updateSession } = useSession();

  const {
    control,
    handleSubmit,
    formState: { isValid, isSubmitting },
  } = useForm({ mode: 'onChange' });

  const questionsArray = [
    { fieldName: 'socialMediaHandles', label: questionnaireQuestions[0] },
    { fieldName: 'gamerNameInfo', label: questionnaireQuestions[1] },
    { fieldName: 'celebrationInfo', label: questionnaireQuestions[2] },
    { fieldName: 'comingWithInfo', label: questionnaireQuestions[3] },
    { fieldName: 'playstyleDescription', label: questionnaireQuestions[4] },
    { fieldName: 'preparationInfo', label: questionnaireQuestions[5] },
    { fieldName: 'otherGamesInfo', label: questionnaireQuestions[6] },
    { fieldName: 'playerAdvice', label: questionnaireQuestions[7] },
    { fieldName: 'playerFeeling', label: questionnaireQuestions[8] },
  ];

  const onSubmit = async (data: QuestionnaireUpdateSchema) => {
    if (!session) return;

    try {
      await updateUser(session.user.id, data);
      await updateSession();

      if (asModalOptions?.onClose) {
        asModalOptions.onClose();
        router.refresh();
      } else {
        router.push('/dashboard');
      }
    } catch (error) {
      console.error('An error occured:', error);
    }
  };

  return (
    <FlexLayout
      py={8}
      px={6}
      space={12}
      flexDirection="column"
      backgroundColor="white"
      sx={{ width: '100%', maxWidth: '808px' }}
    >
      <FlexLayout flexDirection="column" space={8}>
        <TextWithLines upperCase text={questionnaire} hasLeftLine={false} color="midnight800" />
        <FlexLayout as="form" flexDirection="column" space={6}>
          {questionsArray.map((question) => (
            <Controller
              name={question.fieldName}
              control={control}
              key={question.fieldName}
              defaultValue={initialAnswers[question.fieldName]}
              render={({ field, fieldState }) => (
                <Input
                  type="text"
                  value={field.value || ''}
                  onChange={field.onChange}
                  onBlur={field.onBlur}
                  ref={field.ref}
                  isDisabled={isSubmitting}
                  name={question.fieldName}
                  labelLeft={question.label}
                  errorText={fieldState.error?.message}
                  placeholder={`${typeYourAnswerHere}...`}
                />
              )}
            />
          ))}
        </FlexLayout>
      </FlexLayout>
      <FlexLayout space={isMobile ? 4 : 8} mx="auto" flexDirection={isMobile ? 'column' : 'row'}>
        <Button
          fullWidth={isMobile}
          label={skipForNow}
          isDisabled={isSubmitting}
          as={asModalOptions?.onSkip ? 'button' : 'a'}
          variant="secondary"
          href={asModalOptions?.onSkip ? '' : '/dashboard'}
          onClick={asModalOptions?.onSkip}
          size={isMobile ? 'small' : 'large'}
        />
        <Button
          fullWidth={isMobile}
          onClick={handleSubmit(onSubmit)}
          isDisabled={isSubmitting || !isValid}
          label={completeProfile}
          size={isMobile ? 'small' : 'large'}
        />
      </FlexLayout>
    </FlexLayout>
  );
};
