import { useEffect, useState } from 'react';

import { ZoomableGraphicsEntry } from '@/interfaces';
import { Box, FlexLayout, Icon, Image, Text } from '@/ui';
import { ModalLayout } from '../layout/ModalLayout';

interface ZoomableGraphicsProps {
  data: ZoomableGraphicsEntry;
  onEnter?: () => void;
  onExit?: () => void;
}

export const ZoomableGraphics = (props: ZoomableGraphicsProps) => {
  const { data, onEnter, onExit } = props;

  const [isGraphicsOpened, setIsGraphicsOpened] = useState(false);
  const [isHovered, setIsHovered] = useState(false);

  useEffect(() => {
    if (isGraphicsOpened) {
      onEnter?.();
    } else {
      onExit?.();
    }
  }, [isGraphicsOpened]);

  return (
    <Box sx={{ width: '100%', display: 'inline-block', position: 'relative' }}>
      <Image
        src={data.asset.url}
        alt={data.asset.filename}
        width={1100}
        height={600}
        style={{ width: '100%', height: '100%' }}
      />
      <FlexLayout
        justifyContent="center"
        alignItems="center"
        flexDirection="column"
        onClick={() => setIsGraphicsOpened(true)}
        onMouseEnter={() => setIsHovered(true)}
        onMouseLeave={() => setIsHovered(false)}
        sx={{
          position: 'absolute',
          zIndex: 'overlay',
          width: '100%',
          height: '100%',
          background: 'black75',
          top: 0,
          left: 0,
          right: 0,
          bottom: 0,
          opacity: isHovered ? 1 : 0,
          cursor: 'pointer',
        }}
      >
        <Text textVariant="label-l-bold">Click to zoom</Text>
        <Icon icon="expand" size="xl" color="white" />
      </FlexLayout>
      <ModalLayout onClickOutside={() => setIsGraphicsOpened(false)} isOpen={isGraphicsOpened}>
        <Image
          width={1100}
          height={600}
          src={data.asset.url}
          alt={data.asset.title}
          style={{ width: '90dvw', height: 'auto' }}
        />
      </ModalLayout>
    </Box>
  );
};
