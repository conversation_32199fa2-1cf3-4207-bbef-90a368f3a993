import { FlexLayout, Text } from '@/ui';

interface ScheduleInfoBlockProps {
  title: string;
  date: string;
  location: string;
  description?: string;
}

export const ScheduleInfoBlock = ({ title, date, location, description }: ScheduleInfoBlockProps) => {
  return (
    <FlexLayout flexDirection="column" space={[3, 4]}>
      <Text upperCase textVariant={['h7', 'h6', 'h4']} as="h1" color="primary-midnight">
        {title}
      </Text>
      <FlexLayout flexDirection="column" space={3}>
        <FlexLayout space={3}>
          <Text textVariant={['paragraph-s-medium', 'paragraph-m-medium', 'paragraph-l-medium']} color="midnight80">
            {date}
          </Text>
          <Text textVariant={['paragraph-s-medium', 'paragraph-m-medium', 'paragraph-l-medium']} color="midnight80">
            •
          </Text>
          <Text textVariant={['paragraph-s-medium', 'paragraph-m-medium', 'paragraph-l-medium']} color="midnight80">
            {location}
          </Text>
        </FlexLayout>
        {description && (
          <Text
            as="p"
            color="primary-midnight"
            textVariant={['paragraph-s-medium', 'paragraph-m-medium', 'paragraph-l-medium']}
          >
            {description}
          </Text>
        )}
      </FlexLayout>
    </FlexLayout>
  );
};
