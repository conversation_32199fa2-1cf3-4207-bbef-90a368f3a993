import { Property } from 'csstype';

import { Box, Button, colors, FlexLayout, Image, Text, useScreenType } from '@/ui';
import { parseRichTextToReactComponents } from '@/utils';

interface InfoBlockProps {
  title: string;
  description: string;
  topImageUrl: string;
  topImageAlt: string;
  bottomImageUrl?: string;
  bottomImageAlt?: string;
  assetPosition: string;
  button: {
    showButton: boolean;
    isOffsite: boolean;
    link: {
      href: string;
      title: string;
      label: string;
    };
  }[];
  assetPositionMobile: string;
  textBelowButtons?: string;
}

export const InfoBlock = (data: InfoBlockProps) => {
  const { isDesktop } = useScreenType();

  const getLayoutDirection = (): Property.FlexDirection[] => {
    switch (data.assetPosition) {
      case 'left':
        return ['column-reverse', 'row-reverse'];
      default:
        return [data.assetPositionMobile === 'top' ? 'column-reverse' : 'column', 'row'];
    }
  };

  return (
    <Box px={[4, 8, 10]}>
      <FlexLayout flexDirection={getLayoutDirection()} space={[8, 0, 10]} alignItems="center" justifyContent="center">
        <FlexLayout
          flexDirection="column"
          space={[3, 3, 8]}
          flex={1}
          alignItems="center"
          sx={{ maxWidth: [328, '100%', 650] }}
        >
          <Text
            as="h1"
            textVariant={['h6', 'h6', 'h3']}
            upperCase
            isCentered={true}
            sx={{ maxWidth: 649 }}
            color="primary-midnight"
          >
            {data.title}
          </Text>
          <Box
            sx={{
              maxWidth: ['100%', '100%', 620],
              alignSelf: 'center',
              color: colors.midnight80,
              textAlign: 'center',
            }}
          >
            {parseRichTextToReactComponents(data.description, {
              textAlign: 'center',
            })}
          </Box>
          <FlexLayout justifyContent="center">
            {data.button.map((el) => {
              if (el.showButton) {
                return (
                  <Button
                    label={el.link.title}
                    as="a"
                    href={el.link.href}
                    key={el.link.title}
                    target={el.isOffsite ? '_new' : '_self'}
                    size={isDesktop ? 'large' : 'medium'}
                    glow
                    backgroundSize="110%"
                  />
                );
              }

              return null;
            })}
          </FlexLayout>
          {data?.textBelowButtons && (
            <Text color="gray600" isCentered textVariant="label-l-regular">
              {data.textBelowButtons}
            </Text>
          )}
        </FlexLayout>
        {data.topImageUrl && (
          <FlexLayout
            flex={1}
            justifyContent="center"
            sx={{ position: 'relative', maxWidth: ['none', 377, 790], height: ['none', 308, 679] }}
          >
            <Image
              src={data.topImageUrl}
              alt={data.topImageAlt}
              width="100%"
              height="100%"
              sx={{ zIndex: 1, maxWidth: [320, 320, 603], maxHeight: [320, 320, 616] }}
            />
            {data.bottomImageUrl && (
              <Image
                src={data.bottomImageUrl}
                alt={data.bottomImageAlt}
                width="100%"
                height="100%"
                sx={{
                  maxWidth: ['none', 377, 790],
                  maxHeight: ['none', 160, 336],
                  position: 'absolute',
                  bottom: [-10, 0, 0],
                }}
              />
            )}
          </FlexLayout>
        )}
      </FlexLayout>
    </Box>
  );
};
