import { colors, FlexLayout, Text } from '@/ui';
// import { PaddingContainer } from '../shared';
import { Accordion } from '../shared/Accordion';

interface FAQProps {
  title: string;
  items?: {
    title: string;
    content: unknown;
  }[];
  maxWidth?: string;
  withBorder?: boolean;
  sx?: React.CSSProperties;
  backgroundImage?: string;
}

export const FAQ = ({ title, items = [], maxWidth, withBorder = true, sx = {}, backgroundImage }: FAQProps) => {
  return (
    <FlexLayout justifyContent="center" sx={{ width: '100%' }}>
      <FlexLayout
        flexDirection="column"
        space={[0, 6, 6]}
        px={[4, 10]}
        pt={[10, 15]}
        pb={[0, 5]}
        sx={{
          maxWidth,
          width: '100%',
          border: withBorder && `8px solid ${colors['primary-sunrise']}`,
          backgroundImage: `url(${backgroundImage || ''})`,
          backgroundRepeat: 'no-repeat',
          backgroundSize: ['200% auto', '100% auto', '100% auto'],
          backgroundPosition: 'center top',
          ...sx,
        }}
      >
        <Text as="h1" textVariant={['h6', 'h6', 'h3']} isCentered upperCase color="primary-midnight">
          {title}
        </Text>
        <FlexLayout flexDirection="column" alignItems="center">
          {items.map((item, index) => (
            <Accordion key={index} index={index} borderColor="midnight20" {...item} />
          ))}
        </FlexLayout>
      </FlexLayout>
    </FlexLayout>
  );
};
