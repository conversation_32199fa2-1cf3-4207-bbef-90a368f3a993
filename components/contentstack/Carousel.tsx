import { useRef } from 'react';
import Slider, { Settings } from 'react-slick';

import { Box, Button, Image, useScreenType } from '@/ui';
import { ZoomableGraphics } from './ZoomableGraphics';

import 'slick-carousel/slick/slick.css';
import 'slick-carousel/slick/slick-theme.css';
import '@/styles/homepage-carousel.css';

interface CarouselArrowProps {
  isPrevious?: boolean;
  onClick: () => void;
}

const CarouselArrow = (props: CarouselArrowProps) => {
  const { isTablet, isDesktop } = useScreenType();

  const { onClick, isPrevious } = props;

  return (
    <Box
      sx={{
        position: 'absolute',
        top: '45%',
        transform: 'translateY(-45%)',
        left: isPrevious ? ['10px', '10px', '-25px'] : 'unset',
        right: isPrevious ? 'unset' : ['10px', '10px', '-25px'],
        zIndex: 'carousel-arrows',
        cursor: 'pointer',
      }}
    >
      <Button
        onClick={onClick}
        label=""
        icon={isPrevious ? 'chevronLeft' : 'chevronRight'}
        iconSize={isDesktop ? 'm' : isTablet ? 's' : 'xs'}
        size={isDesktop ? 'large' : isTablet ? 'medium' : 'small'}
        isSkewedOnLeft={isPrevious}
        sx={{ px: [4, 4, 6] }}
      />
    </Box>
  );
};

interface CarouselProps {
  autoplay: boolean;
  autoplayTimeMs: number;
  items: {
    imageAlt: string;
    imageUrl: string;
  }[];
  itemsMobile: {
    imageAlt: string;
    imageUrl: string;
  }[];
}

export const Carousel = (props: CarouselProps) => {
  const sliderRef = useRef<Slider | null>(null);

  const { isMobile } = useScreenType();

  const { items, itemsMobile, autoplay, autoplayTimeMs } = props;

  const settings: Settings = {
    centerMode: true,
    adaptiveHeight: true,
    centerPadding: '16px',
    infinite: true,
    slidesToShow: 1,
    speed: 500,
    autoplaySpeed: autoplayTimeMs ?? 5000,
    autoplay,
    dots: true,
    arrows: false,
    swipe: true,
    customPaging: () => (
      <Box
        sx={{
          width: '8px',
          height: '8px',
          borderRadius: '50%',
          background: 'white',
        }}
      />
    ),
  };

  const onNextClick = () => {
    if (sliderRef.current) {
      sliderRef.current.slickNext();
    }
  };

  const onPreviousClick = () => {
    if (sliderRef.current) {
      sliderRef.current.slickPrev();
    }
  };

  return (
    <Box mt={[15, 25, 40]} sx={{ overflowX: 'hidden' }}>
      <Box
        mx="auto"
        sx={{
          maxWidth: '1100px',
          position: 'relative',
          overflow: 'visible',
          width: '100%',
        }}
      >
        <CarouselArrow isPrevious onClick={onPreviousClick} />
        <Slider {...settings} ref={sliderRef}>
          {(isMobile ? itemsMobile : items).map((item) =>
            isMobile ? (
              <ZoomableGraphics
                data={{
                  title: item.imageAlt,
                  asset: {
                    title: item.imageAlt,
                    uid: item.imageUrl,
                    url: item.imageUrl,
                    filename: item.imageAlt,
                    filesize: '',
                    content_type: '',
                  },
                }}
                key={item.imageUrl}
                onEnter={() => {
                  sliderRef.current?.slickPause();
                }}
                onExit={() => {
                  sliderRef.current?.slickPlay();
                }}
              />
            ) : (
              <Box key={item.imageUrl} sx={{ width: '100%', display: 'inline-block', position: 'relative' }}>
                <Image
                  src={item.imageUrl}
                  alt={item.imageAlt}
                  width={1200}
                  height={674}
                  style={{ width: '100%', height: '100%', aspectRatio: '16/9' }}
                />
              </Box>
            ),
          )}
        </Slider>
        <CarouselArrow onClick={onNextClick} />
      </Box>
    </Box>
  );
};
