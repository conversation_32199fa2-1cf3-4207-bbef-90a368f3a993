import { HomepageHeroProps } from '@/data/homepage';
import { Box, colors, FlexLayout, Text } from '@/ui';

const Divider = () => <Box sx={{ height: 1, width: 120, backgroundColor: colors['primary-dusk'] }} />;

type TicketsSaleInfoProps = Pick<
  Partial<HomepageHeroProps>,
  'firstRow' | 'secondRow' | 'thirdRow' | 'fourthRow' | 'fifthRow' | 'sixthRow'
>;

export const TicketsSaleInfo = ({
  firstRow,
  secondRow,
  thirdRow,
  fourthRow,
  fifthRow,
  sixthRow,
}: TicketsSaleInfoProps) => {
  return (
    <FlexLayout flexDirection="column" alignItems="center" sx={{ gap: 4 }} px={4}>
      {firstRow && (
        <Text
          isCentered
          color="primary-midnight"
          textVariant={['paragraph-xs-medium', 'paragraph-s-medium', 'paragraph-s-medium']}
        >
          {firstRow}
        </Text>
      )}
      {secondRow && (
        <Text
          isCentered
          color="primary-midnight"
          textVariant={['paragraph-xs-medium', 'paragraph-s-medium', 'paragraph-s-medium']}
          sx={{ fontWeight: 600 }}
        >
          {secondRow}
        </Text>
      )}
      {thirdRow && (
        <Text isCentered variant="paragraph-xs-medium" color="primary-midnight">
          {thirdRow}
        </Text>
      )}
      {fourthRow && (
        <>
          <Divider />
          <Text isCentered variant="paragraph-xs-medium" color="primary-midnight">
            {fourthRow}
          </Text>
        </>
      )}
      {fifthRow && (
        <>
          <Divider />
          <Text isCentered variant="paragraph-xs-medium" color="primary-midnight">
            {fifthRow}
          </Text>
        </>
      )}
      {sixthRow && (
        <>
          <Divider />
          <Text isCentered variant="paragraph-xs-medium" color="primary-midnight">
            {sixthRow}
          </Text>
        </>
      )}
    </FlexLayout>
  );
};
