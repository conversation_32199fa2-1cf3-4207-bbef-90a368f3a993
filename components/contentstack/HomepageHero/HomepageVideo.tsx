'use client';

import { useSearchParams } from 'next/navigation';

import { Box } from '@/ui';

export const HomepageVideo = ({ type }: { type: 'youtube' | 'twitch' }) => {
  const params = useSearchParams();
  const isTwitchParam = params.get('useTwitch') === 'true';

  const videoUrl =
    type === 'twitch' || isTwitchParam
      ? 'https://player.twitch.tv/?channel=teamfighttactics&parent=localhost&parent=qa.paris.competetft.com&parent=paris.competetft.com&autoplay=true&muted=true'
      : 'https://www.youtube.com/embed/6X7w9Ug8J0M?si=lnqpelbeleZdkB48?&autoplay=1&mute=1';

  return (
    <Box sx={{ width: '100%', maxWidth: 1100, height: 600 }}>
      <iframe
        style={{ border: 0 }}
        width="100%"
        height="100%"
        src={videoUrl}
        title="TFT Paris Open Live Stream"
        allow="autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture; web-share"
        referrerPolicy="strict-origin-when-cross-origin"
        allowFullScreen
      />
    </Box>
  );
};
