import { BlocksRenderer } from '@strapi/blocks-react-renderer';
import { useMemo } from 'react';
import { ThemeUICSSObject } from 'theme-ui';

import { colors, Text } from '@/ui';

interface HeroTitleProps {
  title: unknown | string;
}

const titleStyle = {
  textTransform: 'uppercase',
  color: colors['secondary-lemonade'],
  py: 4,
  px: [2, 2, 8],
  backgroundColor: colors['primary-dusk'],
  textAlign: 'center',
} as ThemeUICSSObject;

export const HeroTitle = ({ title }: HeroTitleProps) => {
  if (!title) {
    return null;
  }

  if (typeof title === 'string') {
    return (
      <Text textVariant="h7" sx={titleStyle}>
        {title}
      </Text>
    );
  }

  return useMemo(
    () => (
      <BlocksRenderer
        // eslint-disable-next-line @typescript-eslint/no-explicit-any
        content={title as any}
        modifiers={{
          bold: ({ children }) => (
            <Text
              textVariant="h7"
              sx={{
                textTransform: 'uppercase',
                color: colors['secondary-lilac'],
              }}
            >
              {children}
            </Text>
          ),
        }}
        blocks={{
          paragraph: ({ children }) => (
            <Text textVariant="h7" sx={titleStyle}>
              {children}
            </Text>
          ),
        }}
      />
    ),
    [title],
  );
};
