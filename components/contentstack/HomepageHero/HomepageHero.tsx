import {
  NoTicketsAvailableATM,
  SpectatorPassesOnSale,
  TechnicalDifficulties,
  TicketPurchased,
  TicketsOnSale,
  TicketsSaleCountdown,
  TicketsSoldOut,
} from '@/components/contentstack/HomepageHero/StateComponents';
import { HeroBannerState, useHeroState } from '@/components/contentstack/HomepageHero/StateComponents/hooks';
import { HomepageHeroProps } from '@/data/homepage';
import { FlexLayout, Image } from '@/ui';
import { HomepageHeroWrapper } from './HomepageHeroWrapper';
// import { HomepageVideo } from './HomepageVideo';

const stateComponentMap = {
  [HeroBannerState.TicketPurchased]: TicketPurchased,
  [HeroBannerState.TicketsSoldOut]: TicketsSoldOut,
  [HeroBannerState.NoTicketsAvailableATM]: NoTicketsAvailableATM,
  [HeroBannerState.TicketsOnSale]: TicketsOnSale,
  [HeroBannerState.TicketsSaleCountdown]: TicketsSaleCountdown,
  [HeroBannerState.SpectatorPassesOnSale]: SpectatorPassesOnSale,
  [HeroBannerState.TechnicalDifficulties]: TechnicalDifficulties,
};

export const HomepageHero = ({
  date,
  location,
  imageUrl,
  imageAlt,
  backgroundImage,
  // watchLive,
  ...restCmsData
}: HomepageHeroProps) => {
  const heroState = useHeroState();
  const StateComponent = stateComponentMap[heroState];

  // const type = 'twitch' as 'youtube' | 'twitch';
  return (
    <HomepageHeroWrapper
      date={date}
      location={location}
      imageUrl={imageUrl}
      imageAlt={imageAlt}
      backgroundImage={backgroundImage}
    >
      <FlexLayout alignItems="center" justifyContent="space-between" sx={{ maxWidth: '1200px' }} mt={[0, 30]}>
        <Image
          src="/images/fili-left.png"
          alt="Fili character left"
          sx={{
            position: ['relative', 'absolute', 'relative'],
            left: ['0', '-150px', '0'],
            display: ['none', 'block', 'block'],
            flexShrink: 0,
          }}
        />
        <StateComponent {...restCmsData} />
        <Image
          src="/images/fili-right.png"
          alt="Fili character right"
          sx={{
            position: ['relative', 'absolute', 'relative'],
            right: ['0', '-150px', '0'],
            display: ['none', 'block', 'block'],
            flexShrink: 0,
          }}
        />
      </FlexLayout>
      {/* <FlexLayout justifyContent={'center'} sx={{ position: 'relative', width: '100%', mt: 20 }}>
        {type === 'twitch' && (
          <Box pt={3} pb={4} px={8} mt={-10} sx={{ position: 'absolute', color: 'white', bg: 'midnight700' }}>
            <Text variant="h4">{watchLive}:</Text>
          </Box>
        )}
        <HomepageVideo type={type} />
      </FlexLayout> */}
    </HomepageHeroWrapper>
  );
};
