'use client';

import { useEffect, useState } from 'react';

import { NoSsr } from '@/components/shared/ NoSsr';
import { useSiteConfig } from '@/context/SiteConfig';
import { Box, colors, FlexLayout, Text } from '@/ui';

interface CountdownTimerProps {
  saleStartDate: Date;
}

export const CountdownTimer = ({ saleStartDate }: CountdownTimerProps) => {
  const siteConfig = useSiteConfig();
  const { localeJSON } = siteConfig;

  const [timeUntilSale, setTimeUntilSale] = useState({ days: 0, hours: 0, minutes: 0, seconds: 0 });

  useEffect(() => {
    const targetDate = saleStartDate;

    setTimeUntilSale(timeUntil(targetDate));

    const interval = setInterval(() => {
      setTimeUntilSale(timeUntil(targetDate));
    }, 1000);

    return () => clearInterval(interval);
  }, []);

  return (
    <FlexLayout justifyContent="center">
      {timeUntilSale.days > 0 && (
        <>
          <Tile label={localeJSON['tDays'] ?? 'days'}>{timeUntilSale.days}</Tile>
          <Semicolon />
        </>
      )}
      <Tile label={localeJSON['tHours'] ?? 'hours'}>{timeUntilSale.hours}</Tile>
      <Semicolon />
      <Tile label={localeJSON['tMinutes'] ?? 'minutes'}>{timeUntilSale.minutes}</Tile>
      <Semicolon />
      <Tile label={localeJSON['tSeconds'] ?? 'seconds'}>{timeUntilSale.seconds}</Tile>
    </FlexLayout>
  );
};

function Semicolon() {
  return (
    <Box
      sx={{
        fontSize: [32, 32, 48],
        minWidth: [16, 16, 32],
        textAlign: 'center',
        fontWeight: 900,
        color: colors['primary-dusk'],
        fontFamily: 'Transducer',
        position: 'relative',
        top: ['4px', '12px', '28px'],
      }}
    >
      :
    </Box>
  );
}

// eslint-disable-next-line @typescript-eslint/no-explicit-any
function Tile({ children, label }: any) {
  return (
    <FlexLayout
      flexDirection="column"
      alignItems="center"
      justifyContent="center"
      sx={{ minWidth: [72, 88, 172], minHeight: [68, 88, 132], backgroundColor: colors['primary-dusk'] }}
    >
      <NoSsr>
        <Text color="secondary-lemonade" textVariant={['h4', 'h3', 'h2']}>
          {children}
        </Text>
        <Text
          color="lemonade50"
          textVariant="label-s-countdown"
          sx={{ textTransform: 'uppercase', fontSize: [10, 10, 12] }}
        >
          {label}
        </Text>
      </NoSsr>
    </FlexLayout>
  );
}

function timeUntil(targetDate: Date) {
  const now = new Date();
  const timeDifference = targetDate.getTime() - now.getTime();

  if (timeDifference < 0) {
    return {
      days: 0,
      hours: 0,
      minutes: 0,
      seconds: 0,
    };
  }

  return {
    seconds: Math.floor((timeDifference / 1000) % 60),
    minutes: Math.floor((timeDifference / (1000 * 60)) % 60),
    hours: Math.floor((timeDifference / (1000 * 60 * 60)) % 24),
    days: Math.floor(timeDifference / (1000 * 60 * 60 * 24)),
  };
}
