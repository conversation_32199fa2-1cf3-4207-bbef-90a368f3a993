/* eslint-disable @typescript-eslint/no-unused-vars */
'use client';

import { PropsWithChildren } from 'react';

import { HomepageHeroProps } from '@/data/homepage';
import { FlexLayout, Image, Text, useScreenType } from '@/ui';
import { ConditionalWrapper, PaddingContainer } from '../../shared';

type HomepageHeroWrapperProps = Pick<
  HomepageHeroProps,
  'date' | 'location' | 'imageUrl' | 'imageAlt' | 'backgroundImage'
>;

export const HomepageHeroWrapper = ({
  date,
  location,
  imageUrl,
  imageAlt,
  backgroundImage = '',
  children,
}: PropsWithChildren<HomepageHeroWrapperProps>) => {
  const { isMobile } = useScreenType();

  return (
    <ConditionalWrapper
      sx={{
        backgroundImage: ['none', `url(${backgroundImage})`],
        backgroundRepeat: 'no-repeat',
        backgroundSize: ['unset', 'contain'],
        backgroundPosition: ['center', 'top, center'],
      }}
      condition={!isMobile}
      wrapper={(children) => <PaddingContainer>{children}</PaddingContainer>}
    >
      <FlexLayout pt={[10, 14]} pb={[25, 25, 35]} flexDirection="column" sx={{ position: 'relative' }} px={[4, 0, 0]}>
        <FlexLayout flexDirection="column" justifyContent="center" alignItems="center" sx={{ width: '100%' }}>
          <FlexLayout
            flexDirection="column"
            justifyContent="center"
            alignItems="center"
            space={[3, 0]}
            sx={{ width: '100%', maxWidth: 600 }}
          >
            <Image src={imageUrl} alt={imageAlt} sx={{ width: [260, 300, 'auto'] }} />
            {/* <FlexLayout flexDirection={['column', 'column', 'row']} space={[2, 2, 3]} alignItems="center">
              <Text color="paleYellow500" textVariant="label-logo">
                {date}
              </Text>
              <Text color="paleYellow500" textVariant="label-logo" sx={{ display: ['none', 'none', 'flex'] }}>
                •
              </Text>
              <Text color="paleYellow500" textVariant="label-logo">
                {location}
              </Text>
            </FlexLayout> */}
          </FlexLayout>
          {children}
        </FlexLayout>
      </FlexLayout>
    </ConditionalWrapper>
  );
};
