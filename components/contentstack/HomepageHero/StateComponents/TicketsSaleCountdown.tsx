'use client';

import { signIn } from 'next-auth/react';

import { useDropInfo } from '@/context/DropInfoProvider';
import { useSiteConfig } from '@/context/SiteConfig';
import { HomepageHeroProps } from '@/data/homepage';
import { useCurrentLocale } from '@/hooks';
import { Button, colors, FlexLayout, Text } from '@/ui';
import { CountdownTimer } from '../CountdownTimer';
import { HeroTitle } from '../HeroTitle';
import { TicketsSaleInfo } from '../TicketsSaleInfo';
import { UserRank } from '../utils';

export type TicketsSaleCountdownProps = Pick<
  HomepageHeroProps,
  | 'heroTitles'
  | 'firstRow'
  | 'secondRow'
  | 'thirdRow'
  | 'fourthRow'
  | 'fifthRow'
  | 'sixthRow'
  | 'login'
  | 'navDashboard'
  | 'heroLoginLabel'
>;

export const TicketsSaleCountdown = ({
  heroTitles,
  firstRow,
  secondRow,
  thirdRow,
  fourthRow,
  fifthRow,
  sixthRow,
  login,
  navDashboard,
  heroLoginLabel,
}: TicketsSaleCountdownProps) => {
  const { featureFlags } = useSiteConfig();

  const { general, user } = useDropInfo();
  const saleStartDate = user ? user.saleStartTimeForRank : general?.saleStartTimes['tactitians_crown'] || new Date();

  const heroTitle = getCountdownHeroTitle(heroTitles);

  const locale = useCurrentLocale();
  const isEnLocale = locale === 'en';

  return (
    <FlexLayout flexDirection="column" sx={{ maxWidth: 1110, width: '100%', py: 12, mt: 20 }}>
      <FlexLayout flexDirection="column" sx={{ zIndex: 1 }} alignItems="center">
        {!!heroTitle && (
          <FlexLayout
            alignItems="center"
            justifyContent="center"
            sx={{
              backgroundColor: colors['primary-dusk'],
              minHeight: 68,
              position: 'relative',
              top: '-34px',
            }}
          >
            <HeroTitle title={heroTitle} />
          </FlexLayout>
        )}
        <FlexLayout alignItems="center" flexDirection="column" space={12}>
          <CountdownTimer saleStartDate={saleStartDate} />
          {!user ? (
            featureFlags.isLoginEnabled && (
              <FlexLayout flexDirection="column" alignItems="center" space={6}>
                <Text color="primary-midnight" sx={{ textAlign: 'center', px: 4 }}>
                  {heroLoginLabel}
                </Text>
                <Button
                  label={login}
                  onClick={() => signIn('rso', { callbackUrl: isEnLocale ? '/dashboard' : `/${locale}/dashboard` })}
                />
              </FlexLayout>
            )
          ) : (
            <Button label={navDashboard} as="a" href={'/dashboard'} />
          )}
          <TicketsSaleInfo
            firstRow={firstRow}
            secondRow={secondRow}
            thirdRow={thirdRow}
            fourthRow={fourthRow}
            fifthRow={fifthRow}
            sixthRow={sixthRow}
          />
        </FlexLayout>
      </FlexLayout>
    </FlexLayout>
  );
};

function getCountdownHeroTitle(heroTitles: HomepageHeroProps['heroTitles']) {
  const { user } = useDropInfo();

  if (!user) {
    return heroTitles.countdownHeroTitle;
  } else {
    switch (user.rank) {
      case UserRank.TactitiansCrown:
        return heroTitles.countdownHeroTitleTactitiansCrown;
      case UserRank.Challenger:
        return heroTitles.countdownHeroTitleChallenger;
      case UserRank.Grandmaster:
      case UserRank.Master:
        return heroTitles.countdownHeroTitleMaster;
      default:
        return heroTitles.competitorBundlesGoOnSaleIn;
    }
  }
}
