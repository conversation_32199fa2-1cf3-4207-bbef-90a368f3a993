import { HomepageHeroProps } from '@/data/homepage';
import { FlexLayout, Image, Text } from '@/ui';

type TechnicalDifficultiesProps = Pick<
  HomepageHeroProps,
  'competitorBundlesTechnicalDifficultiesTitle' | 'competitorBundlesTechnicalDifficultiesBody'
>;

export const TechnicalDifficulties = ({
  competitorBundlesTechnicalDifficultiesTitle,
  competitorBundlesTechnicalDifficultiesBody,
}: TechnicalDifficultiesProps) => {
  return (
    <FlexLayout flexDirection="column" alignItems="center">
      <Image src="/images/hero-state-secondary-eyes.png" />
      <FlexLayout
        flexDirection="column"
        space={6}
        p={6}
        bg="midnight50"
        sx={{
          width: ['100%', '586px'],
          borderWidth: '1px',
          borderStyle: 'dashed',
          borderColor: 'secondary-lilac',
        }}
        alignItems="center"
        mt={-10}
      >
        <Text isCentered textVariant="h4" color="primary-midnight" upperCase>
          {competitorBundlesTechnicalDifficultiesTitle}
        </Text>
        <Text isCentered color="midnight80" textVariant="paragraph-m-medium">
          {competitorBundlesTechnicalDifficultiesBody}
        </Text>
      </FlexLayout>
    </FlexLayout>
  );
};
