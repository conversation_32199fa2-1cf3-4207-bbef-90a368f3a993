import { HomepageHeroProps } from '@/data/homepage';
import { Button, FlexLayout, Image, Text } from '@/ui';

type TicketPurchasedProps = Pick<HomepageHeroProps, 'navDashboard' | 'ticketPurchasedDashboardDescription'>;

export const TicketPurchased = ({ navDashboard, ticketPurchasedDashboardDescription }: TicketPurchasedProps) => {
  return (
    <FlexLayout flexDirection="column" alignItems="center">
      <Image src="/images/hero-state-secondary.png" sx={{ width: '420px', height: '340px' }} />
      <FlexLayout
        flexDirection="column"
        space={6}
        p={8}
        bg="midnight50"
        sx={{
          width: ['100%', '640px', '720px'],
          borderWidth: '1px',
          borderStyle: 'dashed',
          borderColor: 'secondary-lilac',
        }}
        alignItems="center"
        mt={-16}
      >
        <Text textVariant="h4" color="primary-midnight">
          Dashboard
        </Text>
        <Text isCentered color="midnight80" textVariant="paragraph-m-medium">
          {ticketPurchasedDashboardDescription}
        </Text>
        <Button label={navDashboard} as="a" href="/dashboard" />
      </FlexLayout>
    </FlexLayout>
  );
};
