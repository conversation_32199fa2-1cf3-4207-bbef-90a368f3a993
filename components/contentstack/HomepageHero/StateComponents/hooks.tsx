import { useDropInfo } from '@/context/DropInfoProvider';
import { useSiteConfig } from '@/context/SiteConfig';
import { TicketPurchaseState } from '@/enums/drops';

export enum HeroBannerState {
  TicketPurchased = 'TicketPurchased',
  TicketsSoldOut = 'TicketsSoldOut',
  NoTicketsAvailableATM = 'NoTicketsAvailableATM',
  TicketsOnSale = 'TicketsOnSale',
  TicketsSaleCountdown = 'TicketsSaleCountdown',
  SpectatorPassesOnSale = 'SpectatorPassesOnSale',
  TechnicalDifficulties = 'TechnicalDifficulties',
}

export function useHeroState() {
  const { general, user } = useDropInfo();
  const { featureFlags } = useSiteConfig();

  if (user && user.state === TicketPurchaseState.Purchased) {
    return HeroBannerState.TicketPurchased;
  }

  if (!featureFlags.isPurchaseEnabled) {
    return HeroBannerState.TechnicalDifficulties;
  }

  if (featureFlags.areSpectatorPassesOnSale) {
    return HeroBannerState.SpectatorPassesOnSale;
  }

  if (general?.ticketFlags.soldOut) {
    return HeroBannerState.TicketsSoldOut;
  }

  if (general?.ticketFlags.waiting && user?.state !== TicketPurchaseState.Reserved) {
    return HeroBannerState.NoTicketsAvailableATM;
  }

  // Logged in user
  if (user) {
    if (user.saleStartedForRank) {
      return HeroBannerState.TicketsOnSale;
    } else {
      return HeroBannerState.TicketsSaleCountdown;
    }
    // Logged out user
  } else {
    if (general?.saleStarted) {
      return HeroBannerState.TicketsOnSale;
    } else {
      return HeroBannerState.TicketsSaleCountdown;
    }
  }
}
