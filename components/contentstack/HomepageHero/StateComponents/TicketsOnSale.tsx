'use client';

import { signIn } from 'next-auth/react';

import { useDropInfo } from '@/context/DropInfoProvider';
import { useSiteConfig } from '@/context/SiteConfig';
import { HomepageHeroProps } from '@/data/homepage';
import { useCurrentLocale } from '@/hooks';
import { Button, colors, FlexLayout, Image } from '@/ui';
import { HeroTitle } from '../HeroTitle';
import { TicketsSaleInfo } from '../TicketsSaleInfo';
import { UserRank } from '../utils';

export type TicketsOnSaleProps = Pick<
  HomepageHeroProps,
  'heroTitles' | 'firstRow' | 'secondRow' | 'thirdRow' | 'fourthRow' | 'fifthRow' | 'sixthRow' | 'buyCompetitorBundle'
>;

export const TicketsOnSale = ({
  heroTitles,
  buyCompetitorBundle,
  firstRow,
  secondRow,
  thirdRow,
  fourthRow,
  fifthRow,
  sixthRow,
}: TicketsOnSaleProps) => {
  const { featureFlags } = useSiteConfig();

  const { user } = useDropInfo();

  const locale = useCurrentLocale();
  const isEnLocale = locale === 'en';

  const heroTitle = getHeroTitle(heroTitles);

  return (
    <FlexLayout flexDirection="column" sx={{ maxWidth: 1110, width: '100%', py: 12, mt: 20 }} alignItems="center">
      <Image src="/images/hero-state-secondary.png" sx={{ width: '420px', height: '340px' }} />
      <FlexLayout flexDirection="column" sx={{ zIndex: 1 }} alignItems="center" space={12} mt={-20}>
        {!!heroTitle && (
          <FlexLayout
            alignItems="center"
            justifyContent="center"
            px={8}
            sx={{
              backgroundColor: colors['primary-dusk'],
              minHeight: 68,
            }}
          >
            <HeroTitle title={heroTitle} />
          </FlexLayout>
        )}
        <FlexLayout alignItems="center" flexDirection="column" space={12}>
          {!user ? (
            featureFlags.isLoginEnabled && (
              <Button
                label={buyCompetitorBundle}
                onClick={() => signIn('rso', { callbackUrl: isEnLocale ? '/dashboard' : `/${locale}/dashboard` })}
              />
            )
          ) : (
            <Button label={buyCompetitorBundle} as="a" href="/competitor-bundle" />
          )}
          <TicketsSaleInfo
            firstRow={firstRow}
            secondRow={secondRow}
            thirdRow={thirdRow}
            fourthRow={fourthRow}
            fifthRow={fifthRow}
            sixthRow={sixthRow}
          />
        </FlexLayout>
      </FlexLayout>
    </FlexLayout>
  );
};

function getHeroTitle(heroTitles: HomepageHeroProps['heroTitles']) {
  const { user, general } = useDropInfo();

  if (!user) {
    switch (true) {
      case general?.currentPeriod?.allowed_ranks[UserRank.Other]:
        return heroTitles.competitorBundlesAreOnSale;
      case general?.currentPeriod?.allowed_ranks[UserRank.Grandmaster]:
      case general?.currentPeriod?.allowed_ranks[UserRank.Master]:
        return heroTitles.heroTitleMaster;
      case general?.currentPeriod?.allowed_ranks[UserRank.Challenger]:
        return heroTitles.heroTitleChallenger;
      case general?.currentPeriod?.allowed_ranks[UserRank.TactitiansCrown]:
        return heroTitles.heroTitleTactitiansCrown;
      default:
        return heroTitles.competitorBundlesAreOnSale;
    }
  } else {
    switch (user.rank) {
      case UserRank.TactitiansCrown:
        return heroTitles.heroTitleTactitiansCrown;
      case UserRank.Challenger:
        return heroTitles.heroTitleChallenger;
      case UserRank.Grandmaster:
      case UserRank.Master:
        return heroTitles.heroTitleMaster;
      default:
        return heroTitles.competitorBundlesAreOnSale;
    }
  }
}
