import { HomepageHeroProps } from '@/data/homepage';
import { FlexLayout, Image, Text } from '@/ui';

type NoTicketsAvailableATMProps = Pick<
  HomepageHeroProps,
  'competitorBundleSoldOutTitle' | 'competitorBundleSoldOutBody'
>;

export const NoTicketsAvailableATM = ({
  competitorBundleSoldOutTitle,
  competitorBundleSoldOutBody,
}: NoTicketsAvailableATMProps) => {
  return (
    <FlexLayout flexDirection="column" alignItems="center">
      <Image src="/images/hero-state-secondary-eyes.png" />
      <FlexLayout
        flexDirection="column"
        space={6}
        p={8}
        bg="midnight50"
        sx={{
          width: ['100%', '640px', '720px'],
          borderWidth: '1px',
          borderStyle: 'dashed',
          borderColor: 'secondary-lilac',
        }}
        alignItems="center"
        mt={-10}
      >
        <Text isCentered textVariant="h4" color="primary-midnight" upperCase>
          {competitorBundleSoldOutTitle}
        </Text>
        <Text isCentered color="midnight80" textVariant="paragraph-m-medium">
          {competitorBundleSoldOutBody}
        </Text>
      </FlexLayout>
    </FlexLayout>
  );
};
