import { HomepageHeroProps } from '@/data/homepage';
import { FlexLayout, Icon, Text } from '@/ui';
import { HeroTitle } from '../HeroTitle';

export type SpectatorPassesOnSaleProps = Pick<HomepageHeroProps, 'heroTitles' | 'spectatorPassesHeroBody'>;

export const SpectatorPassesOnSale = ({ heroTitles, spectatorPassesHeroBody }: SpectatorPassesOnSaleProps) => {
  return (
    <FlexLayout
      flexDirection="column"
      sx={{
        maxWidth: 1110,
        width: '100%',
        pt: 20,
        pb: 18,
        mt: 20,
        position: 'relative',
      }}
    >
      <FlexLayout flexDirection="column" alignItems="center">
        <FlexLayout
          alignItems="center"
          justifyContent="center"
          sx={{
            minHeight: 68,
            position: 'absolute',
            top: '-34px',
          }}
        >
          <HeroTitle title={heroTitles.spectatorPassesHeroTitle} />
        </FlexLayout>
        <FlexLayout alignItems="center" flexDirection="column" space={3}>
          <Text variant="paragraph-l-medium" color="white" sx={{ textAlign: 'center', px: 3 }}>
            {spectatorPassesHeroBody}
          </Text>
          <Icon
            icon="chevronsDown"
            color="purpleLavander500"
            size="l54"
            sx={{ cursor: 'pointer' }}
            onClick={() => {
              const element = document.getElementById('spectatorPass');
              element?.scrollIntoView({ behavior: 'smooth', block: 'center' });
            }}
          />
        </FlexLayout>
      </FlexLayout>
    </FlexLayout>
  );
};
