import { Box, Color, FlexLayout, Text, TextVariant } from '@/ui';

interface TextWithLinesProps {
  color?: Color;
  hasLeftLine?: boolean;
  hasRightLine?: boolean;
  leftLine?: {
    color: Color;
  };
  rightLine?: {
    color: Color;
  };
  space?: number;
  text: string;
  textVariant?: TextVariant | [TextVariant, TextVariant] | [TextVariant, TextVariant, TextVariant];
  upperCase?: boolean;
}

export const TextWithLines = (props: TextWithLinesProps) => {
  const {
    color = 'midnight800',
    hasLeftLine = true,
    hasRightLine = true,
    leftLine,
    rightLine,
    space = 6,
    text,
    textVariant = 'h6',
    upperCase,
  } = props;
  return (
    <FlexLayout space={space} sx={{ flexGrow: 1 }} alignItems="center">
      {hasLeftLine && <Box sx={{ width: '100%', height: '1px', background: leftLine?.color ?? color }} />}
      <Text textVariant={textVariant} color={color} upperCase={upperCase}>
        {text}
      </Text>
      {hasRightLine && <Box sx={{ flexGrow: 1, height: '1px', background: rightLine?.color ?? color }} />}
    </FlexLayout>
  );
};
