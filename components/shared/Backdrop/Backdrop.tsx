import { PropsWithChildren, useEffect } from 'react';

import { FlexLayout } from '@/ui/index';

export const Backdrop = (props: PropsWithChildren) => {
  const { children } = props;

  useEffect(() => {
    document.documentElement.style.overflow = 'hidden';

    return () => {
      document.documentElement.style.overflow = 'auto';
    };
  }, []);

  return (
    <FlexLayout
      sx={{
        position: 'fixed',
        zIndex: 'backdrop',
        top: 0,
        left: 0,
        width: '100dvw',
        height: '100dvh',
        '::before': {
          position: 'fixed',
          top: 0,
          left: 0,
          bg: 'primary500',
          width: '100%',
          height: '100%',
          content: `""`,
          opacity: 0.8,
        },
      }}
      justifyContent="center"
      alignItems="center"
    >
      {children}
    </FlexLayout>
  );
};
