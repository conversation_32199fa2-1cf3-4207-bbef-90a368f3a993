import React, { PropsWithChildren, ReactNode } from 'react';
import { ThemeUIStyleObject } from 'theme-ui';

import { SpacingProps } from '@/interfaces';
import { Box } from '@/ui';

interface ConditionalWrapperProps extends PropsWithChildren, SpacingProps {
  condition: boolean;
  wrapper: (children: ReactNode) => ReactNode;
  sx?: ThemeUIStyleObject;
}

export const ConditionalWrapper = (props: ConditionalWrapperProps) => {
  const { condition, wrapper, children, sx = {}, ...spacing } = props;

  return (
    <Box {...spacing} sx={sx}>
      {condition ? wrapper(children) : children}
    </Box>
  );
};
