'use client';

import Link from 'next/link';
import isNull from 'lodash/isNull';

import { useSiteConfig } from '@/context/SiteConfig';
import { footerData as data } from '@/data/navigation';
import { useCurrentLocale } from '@/hooks/i18n';
import { FlexLayout, Image, Text, useBpIndex, useScreenType } from '@/ui';
import { NavigationItem } from '../Header/NavigationItem';

export const Footer = () => {
  const { localeJSON } = useSiteConfig();
  const locale = useCurrentLocale();

  const { isDesktop } = useScreenType();
  const bpIndex = useBpIndex();

  return (
    <FlexLayout
      alignItems="center"
      as="footer"
      bg="midnight900"
      backgroundColor="midnight900"
      justifyContent="space-between"
      px={[11, 15]}
      pt={[18, 8]}
      pb={[17, 8]}
      sx={{ minHeight: '90px' }}
      flexDirection={['column', 'column', 'row']}
      space={12}
    >
      <FlexLayout flexDirection={['column', 'column', 'row']} alignItems="center" space={[2, 6]}>
        <FlexLayout space={[8, 8, 12]} flexDirection={['column', 'column', 'row']} alignItems="center">
          {!isDesktop
            ? data.logos_mobile.map((logo) => {
                return (
                  <Image
                    key={logo.uid}
                    src={logo.url}
                    alt={logo.title}
                    {...(!isNull(bpIndex) && { width: logo.width[bpIndex], height: logo.height[bpIndex] })}
                  />
                );
              })
            : data.logos.map((logo, index) => {
                return (
                  <Link href={logo.link!} key={index}>
                    <Image key={logo.uid} src={logo.url} alt={logo.title} width={logo.width} height={logo.height} />
                  </Link>
                );
              })}
        </FlexLayout>
        {!!data.description && (
          <Text color="white50" py={1} textVariant="label-m-regular" isCentered>
            {data.description}
          </Text>
        )}
      </FlexLayout>
      <FlexLayout space={6} flexDirection={['column', 'row', 'row']} alignItems="center" flexShrink={0}>
        {data.links.map((link, index) => {
          return (
            <NavigationItem
              title={localeJSON[link.path.titleKey] ?? link.path.title}
              href={typeof link.path.href === 'function' ? link.path.href(locale) : link.path.href}
              key={index}
              isOffsite={link.is_offsite}
              isInFooter
              isNewTab={link.is_newtab}
            />
          );
        })}
      </FlexLayout>
    </FlexLayout>
  );
};
