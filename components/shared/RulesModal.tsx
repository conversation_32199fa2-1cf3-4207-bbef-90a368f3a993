'use client';

import noop from 'lodash/noop';
import { useEffect, useRef, useState } from 'react';

import { ModalLayout } from '@/components/layout';
import { Box, Button, FlexLayout, Link, Text } from '@/ui';
import { parseRichTextToReactComponents } from '@/utils';

const elementsMapper = getElementsMapper();

export interface RulesModal {
  uuid: string;
  title: string;
  body: string;
  helperText?: string;
  strings: {
    accept: string;
    decline?: string;
  };
}

interface RulesModalProps {
  modalData: RulesModal;
  onModalAccept: (uid: string) => void;
  onModalDecline?: () => void;
}

export const RulesModal = ({ modalData, onModalAccept, onModalDecline }: RulesModalProps) => {
  const [hasScrolledToBottom, setHasScrolledToBottom] = useState(false);
  const rulesRef = useRef<HTMLDivElement>(null);

  useEffect(() => onScroll(), [rulesRef, rulesRef.current]);

  function onScroll() {
    if (rulesRef && rulesRef.current && !hasScrolledToBottom) {
      setHasScrolledToBottom(
        Math.abs(rulesRef.current.scrollHeight - rulesRef.current.scrollTop - rulesRef.current.clientHeight) <= 1,
      );
    }
  }

  return (
    <ModalLayout isOpen={true} onClickOutside={noop} overflowY="hidden">
      <FlexLayout
        sx={{
          position: 'relative',
          minWidth: ['100dvw', '640px', '640px'],
          maxWidth: ['100%', '640px', '640px'],
          height: ['fit-content', 'auto', 'auto'],
          minHeight: ['100dvh', 'auto', 'auto'],
        }}
        px={[4, 6, 12]}
        pt={[4, 4, 10]}
        pb={[6, 6, 10]}
        space={6}
        flexDirection="column"
        alignItems="center"
        backgroundColor="white"
      >
        <FlexLayout flexDirection="column" space={[6, 6, 14]}>
          <Text variant="h3" color="midnight900" upperCase sx={{ textAlign: 'center' }}>
            {modalData.title}
          </Text>
          <Box sx={{ position: 'relative' }}>
            <Box
              ref={rulesRef}
              onScroll={onScroll}
              sx={{
                maxHeight: ['80vh', '60vh', '50vh'],
                overflowY: 'auto',
              }}
            >
              {parseRichTextToReactComponents(modalData.body, undefined, elementsMapper)}
              {!hasScrolledToBottom && (
                <Box
                  sx={{
                    position: 'absolute',
                    bottom: 0,
                    left: 0,
                    right: 0,
                    width: '100%',
                    height: '100%',
                    maxHeight: '200px',
                    background: 'linear-gradient(rgba(255, 255, 255, 0), rgba(255, 255, 255, 1))',
                    pointerEvents: 'none',
                  }}
                />
              )}
            </Box>
          </Box>
        </FlexLayout>
        <FlexLayout flexDirection="column" alignItems="center" space={2}>
          <FlexLayout space={[6, 6, 4]}>
            {!!onModalDecline && !!modalData.strings.decline && (
              <Button
                onClick={onModalDecline}
                label={modalData.strings.decline}
                variant="primary"
                sx={{ textAlign: 'center' }}
                backgroundSize="120%"
              />
            )}
            <Button
              onClick={() => onModalAccept(modalData.uuid)}
              label={modalData.strings.accept}
              variant="secondary"
              isDisabled={!hasScrolledToBottom}
              sx={{ textAlign: 'center' }}
              backgroundSize="120%"
            />
          </FlexLayout>
          <Text color="midnight40" variant="paragraph-xs-regular" isCentered>
            {modalData.helperText}
          </Text>
        </FlexLayout>
      </FlexLayout>
    </ModalLayout>
  );
};

function getElementsMapper() {
  /* eslint-disable @typescript-eslint/no-explicit-any */
  return {
    p: (props: any) => (
      <Text textVariant={['paragraph-s-regular', 'paragraph-m-regular']} color="primary500" as="p" {...props} />
    ),
    h1: (props: any) => <Text textVariant="h1" color="primary500" my={8} as="h1" {...props} />,
    h2: (props: any) => <Text textVariant="h2" color="primary500" my={8} as="h2" {...props} />,
    h3: (props: any) => <Text textVariant="h3" color="primary500" my={8} as="h3" {...props} />,
    h4: (props: any) => <Text textVariant="h4" color="primary500" my={8} as="h4" {...props} />,
    h5: (props: any) => <Text textVariant="h5" color="primary500" my={8} as="h5" {...props} />,
    h6: (props: any) => <Text textVariant="h6" color="primary500" my={8} as="h6" {...props} />,
    strong: (props: any) => (
      <Text textVariant={['paragraph-s-bold', 'paragraph-m-bold']} color="primary500" as="strong" {...props} />
    ),
    a: (props: any) => <Link sx={{ color: 'secondary200' }} {...props} />,
    ol: (props: any) => (
      <Text
        textVariant={['paragraph-s-regular', 'paragraph-m-regular']}
        as="ol"
        color="primary500"
        {...props}
        sx={{ counterReset: 'item' }}
      />
    ),
    li: (props: any) => (
      <Text
        textVariant={['paragraph-s-regular', 'paragraph-m-regular']}
        as="li"
        color="primary500"
        {...props}
        sx={{
          display: 'block',
          ':before': {
            fontWeight: 'bold',
            content: 'counters(item, ". ") ". "',
            counterIncrement: 'item',
          },
        }}
      />
    ),
  };
  /* eslint-enable @typescript-eslint/no-explicit-any */
}
