'use client';

import { Button, FlexLayout, Image, Text, useScreenType } from '@/ui';

interface SomethingWentWrongProps {
  locale?: string;
}

export default function SomethingWentWrong({ locale }: SomethingWentWrongProps) {
  const { isDesktop, isMobile } = useScreenType();

  const isEnLocale = locale === 'en';

  const title = isEnLocale ? 'OOPS....SOMETHING WENT WRONG!' : 'OUPS ! UN PROBLÈME EST SURVENU !';
  const body = isEnLocale
    ? 'Looks like the page you’re trying to reach doesn’t exist!'
    : "Il semble que la page que vous essayez d'atteindre n'existe pas !";
  const homeButtonLabel = isEnLocale ? 'Take me home' : 'RAMENEZ-MOI CHEZ MOI';

  return (
    <FlexLayout
      flexDirection="column"
      space={[6, 8]}
      alignItems="center"
      justifyContent="center"
      pt={[12, 20, 30]}
      pb={20}
      px={6}
      flexGrow={1}
    >
      <FlexLayout flexDirection="column" alignItems="center" space={[4, 8]}>
        <Image
          src="/images/snake.png"
          alt="Something went wrong picture"
          style={{
            maxWidth: '100%',
            width: isMobile ? '240px' : '420px',
            height: isMobile ? '240px' : '420px',
          }}
        />
        <FlexLayout flexDirection="column" alignItems="center" space={3}>
          <Text as="h6" textVariant={['h7', 'h6', 'h4']} color="primary-midnight" sx={{ textAlign: 'center' }}>
            {title}
          </Text>
          <Text
            as="p"
            textVariant={['paragraph-xs-medium', 'paragraph-m-medium', 'paragraph-l-medium']}
            color="midnight80"
            sx={{ textAlign: 'center' }}
          >
            {body}
          </Text>
        </FlexLayout>
      </FlexLayout>
      <Button
        label={homeButtonLabel}
        variant="tertiary"
        size={isDesktop ? 'large' : isMobile ? 'small' : 'medium'}
        href="/"
        as="a"
      />
    </FlexLayout>
  );
}
