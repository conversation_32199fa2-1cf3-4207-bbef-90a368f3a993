import { BlocksRenderer } from '@strapi/blocks-react-renderer';
import { useState } from 'react';

import { Box, Color, colors, FlexLayout, Icon, Link, Text } from '@/ui';

interface AccordionProps {
  borderColor?: Color;
  index: number;
  title: string;
  content: any;
}

export const Accordion = (props: AccordionProps) => {
  const { borderColor = 'primary300', title, content, index } = props;

  const [isExpanded, setIsExpanded] = useState(false);

  return (
    <FlexLayout
      flexDirection="column"
      onClick={() => setIsExpanded((prev) => !prev)}
      sx={{
        borderBottom: '2px solid',
        '&:last-of-type': {
          borderBottom: 'none',
        },
        borderColor,
        width: '100%',
        cursor: 'pointer',
      }}
    >
      <FlexLayout sx={{ py: '30px' }} justifyContent="space-between" alignItems="center" space={5}>
        <Text
          as="p"
          textVariant={['paragraph-s-medium', 'paragraph-s-medium', 'paragraph-m-medium']}
          color="primary-midnight"
        >
          {index + 1}. {title}
        </Text>
        <Icon icon={isExpanded ? 'chevronUp' : 'chevronDown'} color="primary-midnight" />
      </FlexLayout>
      {isExpanded && (
        <Box p={3}>
          {content && (
            <BlocksRenderer
              content={content}
              blocks={{
                list: ({ children }) => <ul>{children}</ul>,
                link: ({ url, children }) => (
                  <Link href={url} sx={{ color: colors.linkColor1 }}>
                    {children}
                  </Link>
                ),
                'list-item': ({ children }) => (
                  <Text
                    textVariant={['paragraph-s-regular', 'paragraph-m-regular']}
                    as="li"
                    sx={{ color: colors['primary-midnight'] }}
                    {...props}
                  >
                    {children}
                  </Text>
                ),
                paragraph: ({ children }) => (
                  <Text
                    textVariant={['paragraph-s-regular', 'paragraph-m-regular']}
                    as="span"
                    sx={{ color: colors['primary-midnight'] }}
                    {...props}
                  >
                    {children}
                  </Text>
                ),
              }}
            />
          )}
        </Box>
      )}
    </FlexLayout>
  );
};
