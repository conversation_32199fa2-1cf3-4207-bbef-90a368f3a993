import { useEffect, useRef } from 'react';

import { LoginModal } from '@/components/LoginModal';
import { headerData as data } from '@/data/navigation';
import { Box, FlexLayout, Image } from '@/ui';
import { DesktopNavigation } from './DesktopNavigation';
import { MobileNavigation } from './MobileNavigation';

export const Header = () => {
  const headerRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    const onScroll = () => {
      if (headerRef.current) {
        if (document.documentElement.scrollTop > 0 || document.body.scrollTop > 0) {
          headerRef.current.style.height = '70px';
        } else {
          headerRef.current.style.height = '90px';
        }
      }
    };

    document.addEventListener('scroll', onScroll);

    return () => document.removeEventListener('scroll', onScroll);
  }, []);

  return (
    <FlexLayout
      ref={headerRef}
      alignItems="center"
      justifyContent={['space-between', 'space-between', 'center']}
      px={[6, 6, 0]}
      bg="midnight900"
      sx={{
        position: 'sticky',
        top: 0,
        left: 0,
        zIndex: 'navigation',
        transition: 'all 0.25s ease',
        height: ['72px', '72px', '98px'],
      }}
      as="header"
    >
      <Box
        onClick={() => (window.location.href = '/')}
        sx={{ cursor: 'pointer', position: ['static', 'static', 'absolute'], left: '60px' }}
      >
        <Image
          src={data.logo_mobile.url}
          alt={data.logo_mobile.title}
          width={50}
          height={50}
          sx={{ display: ['block', 'block', 'none'] }}
        />

        <Image
          src={data.logo.url}
          alt={data.logo.title}
          width={225}
          height={27}
          sx={{ display: ['none', 'none', 'block'] }}
        />
      </Box>
      <MobileNavigation logo={data.logo_mobile} links={data.links} additionalLinks={data.additionalLinks} />
      <DesktopNavigation links={data.links} />
      <LoginModal />
    </FlexLayout>
  );
};
