'use client';

import React from 'react';

import { useSiteConfig } from '@/context/SiteConfig';
import { CUSTOM_EVENTS } from '@/enums/custom-events';
import { Text } from '@/ui';
import { triggerEvent } from '@/utils';

export const LoginLink = () => {
  const { localeJSON } = useSiteConfig();
  return (
    <Text
      color="secondary-lemonade"
      textVariant="paragraph-xs-medium"
      sx={{
        cursor: 'pointer',
        fontWeight: 600,
        ':hover': {
          color: 'purpleLavander400',
        },
      }}
      onClick={() => triggerEvent(CUSTOM_EVENTS.SHOW_LOGIN_MODAL)}
    >
      {localeJSON['login'] ?? 'Log In'}
    </Text>
  );
};
