'use client';

import { useSession } from 'next-auth/react';
import capitalize from 'lodash/capitalize';
import React, { useState } from 'react';

import { useSiteConfig } from '@/context/SiteConfig';
import { LinkData } from '@/interfaces/';
import { Box, colors, FlexLayout, Icon, Text, useOnClickOutside } from '@/ui';
import DesktopLangaugePicker from '../DesktopLanguagePicker';
import { LoginLink } from '../LoginLink';
import { LogoutLink } from '../LogoutLink';
import { NavigationItem } from '../NavigationItem';
import { NavigationMenu } from './NavigationMenu';

interface DesktopNavigationProps {
  links: LinkData[];
}

export const DesktopNavigation = ({ links }: DesktopNavigationProps) => {
  const { data: session } = useSession();
  const { localeJSON, featureFlags } = useSiteConfig();

  const [isOpen, setIsOpen] = useState(false);
  const ref = useOnClickOutside(() => setIsOpen(false));

  return (
    <>
      <FlexLayout space={6} sx={{ display: ['none', 'none', 'flex'] }}>
        {links.map((link) => {
          const title = localeJSON[link.path.titleKey] ?? link.path.title;
          if (link.sublinks.length !== 0) {
            return <NavigationMenu links={link.sublinks} title={title} key={link._metadata.uid} />;
          } else {
            return (
              <NavigationItem
                href={link.path.href}
                title={title}
                key={link._metadata.uid}
                isOffsite={link.is_offsite}
              />
            );
          }
        })}
      </FlexLayout>
      <FlexLayout
        space={6}
        alignItems={'center'}
        sx={{ position: 'absolute', right: '60px', display: ['none', 'none', 'flex'] }}
      >
        {session && featureFlags.isLoginEnabled ? (
          <FlexLayout space={5}>
            <Box sx={{ position: 'relative', cursor: 'pointer' }} ref={ref}>
              <FlexLayout alignItems="center" onClick={() => setIsOpen(!isOpen)} space={1}>
                <Text
                  color={isOpen ? 'purpleLavander500' : 'secondary-lemonade'}
                  textVariant="paragraph-xs-medium"
                  sx={{ fontWeight: 600 }}
                >
                  {session.user.extDisplayName ?? ''}
                </Text>
                <Icon
                  icon={isOpen ? 'chevronUp' : 'chevronDown'}
                  color={isOpen ? 'purpleLavander500' : 'secondary-lemonade'}
                />
              </FlexLayout>
              {isOpen && (
                <FlexLayout
                  flexDirection="column"
                  bg="midnight900"
                  sx={{
                    border: `1px solid ${colors.lemonade25}`,
                    position: 'absolute',
                    top: '34px',
                    right: 0,
                    zIndex: 'overlay',
                    width: 'max-content',
                    minWidth: '134px',
                  }}
                >
                  <NavigationItem
                    href="/dashboard"
                    title={localeJSON['navDashboard'] ?? 'My Dashboard'}
                    isInDropdown
                    onClick={() => setIsOpen(false)}
                  />
                  <Box backgroundColor="white20" sx={{ alignSelf: 'center', width: '110px', height: '1px' }} />
                  <NavigationItem
                    href="/support"
                    title={capitalize(localeJSON['supportTitle'] ?? 'Support')}
                    isInDropdown
                    onClick={() => setIsOpen(false)}
                  />
                  <Box backgroundColor="white20" sx={{ alignSelf: 'center', width: '110px', height: '1px' }} />
                  <LogoutLink isInDropdown />
                </FlexLayout>
              )}
            </Box>
          </FlexLayout>
        ) : null}

        {!session && featureFlags.isLoginEnabled ? (
          <Box>
            <LoginLink />
          </Box>
        ) : null}

        {featureFlags.showLanguageDropdown && <DesktopLangaugePicker />}
      </FlexLayout>
    </>
  );
};
