'use client';

import { useClickAway } from '@uidotdev/usehooks';
import { Fragment, useRef, useState } from 'react';

import { useSiteConfig } from '@/context/SiteConfig';
import { useNormalizedPathname } from '@/hooks';
import { SublinkData } from '@/interfaces';
import { Box, FlexLayout, Icon, Text, theme } from '@/ui';
import { NavigationItem } from '../NavigationItem';

interface NavigationMenuProps {
  title: string;
  links: SublinkData[];
}

export const NavigationMenu = ({ title, links }: NavigationMenuProps) => {
  const { localeJSON } = useSiteConfig();

  const ref = useClickAway((e) => {
    if (textRef?.current === e.target) {
      return;
    }
    setIsExpanded(false);
  });

  const textRef = useRef(null);

  const [isExpanded, setIsExpanded] = useState(false);

  const pathname = useNormalizedPathname();
  const isSelected = links.some((link) => pathname?.startsWith(link.path.href));

  return (
    <FlexLayout flexDirection="column" sx={{ position: 'relative', overflow: 'visible' }}>
      <FlexLayout justifyContent="center" flexDirection="column" onClick={() => setIsExpanded(!isExpanded)}>
        <FlexLayout space={2} alignItems="center" sx={{ cursor: 'pointer' }}>
          <Text
            ref={textRef}
            className="nav-menu-item"
            color={isSelected ? 'purpleLavander500' : 'secondary-lemonade'}
            textVariant="paragraph-xs-medium"
            sx={{
              ':hover': {
                color: 'purpleLavander400',
              },
            }}
          >
            {title}
          </Text>
          <Icon icon={isExpanded ? 'chevronUp' : 'chevronDown'} color="secondary-lemonade" size="s" />
        </FlexLayout>
      </FlexLayout>
      {isExpanded && (
        <FlexLayout
          ref={ref}
          flexDirection="column"
          bg="midnight900"
          flexShrink={0}
          sx={{
            position: 'absolute',
            top: 7,
            minWidth: '150px',
            border: `1px solid ${theme?.colors?.['lemonade25']}`,
          }}
          space={2}
          flexGrow={1}
          py={2}
          px={3}
        >
          {links.map((link, index) => (
            <Fragment key={index}>
              <NavigationItem
                href={link.path.href}
                title={localeJSON[link.path.titleKey] ?? link.path.title}
                key={link.path.href}
                isOffsite={link.is_offsite}
                onClick={() => setIsExpanded(false)}
              />
              {index !== links.length - 1 && <Box sx={{ width: '100%', height: '1px', bg: 'lemonade25' }} />}
            </Fragment>
          ))}
        </FlexLayout>
      )}
    </FlexLayout>
  );
};
