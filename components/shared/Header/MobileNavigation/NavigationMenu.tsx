'use client';

import { usePathname } from 'next/navigation';
import { signOut, useSession } from 'next-auth/react';
import React, { useEffect, useState } from 'react';

import { ModalLayout } from '@/components/layout/ModalLayout';
import { useSiteConfig } from '@/context/SiteConfig';
import { CUSTOM_EVENTS } from '@/enums/custom-events';
import { AssetData, LinkData, SublinkData } from '@/interfaces';
import { Box, Color, colors, FlexLayout, Icon, Image, Text } from '@/ui/index';
import { offEvent, onEvent, triggerEvent } from '@/utils';
import MobileLanguagePicker from '../MobileLanguagePicker';
import { NavigationItem } from '../NavigationItem';

interface NavigationMenuDropdownLinkProps {
  title: string;
  links: SublinkData[];
}

const NavigationMenuDropdownLink = ({ title, links }: NavigationMenuDropdownLinkProps) => {
  const { localeJSON } = useSiteConfig();

  const pathname = usePathname();
  const isSelected = links.some((link) => pathname?.startsWith(link.path.href));

  const [isExpanded, setIsExpanded] = useState(isSelected);

  //const { activePhases } = useCurrentPhaseContext();

  return (
    <FlexLayout flexDirection="column" sx={{ cursor: 'pointer' }} space={3}>
      <FlexLayout justifyContent="center" flexDirection="column" onClick={() => setIsExpanded(!isExpanded)} px={6}>
        <FlexLayout
          space={1}
          alignItems="center"
          sx={{
            ':hover': {
              color: '#C597E7',
            },
          }}
          color="white"
        >
          <Text color="inherit" textVariant="paragraph-l-medium">
            {title}
          </Text>
          <Icon icon={isExpanded ? 'chevronUp' : 'chevronDown'} />
        </FlexLayout>
      </FlexLayout>
      {isExpanded && (
        <FlexLayout flexDirection="column" bg="midnight800">
          {/*
            Since we handle displaying brackets sublinks through code (in CurrentPhase context we send a request to get a current phase and base on that)
            we generate an array of phases that should be displayed

            Since this NavigationDropdownLink is not intended to be used only for brackets we will check here if parent base path is /brackets (which is defined in CS)
            If it is and if link href is in active phases e.g. href in CS will be /round1  but we will check here if round1 is in activePhases

            Using link.path.href.substring(1) to get rid of '/' so round1 === round1 e.g.
          */}
          {links.map(
            (link) => (
              //basePath === '/brackets' && activePhases.includes(link.path.href.substring(1)) ? (
              <NavigationItem
                href={link.path.href}
                title={localeJSON[link.path.titleKey] ?? link.path.title}
                key={link.path.href}
                isOffsite={link.is_offsite}
                isInMenu
                isSubitem
              />
            ),
            //) : null,
          )}
        </FlexLayout>
      )}
    </FlexLayout>
  );
};

interface NavigationMenuProps {
  logo: AssetData;
  CSLinks: LinkData[];
  additionalLinks: LinkData[];
}

export const NavigationMenu = ({ logo, CSLinks, additionalLinks }: NavigationMenuProps) => {
  const { localeJSON, featureFlags } = useSiteConfig();
  const { data: session } = useSession();

  const [isOpen, setIsOpen] = useState(false);

  useEffect(() => {
    const onMenuToggle = (e: CustomEvent<{ isOpen: boolean }>) => {
      const isOpened = e.detail.isOpen;

      setIsOpen(isOpened);
    };

    onEvent(CUSTOM_EVENTS.TOGGLE_NAVIGATION_MENU, onMenuToggle);

    return () => offEvent(CUSTOM_EVENTS.TOGGLE_NAVIGATION_MENU, onMenuToggle);
  }, []);

  return (
    <ModalLayout isFullScreenOnTablet isOpen={isOpen} onClickOutside={() => setIsOpen(false)}>
      <FlexLayout
        py={4}
        flexDirection="column"
        bg="midnight900"
        sx={{
          minWidth: '100dvw',
          minHeight: '100dvh',
        }}
      >
        <FlexLayout justifyContent="space-between" alignItems="center" px={6}>
          <Box>
            <Image src={logo.url} alt={logo.title} width={50} height={50} />
          </Box>
          <Box
            sx={{ cursor: 'pointer', position: 'relative', right: 4 }}
            onClick={() => triggerEvent(CUSTOM_EVENTS.TOGGLE_NAVIGATION_MENU, { isOpen: false })}
          >
            <Icon icon="close" size="l" color="secondary-lemonade" />
          </Box>
        </FlexLayout>
        <Box sx={{ height: '1px', width: '90%', backgroundColor: colors.lemonade25, margin: '0 auto', mt: 4 }} />

        {session?.user?.extDisplayName ? (
          <Text variant="paragraph-l-medium" color="purpleLavander500" pl={6} mt={6}>
            {session?.user?.extDisplayName}
          </Text>
        ) : null}

        <Text textVariant="paragraph-xs-medium" upperCase color={colors.lemonade50 as Color} sx={{ pl: 6, mt: 6 }}>
          {localeJSON['navigation']}
        </Text>
        <FlexLayout flexDirection="column" mt={4} space={5}>
          {CSLinks.map((link) => {
            const title = localeJSON[link.path.titleKey] ?? link.path.title;
            if (link.sublinks.length !== 0) {
              return <NavigationMenuDropdownLink links={link.sublinks} title={title} key={link._metadata.uid} />;
            } else {
              return (
                <NavigationItem
                  key={link._metadata.uid}
                  title={title}
                  href={link.path.href}
                  isOffsite={link.is_offsite}
                  isInMenu
                />
              );
            }
          })}
          {session ? (
            <>
              <Text textVariant="paragraph-xs-medium" upperCase color={colors.lemonade50 as Color} sx={{ pl: 6 }}>
                {localeJSON['regModalAccount']}
              </Text>
              {additionalLinks.map((link) => (
                <NavigationItem
                  key={link._metadata.uid}
                  title={localeJSON[link.path.titleKey] ?? link.path.title}
                  href={link.path.href}
                  isOffsite={link.is_offsite}
                  isInMenu
                />
              ))}
              <NavigationItem
                title={localeJSON['logout']}
                href="/logout"
                isInMenu
                onClick={() => signOut({ callbackUrl: '/' })}
              />
            </>
          ) : null}
          {featureFlags.showLanguageDropdown && <MobileLanguagePicker />}
        </FlexLayout>
      </FlexLayout>
    </ModalLayout>
  );
};
