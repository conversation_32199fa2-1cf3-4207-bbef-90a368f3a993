'use client';

import { useSession } from 'next-auth/react';
import React from 'react';

import { useSiteConfig } from '@/context/SiteConfig';
import { CUSTOM_EVENTS } from '@/enums/custom-events';
import { AssetData, LinkData } from '@/interfaces';
import { Box, FlexLayout, Icon } from '@/ui';
import { triggerEvent } from '@/utils';
import { LoginLink } from '../LoginLink';
import { NavigationMenu } from './NavigationMenu';

interface MobileNavigationProps {
  logo: AssetData;
  links: LinkData[];
  additionalLinks: LinkData[];
}

export const MobileNavigation = ({ logo, links, additionalLinks = [] }: MobileNavigationProps) => {
  const { data: session } = useSession();
  const { featureFlags } = useSiteConfig();

  return (
    <FlexLayout alignItems="center" space={4} sx={{ display: ['flex', 'flex', 'none'] }}>
      {!session && featureFlags.isLoginEnabled ? <LoginLink /> : null}
      <Box
        as="span"
        sx={{ cursor: 'pointer' }}
        onClick={() => triggerEvent(CUSTOM_EVENTS.TOGGLE_NAVIGATION_MENU, { isOpen: true })}
      >
        <Icon icon="menu" size="l" color="secondary-lemonade" />
      </Box>
      <NavigationMenu logo={logo} CSLinks={links} additionalLinks={additionalLinks} />
    </FlexLayout>
  );
};
