import React, { useEffect, useState } from 'react';

import { ModalLayout } from '@/components/layout/ModalLayout';
import { CUSTOM_EVENTS } from '@/enums/custom-events';
import { AssetData } from '@/interfaces';
import { Box, FlexLayout, Icon, Image, Text } from '@/ui/index';
import { offEvent, onEvent, triggerEvent } from '@/utils';
import { LogoutLink } from './LogoutLink';
import { NavigationItem } from './NavigationItem';

interface AccountMenuProps {
  logo: AssetData;
}

export const AccountMenu = ({ logo }: AccountMenuProps) => {
  const [isOpen, setIsOpen] = useState(false);

  useEffect(() => {
    const onMenuToggle = (e: CustomEvent<{ isOpen: boolean }>) => {
      const isOpened = e.detail.isOpen;

      setIsOpen(isOpened);
    };

    onEvent(CUSTOM_EVENTS.TOGGLE_ACCOUNT_MENU, onMenuToggle);

    return () => offEvent(CUSTOM_EVENTS.TOGGLE_ACCOUNT_MENU, onMenuToggle);
  }, []);

  return (
    <ModalLayout isFullScreenOnTablet isOpen={isOpen} onClickOutside={() => setIsOpen(false)}>
      <FlexLayout
        py={4}
        flexDirection="column"
        bg="midnight900"
        sx={{
          width: '100dvw',
          minHeight: '100dvh',
        }}
      >
        <FlexLayout justifyContent="space-between" alignItems="center" px={6}>
          <Box>
            <Image src={logo.url} alt={logo.title} width={40} height={40} />
          </Box>
          <Box
            sx={{ cursor: 'pointer' }}
            onClick={() => triggerEvent(CUSTOM_EVENTS.TOGGLE_ACCOUNT_MENU, { isOpen: false })}
          >
            <Icon icon="close" size="l" color="white" />
          </Box>
        </FlexLayout>
        <FlexLayout space={2} flexDirection="column" alignItems="center" justifyContent="center">
          <Text textVariant="h8" isCentered upperCase color="white">
            Account
          </Text>
          {/* <Text textVariant="paragraph-m-bold" color="tertiary300" isCentered>
            {user?.alias}#
            <Text as="span" textVariant="paragraph-m-bold" color="white">
              {user?.aliasHash}
            </Text>
          </Text> */}
        </FlexLayout>
        <FlexLayout flexDirection="column" mt={11} space={6}>
          <NavigationItem href="/dashboard" title="My Dashboard" />
          {/* <NavigationItem href="/settings" title="Settings" /> */}
          <LogoutLink />
        </FlexLayout>
      </FlexLayout>
    </ModalLayout>
  );
};
