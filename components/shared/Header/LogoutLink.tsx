'use client';

import { signOut } from 'next-auth/react';
import React from 'react';

import { useSiteConfig } from '@/context/SiteConfig';
import { CUSTOM_EVENTS } from '@/enums/custom-events';
import { Text, useScreenType } from '@/ui';
import { triggerEvent } from '@/utils';

interface LogoutLinkProps {
  isInDropdown?: boolean;
}

export const LogoutLink = ({ isInDropdown = false }: LogoutLinkProps) => {
  const { localeJSON } = useSiteConfig();
  const { isDesktop } = useScreenType();

  const onLogoutClick = () => {
    if (!isDesktop) {
      triggerEvent(CUSTOM_EVENTS.TOGGLE_ACCOUNT_MENU, { isOpen: false });
    }
    signOut({ callbackUrl: '/' });
  };

  return (
    <Text
      color="white"
      textVariant={['paragraph-m-bold', 'paragraph-m-bold', 'paragraph-xs-medium']}
      sx={{
        color: 'secondary-lemonade',
        ':hover': {
          color: 'purpleLavander400',
        },
      }}
      onClick={onLogoutClick}
      py={[0, 0, 2]}
      px={[6, 6, isInDropdown ? 3 : 0]}
    >
      {localeJSON['logout'] ?? 'Log Out'}
    </Text>
  );
};
