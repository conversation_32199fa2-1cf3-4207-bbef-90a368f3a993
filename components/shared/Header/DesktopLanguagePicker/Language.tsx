'use client';

import { FlexLayout, Icon, IconProps, Text } from '@/ui';

interface LanguageProps {
  onClick: () => void;
  isActive: boolean;
  label: string;
  icon: IconProps['icon'];
}

export default function Language({ isActive, icon, label, onClick }: LanguageProps) {
  return (
    <FlexLayout
      sx={{ cursor: !isActive && 'pointer' }}
      alignItems="center"
      space={2}
      onClick={isActive ? undefined : onClick}
    >
      <Icon icon={icon} />
      <Text
        as="p"
        textVariant="paragraph-xs-medium"
        color={isActive ? 'purpleLavander500' : 'secondary-lemonade'}
        sx={{
          whiteSpace: 'nowrap',
          ':hover': {
            color: 'purpleLavander400',
          },
        }}
      >
        {label}
      </Text>
    </FlexLayout>
  );
}
