'use client';

import { defaultLocale } from '@/config/i18n';
import { useSiteConfig } from '@/context/SiteConfig';
import { useCurrentLocale } from '@/hooks';
import { Box, FlexLayout } from '@/ui';
import { useSwitchLocale } from '../hooks';
import Language from './Language';

interface LanguagePickerMenuProps {
  isOpen: boolean;
}

export function LanguagePickerMenu({ isOpen }: LanguagePickerMenuProps) {
  const switchLocale = useSwitchLocale();
  const locale = useCurrentLocale();
  const siteConfig = useSiteConfig();

  return (
    <Box
      px={3}
      py={2}
      sx={{
        position: 'absolute',
        top: 8,
        right: 0,
        border: '1px solid',
        borderColor: 'white20',
        backgroundColor: 'midnight900',
        opacity: isOpen ? 1 : 0,
        display: isOpen ? 'block' : 'none',
        transitionProperty: 'display, opacity',
        transitionDuration: '250ms',
        transitionBehavior: 'allow-discrete',
      }}
    >
      <FlexLayout flexDirection={defaultLocale === 'en' ? 'column' : 'column-reverse'} space={2}>
        <Language
          isActive={locale == 'en'}
          label={siteConfig.localeJSON['languagePickerEnglish']}
          icon="enFlag"
          onClick={() => switchLocale('en')}
        />
        <Box backgroundColor="white20" sx={{ height: 1 }} />
        <Language
          label={siteConfig.localeJSON['languagePickerFrench']}
          isActive={locale == 'fr'}
          icon="frFlag"
          onClick={() => switchLocale('fr')}
        />
      </FlexLayout>
    </Box>
  );
}
