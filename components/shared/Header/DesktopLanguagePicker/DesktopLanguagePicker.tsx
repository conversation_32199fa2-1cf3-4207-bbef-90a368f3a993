'use client';

import { useClickAway, useToggle } from '@uidotdev/usehooks';

import { useCurrentLocale } from '@/hooks';
import { Box, FlexLayout, Icon } from '@/ui';
import { LanguagePickerMenu } from './LanguagePickerMenu';

export default function DesktopLanguagePicker() {
  const [isOpen, toggleIsOpen] = useToggle(false);
  const locale = useCurrentLocale();

  const ref = useClickAway(() => toggleIsOpen(false));

  return (
    <Box sx={{ position: 'relative' }} ref={ref}>
      <FlexLayout space={1} alignItems={'center'} sx={{ cursor: 'pointer' }} onClick={() => toggleIsOpen()}>
        <Icon icon={locale === 'en' ? 'enFlag' : 'frFlag'} />
        <Icon
          icon="chevronDown"
          color="white"
          sx={{ transition: 'rotate 250ms', rotate: isOpen ? '-0.5turn' : 'none' }}
        />
      </FlexLayout>
      <LanguagePickerMenu isOpen={isOpen} />
    </Box>
  );
}
