import { useRouter } from 'next/navigation';

import { Locale } from '@/config/i18n';
import { useNormalizedPathname } from '@/hooks';

export function useSwitchLocale() {
  const router = useRouter();
  const normalizedPathname = useNormalizedPathname();

  function switchLocale(locale: Locale) {
    if (!normalizedPathname) return;
    router.replace(`/${locale}${normalizedPathname}`);
    router.refresh();
  }

  return switchLocale;
}
