import Link, { LinkProps } from 'next/link';
import { useState } from 'react';

import { CUSTOM_EVENTS } from '@/enums/custom-events';
import { useNormalizedPathname } from '@/hooks';
import { FlexLayout, Icon, Text } from '@/ui/components';
import { colors, useBpIndex, useScreenType } from '@/ui/index';
import { getResponsiveValue } from '@/utils';
import { triggerEvent } from '@/utils/event';

interface NavigationItemProps {
  title: string;
  href: LinkProps['href'];
  isOffsite?: boolean;
  isNewTab?: boolean;
  isInDropdown?: boolean;
  isInFooter?: boolean;
  isInMenu?: boolean;
  isSubitem?: boolean;
  onClick?: () => void;
}

export const NavigationItem = (props: NavigationItemProps) => {
  const {
    title,
    href,
    onClick,
    isOffsite = false,
    isInDropdown = false,
    isInFooter = false,
    isInMenu = false,
    isSubitem = false,
    isNewTab = false,
  } = props;
  const pathname = useNormalizedPathname();
  const isSelected =
    href === '/'
      ? pathname === href
      : pathname?.startsWith(typeof href === 'string' ? href : (href?.pathname as string));

  const { isDesktop } = useScreenType();
  const bpIndex = useBpIndex();
  const [isHovered, setIsHovered] = useState<boolean>(false);

  const onMobileLinkClick = () => {
    if (isDesktop) return;

    triggerEvent(CUSTOM_EVENTS.TOGGLE_ACCOUNT_MENU, { isOpen: false });
    triggerEvent(CUSTOM_EVENTS.TOGGLE_NAVIGATION_MENU, { isOpen: false });
  };

  const paddingX = getResponsiveValue([isInFooter ? 0 : 24, isInFooter ? 0 : 24, isInDropdown ? 12 : 0], bpIndex ?? 2);
  const paddingY = getResponsiveValue([isSubitem ? 8 : 0, isSubitem ? 8 : 0, isInDropdown ? 8 : 0], bpIndex ?? 2);

  return (
    <Link
      href={href}
      target={isOffsite || isNewTab ? '_blank' : '_self'}
      onClick={() => {
        onMobileLinkClick();
        if (onClick) onClick();
      }}
      style={{
        display: 'flex',
        backgroundColor: isInMenu && (isHovered || isSelected) ? colors.midnight700 : 'initial',
        paddingLeft: paddingX,
        paddingRight: paddingX,
        paddingTop: paddingY,
        paddingBottom: paddingY,
        textDecoration: 'none',
        width: isInDropdown ? '100%' : 'initial',
      }}
      onMouseEnter={() => setIsHovered(true)}
      onMouseLeave={() => setIsHovered(false)}
    >
      <FlexLayout alignItems="center">
        <Text
          color={isSelected && !isInMenu ? 'purpleLavander500' : 'secondary-lemonade'}
          textVariant={isInMenu ? (isSubitem ? 'paragraph-m-medium' : 'paragraph-l-medium') : 'paragraph-xs-medium'}
          sx={{
            ':hover': {
              color: isInMenu ? 'white' : 'purpleLavander400',
            },
          }}
        >
          {title}
        </Text>
        {isOffsite && <Icon icon="arrowUpRight" size="m" color="secondary-lemonade" />}
      </FlexLayout>
    </Link>
  );
};
