'use client';

import { useToggle } from '@uidotdev/usehooks';

import { defaultLocale } from '@/config/i18n';
import { useSiteConfig } from '@/context/SiteConfig';
import { useCurrentLocale } from '@/hooks';
import { Box, FlexLayout, Icon } from '@/ui';
import { useSwitchLocale } from '../hooks';
import Language from './Language';

export default function MobileLanguagePicker() {
  const [isOpen, toggleIsOpen] = useToggle(false);
  const locale = useCurrentLocale();
  const switchLocale = useSwitchLocale();
  const siteConfig = useSiteConfig();

  return (
    <FlexLayout space={3} flexDirection="column">
      <Box px={6}>
        <FlexLayout
          space={1}
          alignItems="center"
          color="white"
          sx={{ cursor: 'pointer', ':hover': { color: '#C597E7' } }}
          onClick={() => toggleIsOpen()}
        >
          <Icon icon={locale === 'en' ? 'enFlag' : 'frFlag'} size="l" />
          <Icon icon={isOpen ? 'chevronUp' : 'chevronDown'} />
        </FlexLayout>
      </Box>
      {isOpen && (
        <FlexLayout flexDirection={defaultLocale === 'en' ? 'column' : 'column-reverse'}>
          <Language
            isActive={locale == 'en'}
            label={siteConfig.localeJSON['languagePickerEnglish']}
            icon="enFlag"
            onClick={() => switchLocale('en')}
          />
          <Language
            isActive={locale == 'fr'}
            label={siteConfig.localeJSON['languagePickerFrench']}
            icon="frFlag"
            onClick={() => switchLocale('fr')}
          />
        </FlexLayout>
      )}
    </FlexLayout>
  );
}
