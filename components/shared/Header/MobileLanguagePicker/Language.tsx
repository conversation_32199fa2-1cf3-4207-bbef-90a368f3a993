import { Box, FlexLayout, Icon, IconProps, Text } from '@/ui';

interface LanguageProps {
  isActive: boolean;
  label: string;
  icon: IconProps['icon'];
  onClick: () => void;
}

export default function Language({ isActive, icon, label, onClick }: LanguageProps) {
  return (
    <Box
      px={6}
      py={2}
      backgroundColor={isActive ? 'midnight700' : 'midnight800'}
      sx={{ cursor: !isActive && 'pointer', ':hover': { backgroundColor: 'midnight700' } }}
      onClick={isActive ? undefined : onClick}
    >
      <FlexLayout space={2} alignItems="center">
        <Icon icon={icon} />
        <Text as="p" textVariant="paragraph-m-medium" color={'white'}>
          {label}
        </Text>
      </FlexLayout>
    </Box>
  );
}
