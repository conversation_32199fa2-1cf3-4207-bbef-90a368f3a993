import { useEffect, useRef } from 'react';

import { FlexLayout, Icon } from '@/ui';

export const BackToTopButton = () => {
  const btnRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    const onScroll = () => {
      if (btnRef.current) {
        if (document.documentElement.scrollTop > 150 || document.body.scrollTop > 150) {
          btnRef.current.style.display = 'flex';
        } else {
          btnRef.current.style.display = 'none';
        }
      }
    };

    document.addEventListener('scroll', onScroll);

    return () => document.removeEventListener('scroll', onScroll);
  }, []);

  return (
    <FlexLayout
      ref={btnRef}
      justifyContent="center"
      alignItems="center"
      p={5}
      bg="primary600"
      sx={{ position: 'fixed', bottom: '20px', right: '20px', borderRadius: '50%', zIndex: 'modal', display: 'none' }}
      onClick={() => window.scrollTo({ top: 0, left: 0, behavior: 'smooth' })}
    >
      <Icon icon="arrowUp" color="white" />
    </FlexLayout>
  );
};
