import { useEffect, useState } from 'react';
import QRCode from 'react-qr-code';

import { FlexLayout } from '@/ui';
import { utf8_to_b64 } from '@/utils';

export interface qrProps {
  code: string;
}

export const QrCode: React.FC<qrProps> = ({ code }) => {
  const [qrCode, setQrCode] = useState<string>(utf8_to_b64(code) as string);

  useEffect(() => {
    setQrCode(utf8_to_b64(code) as string);
  }, [code]);

  return (
    <>
      {qrCode && (
        <FlexLayout justifyContent="center">
          <FlexLayout style={{ background: 'white', padding: '6px' }}>
            <QRCode bgColor="white" fgColor="black" size={216} key={qrCode} level="L" value={qrCode}></QRCode>
          </FlexLayout>
        </FlexLayout>
      )}
    </>
  );
};
