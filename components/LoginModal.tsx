'use client';

import { signIn } from 'next-auth/react';
import { useEffect, useState } from 'react';

import { useSiteConfig } from '@/context/SiteConfig';
import { CUSTOM_EVENTS } from '@/enums/custom-events';
import { useCurrentLocale } from '@/hooks';
import { Box, Button, FlexLayout, Icon, Image, Text, useScreenType } from '@/ui';
import { offEvent, onEvent } from '@/utils';
import { ModalLayout } from './layout';

const logInModalTitle = 'Log in';
const logInModalButton = 'Riot login';
const logInModalPrimaryText =
  'Purchase a Competitor Bundle, view event details, and more by logging into the TFT Paris Open site.';
const logInModalSecondaryText = 'By clicking on Riot Login you’ll be taken outside of this website.';

export const LoginModal = () => {
  const { isMobile, isTablet } = useScreenType();
  const { localeJSON } = useSiteConfig();
  const [isOpen, setIsOpen] = useState(false);

  const locale = useCurrentLocale();
  const isEnLocale = locale === 'en';

  useEffect(() => {
    const onLoginClick = () => {
      setIsOpen(true);
    };

    onEvent(CUSTOM_EVENTS.SHOW_LOGIN_MODAL, onLoginClick);

    return () => offEvent(CUSTOM_EVENTS.SHOW_LOGIN_MODAL, onLoginClick);
  }, []);

  return (
    <ModalLayout onClickOutside={() => setIsOpen(false)} isOpen={isOpen} overflowY="auto">
      <FlexLayout
        sx={{
          position: 'relative',
          maxWidth: ['90%', '640px', '640px'],
          minHeight: ['90%', 'auto', 'auto'],
          margin: ['0 auto', '', ''],
        }}
        px={[4, 8]}
        pt={[14, 16]}
        pb={[6, 8]}
        space={2}
        flexDirection="column"
        alignItems="center"
        justifyContent="center"
        backgroundColor="white"
      >
        <Box
          sx={{ position: 'absolute', top: '24px', right: '24px', cursor: 'pointer' }}
          onClick={() => setIsOpen(false)}
        >
          <Icon icon="close" color="primary500" size="l" />
        </Box>
        <Text textVariant={['h7', 'h6', 'h4']} upperCase isCentered color="midnight900">
          {localeJSON['login'] ?? logInModalTitle}
        </Text>
        <Text
          as="p"
          textVariant={['paragraph-xs-medium', 'paragraph-s-medium']}
          isCentered
          mt={3}
          mb={6}
          color="midnight80"
        >
          {localeJSON['loginModalBody'] ?? logInModalPrimaryText}
        </Text>
        <Button
          label={localeJSON['loginModalCTA'] ?? logInModalButton}
          onClick={() => signIn('rso', { callbackUrl: isEnLocale ? '/dashboard' : `/${locale}/dashboard` })}
          size={isMobile ? 'small' : isTablet ? 'medium' : 'large'}
        />
        <Text as="p" color="midnight60" mt={1} textVariant="paragraph-xs-regular" sx={{ opacity: 0.5 }} isCentered>
          {localeJSON['loginModalDisc'] ?? logInModalSecondaryText}
        </Text>
        <FlexLayout flexDirection="column" space={3} alignItems="center">
          <Image src="/esl-logo-transparent.png" mt={10} sx={{ width: 56 }} />
          <Text sx={{ width: 147, textAlign: 'center' }} variant="paragraph-xs-medium" color="black20">
            {localeJSON['loginModalFooter']}
          </Text>
        </FlexLayout>
      </FlexLayout>
    </ModalLayout>
  );
};
