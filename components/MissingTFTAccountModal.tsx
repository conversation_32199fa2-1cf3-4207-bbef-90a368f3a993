import { useRouter } from 'next/router';
import { noop } from 'lodash';
import { useEffect, useState } from 'react';

import { Button, FlexLayout, Image, Text } from '@/ui';
import { ModalLayout } from './layout/ModalLayout';

export const MissingTFTAccountModal = () => {
  const [isOpen, setIsOpen] = useState(false);

  const { query, replace, pathname } = useRouter();
  useEffect(() => {
    const { error, error_description } = query;

    if (error && error_description === 'TFT account not found') {
      setIsOpen(true);
    }
  }, [query]);

  const onOkClick = () => {
    setIsOpen(false);
    replace(pathname, undefined, { shallow: true });
  };

  return (
    <ModalLayout isOpen={isOpen} onClickOutside={noop}>
      <FlexLayout
        sx={{
          position: 'relative',
          minWidth: ['100dvw', '640px', '640px'],
          maxWidth: [' 100%', '640px', '640px'],
          height: ['fit-content', 'auto', 'auto'],
          minHeight: ['100dvh', 'auto', 'auto'],
        }}
        px={[8, 16, 20]}
        py={18}
        space={8}
        flexDirection="column"
        alignItems="center"
        backgroundColor="white"
      >
        <Image width={202} height={165} alt="Craggle" src={'/images/craggle.png'} />
        <Text textVariant={['h5', 'h6']} isCentered upperCase color="primary500" px={4}>
          A valid Teamfight Tactics account is required
        </Text>
        <Text textVariant="paragraph-s-regular" color="primary500" isCentered>
          A valid Teamfight Tactics account is required to register for the TFT Vegas Open as a Competitor. We found
          your Riot account, but it is not linked to TFT. Please try signing in and launching Teamfight Tactics to
          resolve this issue.
        </Text>
        <Button label="OK" onClick={onOkClick} backgroundSize="180%" />
      </FlexLayout>
    </ModalLayout>
  );
};
