'use client';

import { useRouter } from 'next/navigation';
import { useSession } from 'next-auth/react';
import { useState } from 'react';

import { updateUser } from '@/apis/user';
import { useSiteConfig } from '@/context/SiteConfig';
import { RulesModal } from './shared/RulesModal';

export enum ModalType {
  OfficialRules = 'OfficialRules',
  ParticipationAgreement = 'ParticipationAgreement',
  ReleaseForm = 'ReleaseForm',
}

interface ModalsProps {
  modalsData: RulesModal[];
}

export function LegalDocumentsModals({ modalsData }: ModalsProps) {
  const siteConfig = useSiteConfig();
  const { featureFlags } = siteConfig;
  const router = useRouter();

  const { data: session, update: updateSession } = useSession();

  const [officialRulesModal, participationAgreementModal, releaseFormModal] = modalsData;

  const showReleaseFormModal =
    !!session?.user &&
    session.user?.extHasTicket &&
    !session.user.extReleaseForm &&
    featureFlags.isReleaseFormModalVisible;

  const showRulesModals = !!session?.user && session.user?.extHasTicket && !session.user.extRulesAccepted;

  const [modal, setModal] = useState(getInitialModal);

  function getInitialModal() {
    if (showRulesModals) return ModalType.OfficialRules;
    else if (showReleaseFormModal) return ModalType.ReleaseForm;
    else return null;
  }

  async function onReleaseFormAccepted() {
    try {
      if (!session) {
        throw new Error('Failed to resolve session');
      }

      await updateUser(session.user.id, { releaseForm: true });
      updateSession();
      router.push('/success');

      setModal(null);
    } catch (err) {
      console.error('Failed to set release form as accepted', err);
    }
  }

  async function onRulesAccepted() {
    try {
      if (!session) {
        throw new Error('Failed to resolve session');
      }

      await updateUser(session.user.id, { rulesAccepted: true });
      if (showReleaseFormModal) {
        setModal(ModalType.ReleaseForm);
      } else {
        router.push('/success');
        setModal(null);
      }
    } catch (err) {
      console.error('Failed to accept rules', err);
    }
  }
  return (
    <>
      {modal === ModalType.ReleaseForm && (
        <RulesModal modalData={releaseFormModal} onModalAccept={onReleaseFormAccepted} />
      )}
      {modal === ModalType.ParticipationAgreement && (
        <RulesModal modalData={participationAgreementModal} onModalAccept={onRulesAccepted} />
      )}
      {modal === ModalType.OfficialRules && (
        <RulesModal
          modalData={officialRulesModal}
          onModalAccept={() => {
            setModal(ModalType.ParticipationAgreement);
          }}
        />
      )}
    </>
  );
}
