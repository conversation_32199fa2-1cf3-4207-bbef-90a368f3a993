'use client';

import { useIsClient } from '@uidotdev/usehooks';
import { type PropsWithChildren, useEffect } from 'react';
import ReactDOM from 'react-dom';

import { Box, FlexLayout, useOnClickOutside } from '@/ui';

export interface ModalLayoutProps extends PropsWithChildren {
  onClickOutside: () => void;
  overflowY?: 'visible' | 'hidden' | 'auto' | 'scroll';
  isOpen: boolean;
  isFullScreenOnTablet?: boolean;
}

export const ModalLayout = (props: ModalLayoutProps) => {
  const { children, isFullScreenOnTablet, onClickOutside, isOpen, overflowY = 'scroll' } = props;

  const isClient = useIsClient();
  const ref = useOnClickOutside(onClickOutside);

  useEffect(() => {
    if (isOpen) {
      document.documentElement.style.overflow = 'hidden';
    } else {
      document.documentElement.style.overflow = 'auto';
    }

    return () => {
      document.documentElement.style.overflow = 'auto';
    };
  }, [isOpen]);

  if (!isOpen) {
    return null;
  }

  if (!isClient) {
    return null;
  }

  return ReactDOM.createPortal(
    <FlexLayout
      justifyContent="center"
      alignItems="center"
      sx={{
        position: 'fixed',
        top: '0',
        left: '0',
        zIndex: 'modal',
        background: 'black75',
        width: '100dvw',
        minHeight: '100dvh',
      }}
    >
      <Box
        ref={ref}
        mt={[0, isFullScreenOnTablet ? 0 : 8, 8]}
        sx={{
          maxWidth: ['100%', isFullScreenOnTablet ? '100%' : '80%', '80%'],
          maxHeight: '100dvh',
          height: ['fit-content', 'fit-content', 'fit-content'],
          overflowY,
          overflowX: 'hidden',
        }}
      >
        {children}
      </Box>
    </FlexLayout>,
    document.getElementById('modal-container')!,
  );
};
