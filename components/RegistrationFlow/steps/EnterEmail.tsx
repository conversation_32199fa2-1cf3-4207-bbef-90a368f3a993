'use client';

import { yupResolver } from '@hookform/resolvers/yup';
import { useState } from 'react';
import { Controller, useForm } from 'react-hook-form';
import * as Yup from 'yup';

import { requestVerificationCode } from '@/apis/auth-v2';
import { useSiteConfig } from '@/context/SiteConfig';
import { useCurrentLocale } from '@/hooks';
import { Button, FlexLayout, Image, Input, Text, useScreenType } from '@/ui';
import { RegistrationStep, RegistrationStepProps } from '../utils';

const emailFormTitle = 'Verify your email';
const emailFormPrimaryText = 'Please enter your preferred contact email below. We will send you a verification code.';
const emailFormInputLabel = 'Preferred Contact Email';
const emailFormInputPlaceholder = '<EMAIL>';
const sendCode = 'Send code';
const sending = 'Sending...';
const emailInvalidError = 'Email address is not valid.';
const emailRequiredError = 'Email is required.';
const sendCodeError = 'An error occurred. Please try again.';

function getVerifyEmailSchema(localeJSON: Record<string, string>) {
  return Yup.object().shape({
    email: Yup.string()
      .email(localeJSON['regVerifyModalError'] ?? emailInvalidError)
      .required(localeJSON['emailIsRequiredErrorText'] ?? emailRequiredError),
  });
}

type VerifyEmailFormData = {
  email: string;
};

export default function EnterEmail({ setEmail, setStep }: RegistrationStepProps) {
  const { localeJSON } = useSiteConfig();
  const locale = useCurrentLocale();
  const { isMobile, isTablet } = useScreenType();

  const [verifyEmailError, setVerifyEmailError] = useState('');
  const [isLoading, setIsLoading] = useState(false);

  const {
    control,
    handleSubmit,
    setValue,
    getValues,
    trigger,
    formState: { isValid },
  } = useForm<VerifyEmailFormData>({
    resolver: yupResolver(getVerifyEmailSchema(localeJSON)),
    reValidateMode: 'onChange',
    defaultValues: {
      email: '',
    },
  });

  const onSubmit = (data: VerifyEmailFormData) => {
    setIsLoading(true);

    requestVerificationCode(getValues('email'), locale)
      .then(() => setEmail(data.email))
      .then(() => setStep(RegistrationStep.EnterCode))
      .catch((err) => setVerifyEmailError(err?.message || localeJSON['genericErrorText'] || sendCodeError))
      .finally(() => setIsLoading(false));
  };

  return (
    <FlexLayout px={8} pt={14} pb={8} flexDirection="column" alignItems="center" backgroundColor="white">
      <Text textVariant={['h7', 'h6', 'h4']} upperCase isCentered color="midnight900">
        {localeJSON['regVerifyModalTitle'] ?? emailFormTitle}
      </Text>
      <Text
        as="p"
        textVariant={['paragraph-xs-medium', 'paragraph-s-medium']}
        isCentered
        mt={6}
        mb={6}
        color="midnight80"
      >
        {localeJSON['regVerifyModalSubtitle'] ?? emailFormPrimaryText}
      </Text>
      <FlexLayout
        as="form"
        onSubmit={handleSubmit(onSubmit)}
        sx={{ width: '100%' }}
        flexDirection="column"
        alignItems="center"
      >
        <Controller
          name="email"
          control={control}
          render={({ field, fieldState }) => (
            <Input
              type="text"
              value={field.value}
              onChange={(v) => {
                trigger('email');
                setValue('email', v);
              }}
              onBlur={field.onBlur}
              ref={field.ref}
              name={field.name}
              labelLeft={localeJSON['regVerifyModalInfo'] ?? emailFormInputLabel}
              placeholder={emailFormInputPlaceholder}
              errorText={fieldState.error?.message}
            />
          )}
        />
        {verifyEmailError && (
          <Text textVariant="label-m-regular" color="error200" isCentered mt={8}>
            {verifyEmailError}
          </Text>
        )}
        <Button
          label={isLoading ? `${localeJSON['sending']}...` ?? sending : localeJSON['regVerifyModalCTA'] ?? sendCode}
          mt={10}
          isDisabled={isLoading || !isValid}
          backgroundSize={isLoading ? '110%' : '100%'}
          size={isMobile ? 'small' : isTablet ? 'medium' : 'large'}
        />
        <FlexLayout flexDirection="column" space={3} alignItems="center">
          <Image src="/esl-logo-transparent.png" mt={10} sx={{ width: 56 }} />
          <Text sx={{ width: 147, textAlign: 'center' }} variant="paragraph-xs-medium" color="black20">
            {localeJSON['loginModalFooter']}
          </Text>
        </FlexLayout>
      </FlexLayout>
    </FlexLayout>
  );
}
