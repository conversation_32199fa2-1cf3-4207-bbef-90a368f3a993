'use client';

import { Box } from 'theme-ui';

import { useSiteConfig } from '@/context/SiteConfig';
import { Button, FlexLayout, Image, Text, useScreenType } from '@/ui';
import { RegistrationStepProps } from '../utils';

const accountCreated = 'SUCCESS!';
const accountCreatedDesc = 'Congrats, your account has been successfully created!';

export default function AccountCreated({ close }: RegistrationStepProps) {
  const { localeJSON } = useSiteConfig();
  const { isMobile } = useScreenType();

  return (
    <FlexLayout
      px={[6, 8]}
      pt={[6, 8]}
      pb={8}
      space={8}
      flexDirection="column"
      alignItems="center"
      backgroundColor="white"
    >
      <Box sx={{ width: [220, 320], height: [220, 320] }}>
        <Image width="100%" height="100%" alt="registration success image" src="/images/accountCreatedImg.png" />
      </Box>
      <FlexLayout flexDirection="column" alignItems="center" space={3}>
        <Text textVariant={['h7', 'h4']} isCentered upperCase color="midnight900" px={4}>
          {localeJSON['successModalTitle'] ?? accountCreated}
        </Text>
        <Text textVariant={['paragraph-xs-medium', 'paragraph-s-medium']} isCentered color="midnight80" px={4}>
          {localeJSON['successModalSubtitle'] ?? accountCreatedDesc}
        </Text>
      </FlexLayout>
      <Button label={localeJSON['successModalCTA'] ?? 'Continue'} onClick={close} size={isMobile ? 'small' : 'large'} />
    </FlexLayout>
  );
}
