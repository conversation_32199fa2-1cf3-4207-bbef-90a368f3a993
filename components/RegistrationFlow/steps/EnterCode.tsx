'use client';

import { useSession } from 'next-auth/react';
import { omit } from 'lodash';
import { useEffect, useState } from 'react';
import OTPInput from 'react-otp-input';

import { requestVerificationCode, submitVerificationCode } from '@/apis/auth-v2';
import { useSiteConfig } from '@/context/SiteConfig';
import { CUSTOM_EVENTS } from '@/enums/custom-events';
import { useCurrentLocale } from '@/hooks';
import { Button, FlexLayout, OTPInputCell, Text, useScreenType } from '@/ui';
import { triggerEvent } from '@/utils';
import { RegistrationStep, RegistrationStepProps } from '../utils';

const codeErrorMessage = 'The code you entered is incorrect. Please enter the code you received or request a new code.';
const codeResendErrorMessage = 'Something went wrong with resending the code. Please try again later.';
const newCodeMessage =
  'A new verification code has been sent to your email. You can request a new code in {{X}} seconds.';

const getResendLabel = (localeJSON: Record<string, string>, seconds: number) =>
  (localeJSON['regVerifyModalEntryNewCode'] ?? newCodeMessage).replace('{{X}}', `${seconds}`);

export default function EnterCode({ email, setStep }: RegistrationStepProps) {
  const { localeJSON } = useSiteConfig();
  const locale = useCurrentLocale();
  const { isMobile, isTablet } = useScreenType();

  const { update: updateSession } = useSession();
  const [code, setCode] = useState('');
  const [codeError, setCodeError] = useState('');
  const [codeResendError, setCodeResendError] = useState('');
  const [isSubmittingCode, setIsSubmittingCode] = useState(false);
  const [canResend, setCanResend] = useState(false);
  const [seconds, setSeconds] = useState(0);
  const [showCanRequestNewCode, setShowCanRequestNewCode] = useState(false);

  useEffect(() => {
    let timeout: NodeJS.Timeout;

    if (seconds > 0) {
      timeout = setTimeout(() => {
        setSeconds(seconds - 1);
      }, 1000);
    } else if (seconds === 0) {
      setCanResend(true);
    }
    return () => clearTimeout(timeout);
  }, [seconds]);

  function submitCode() {
    setIsSubmittingCode(true);
    submitVerificationCode(code)
      .then(() => {
        setStep(RegistrationStep.CollectUserData);
        updateSession();
        triggerEvent(CUSTOM_EVENTS.SHOW_REGISTER_MODAL);
      })
      .catch(() => setCodeError(localeJSON['regVerifyModalEntryIncorrectError'] ?? codeErrorMessage))
      .finally(() => setIsSubmittingCode(false));
  }

  const resendCode = () => {
    setCanResend(false);
    setSeconds(30);
    setShowCanRequestNewCode(true);

    requestVerificationCode(email, locale).catch(() =>
      setCodeResendError(localeJSON['regVerifyModalResendCodeError'] ?? codeResendErrorMessage),
    );
  };

  return (
    <FlexLayout px={8} pt={14} pb={8} flexDirection="column" alignItems="center" backgroundColor="white">
      <Text textVariant={['h7', 'h6', 'h4']} upperCase isCentered color="midnight900">
        {localeJSON['regVerifyModalEnterTitle'] ?? 'enter verification code'}
      </Text>
      <Text
        as="p"
        textVariant={['paragraph-xs-medium', 'paragraph-s-medium']}
        isCentered
        mt={3}
        mb={6}
        color="midnight80"
      >
        {localeJSON['regVerifyModalEnterSubtitle'] ?? 'Enter the verification code we sent to'} {email}.
      </Text>
      <FlexLayout flexDirection="column" alignItems="center" space={8}>
        <FlexLayout flexDirection="column" alignItems="center">
          <Button
            label={localeJSON['regVerifyModalEnterFirstCTA'] ?? 'Resend code'}
            size={isMobile ? 'small' : 'medium'}
            variant="secondary"
            isDisabled={!canResend}
            onClick={resendCode}
          />
          {!canResend && seconds > 0 && seconds <= 30 && !codeResendError && (
            <Text mt={2} textVariant="paragraph-xs-medium" isCentered color="midnight60">
              {getResendLabel(localeJSON, seconds)}
            </Text>
          )}
          {canResend && showCanRequestNewCode && !codeResendError && (
            <Text mt={2} textVariant="paragraph-xs-medium" isCentered color="midnight60">
              {localeJSON['regVerifyModalCanRequestNewCode'] ?? 'You can request a new code!'}
            </Text>
          )}
        </FlexLayout>
        <FlexLayout flexDirection="column" space={3}>
          <OTPInput
            onChange={setCode}
            value={code}
            inputType="number"
            numInputs={6}
            containerStyle={{ gap: '8px' }}
            renderSeparator={null}
            renderInput={(props) => (
              <OTPInputCell {...omit(props, 'style')} disabled={isSubmittingCode} error={!!codeError} />
            )}
            shouldAutoFocus
          />
          {codeError && (
            <Text textVariant="label-m-regular" color="error200">
              {codeError}
            </Text>
          )}
        </FlexLayout>
        <Button
          label={localeJSON['regVerifyModalEnterSecondCTA'] ?? 'Continue'}
          isDisabled={isSubmittingCode || code.length !== 6}
          onClick={() => {
            if (!code || code.length !== 6) return;
            submitCode();
          }}
          size={isMobile ? 'small' : isTablet ? 'medium' : 'large'}
        />
        {codeResendError && (
          <Text textVariant="label-m-regular" color="error200" isCentered mt={8}>
            {codeResendError}
          </Text>
        )}
      </FlexLayout>
    </FlexLayout>
  );
}
