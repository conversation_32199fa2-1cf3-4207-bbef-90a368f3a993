'use client';

import { useSession } from 'next-auth/react';
import { yupResolver } from '@hookform/resolvers/yup';
import { useEffect, useState } from 'react';
import { Controller, useForm } from 'react-hook-form';

import { register } from '@/apis/auth-v2';
import { TextWithLines } from '@/components/shared/TextWithLines';
import { useSiteConfig } from '@/context/SiteConfig';
import { useCurrentLocale } from '@/hooks';
import { RegionWithCountries } from '@/interfaces';
import { Button, Checkbox, FlexLayout, Icon, Input, Link, Select, SelectOption, Text, useScreenType } from '@/ui';
import { getUserDataSchema, UserData } from '@/utils/registration';
import { RegistrationStep, RegistrationStepProps } from '../utils';

const registerError = 'An error occurred. Please try again.';
const socialsHint = 'Discord, WeChat, etc. Please list the social and your handle.';

export default function CollectUserData({ email, setStep }: RegistrationStepProps) {
  const { isMobile } = useScreenType();

  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState('');

  const siteConfig = useSiteConfig();
  const { localeJSON } = siteConfig;
  const locale = useCurrentLocale();

  const { data: session } = useSession();

  const options = getOptionsFromRegions(siteConfig.regions || []);

  // const countryHint = siteConfig.localeJSON.countryHint;

  useEffect(() => {
    if (session?.user?.extGameName) {
      setValue('displayName', session.user.extGameName);
    }
  }, []);

  const {
    control,
    handleSubmit,
    setValue,
    trigger,
    formState: { isValid },
  } = useForm<UserData>({
    resolver: yupResolver(getUserDataSchema(localeJSON)),
    defaultValues: {
      displayName: '',
      gameServer: '',
      preferredGameClientLanguage: '',
      age: false,
      firstName: '',
      lastName: '',
      country: '',
      preferredContact: '',
      shirtSize: '',
      terms: false,
      marketing: false,
      runeterraRegion: '',
    },
  });

  const onSubmit = (data: UserData) => {
    setIsLoading(true);

    register(data, locale)
      .then(() => setStep(RegistrationStep.AccountCreated))
      .catch((err) => setError(err?.message || localeJSON['genericErrorText'] || registerError))
      .finally(() => setIsLoading(false));
  };

  return (
    <FlexLayout px={[4, 8]} pt={14} pb={8} space={8} flexDirection="column" alignItems="center" backgroundColor="white">
      <Text textVariant={['h7', 'h6', 'h4']} color="midnight900" isCentered upperCase sx={{ maxWidth: '15em' }}>
        {localeJSON['regModalTitle'] ?? 'Finish creating your account'}
      </Text>
      <Text as="p" textVariant={['paragraph-xs-medium', 'paragraph-s-medium']} color="midnight80" isCentered>
        {localeJSON['regModalBody'] ??
          'In order to finish setting up your account for the TFT Paris Open, we want to make sure we have the correct information.'}
      </Text>
      <FlexLayout alignItems="center" space={4}>
        <Icon size="l" icon="riot" color="midnight900" />
        <Text textVariant="label-l-regular" color="midnight900">
          {`${localeJSON['regModalAuthenticatedAsMessage'] ?? "You've authenticated as"} `}
          <Text textVariant="label-l-bold" color="midnight900" sx={{ fontWeight: 'bold' }}>
            {email}
          </Text>
        </Text>
      </FlexLayout>
      <FlexLayout as="form" flexDirection="column" sx={{ width: '100%' }} space={6} onSubmit={handleSubmit(onSubmit)}>
        <TextWithLines
          hasLeftLine={false}
          text={localeJSON['regModalAccount'] ?? 'ACCOUNT'}
          textVariant={['h9', 'h6']}
          color="midnight900"
          rightLine={{
            color: 'midnight20',
          }}
        />
        <Controller
          name="displayName"
          control={control}
          render={({ field, fieldState }) => (
            <Input
              type="text"
              value={field.value}
              onChange={(v) => setValue('displayName', v)}
              onBlur={() => {
                trigger('displayName');
                field.onBlur();
              }}
              ref={field.ref}
              name={field.name}
              labelLeft={localeJSON['regModalDisplayName'] ?? 'Display Name'}
              placeholder={localeJSON['regModalDisplayName'] ?? 'Display Name'}
              errorText={fieldState.error?.message}
              helperText={
                localeJSON['regModalDisplayNameHelper'] ??
                'This will be your name for the tournament and cannot be changed. Must be alphanumeric in 3-16 latin characters only.'
              }
            />
          )}
        />
        <Controller
          name="gameServer"
          control={control}
          render={({ field, fieldState }) => (
            <Select
              hasDropdownIndicator
              value={field.value}
              onChange={(v) => {
                setValue('gameServer', v as string);
              }}
              onBlur={() => {
                trigger('gameServer');
                field.onBlur();
              }}
              isDisabled={false}
              ref={field.ref}
              name={field.name}
              label={localeJSON['regModalGameServer'] ?? 'Game Server'}
              errorText={fieldState.error?.message}
              options={getGameServers()}
              placeholder={localeJSON['regModalGameServerPlaceholder'] ?? 'Choose server...'}
            />
          )}
        />
        <Controller
          name="preferredGameClientLanguage"
          control={control}
          render={({ field, fieldState }) => (
            <Select
              hasDropdownIndicator
              value={field.value}
              onChange={(v) => {
                setValue('preferredGameClientLanguage', v as string);
              }}
              onBlur={() => {
                trigger('preferredGameClientLanguage');
                field.onBlur();
              }}
              isDisabled={false}
              ref={field.ref}
              name={field.name}
              label={localeJSON['regPreferredGameClientLanguage'] ?? 'Preferred Game Client Language'}
              errorText={fieldState.error?.message}
              options={getPreferredGameClientLanguages(locale)}
              placeholder={localeJSON['regPreferredGameClientLanguage'] ?? 'Choose language...'}
              helperText={
                localeJSON['regPreferredGameClientLanguageHelper'] ??
                'Choose the language that you will see in-game during the tournament'
              }
            />
          )}
        />
        <TextWithLines
          hasLeftLine={false}
          text={localeJSON['regModalAccInfo'] ?? 'ACCOUNT INFORMATION'}
          textVariant={['h9', 'h6']}
          color="midnight900"
          rightLine={{
            color: 'midnight20',
          }}
        />
        <FlexLayout space={3} flexDirection={['column', 'row']}>
          <Controller
            name="firstName"
            control={control}
            render={({ field, fieldState }) => (
              <Input
                type="text"
                value={field.value}
                onChange={(v) => setValue('firstName', v)}
                onBlur={() => {
                  trigger('firstName');
                  field.onBlur();
                }}
                ref={field.ref}
                name={field.name}
                labelLeft={localeJSON['regModalFirstName'] ?? 'First Name'}
                placeholder={localeJSON['regModalFirstName'] ?? 'First Name'}
                errorText={fieldState.error?.message}
              />
            )}
          />
          <Controller
            name="lastName"
            control={control}
            render={({ field, fieldState }) => (
              <Input
                type="text"
                value={field.value}
                onChange={(v) => setValue('lastName', v)}
                onBlur={() => {
                  trigger('lastName');
                  field.onBlur();
                }}
                ref={field.ref}
                name={field.name}
                labelLeft={localeJSON['regModalLasttNamePlaceholder'] ?? 'Last Name'}
                placeholder={localeJSON['regModalLasttNamePlaceholder'] ?? 'Last Name'}
                errorText={fieldState.error?.message}
              />
            )}
          />
        </FlexLayout>
        <Controller
          name="age"
          control={control}
          render={({ field }) => (
            <Checkbox
              isChecked={field.value}
              onChange={(v) => {
                setValue('age', v);
                trigger('age');
              }}
              label={
                localeJSON['regModalAgeDisc'] ?? 'I confirm that I am at least 18 years of age by December 13, 2024*'
              }
            />
          )}
        />
        <FlexLayout space={3} flexDirection={['column', 'row']}>
          <Controller
            name="country"
            control={control}
            render={({ field, fieldState }) => (
              <FlexLayout flexDirection="column" flex={1}>
                <Select
                  hasDropdownIndicator
                  value={field.value}
                  onChange={(v) => setValue('country', v as string)}
                  onBlur={() => {
                    trigger('country');
                    field.onBlur();
                  }}
                  isDisabled={false}
                  ref={field.ref}
                  name={field.name}
                  label={localeJSON['regModalCountry'] ?? 'Country / Region'}
                  errorText={fieldState.error?.message}
                  options={options}
                  placeholder={localeJSON['regModalCountryPlaceholder'] ?? 'Choose country...'}
                  hasGroupedOptions={true}
                />
              </FlexLayout>
            )}
          />
        </FlexLayout>
        <Controller
          name="preferredContact"
          control={control}
          render={({ field, fieldState }) => (
            <FlexLayout flexDirection="column">
              <Input
                type="text"
                value={field.value}
                onChange={(v) => setValue('preferredContact', v)}
                onBlur={() => {
                  trigger('preferredContact');
                  field.onBlur();
                }}
                ref={field.ref}
                name={field.name}
                labelLeft={localeJSON['regModalPreferredContact'] ?? 'Preferred Contact'}
                placeholder={localeJSON['regModalPreferredContactPlaceholder'] ?? 'Write your social and handle'}
                errorText={fieldState.error?.message}
                helperText={localeJSON['regModalPreferredContactDesc'] ?? socialsHint}
              />
            </FlexLayout>
          )}
        />
        <Controller
          name="shirtSize"
          control={control}
          render={({ field, fieldState }) => (
            <Select
              hasDropdownIndicator
              value={field.value}
              onChange={(v) => {
                setValue('shirtSize', v as string);
              }}
              onBlur={() => {
                trigger('shirtSize');
                field.onBlur();
              }}
              isDisabled={false}
              ref={field.ref}
              name={field.name}
              label={localeJSON['regModalShirtSizeTitle'] ?? 'Shirt Size'}
              errorText={fieldState.error?.message}
              options={getShirtSizes()}
              placeholder={localeJSON['regModalShirtSizePlaceholder'] ?? 'Choose your shirt size'}
            />
          )}
        />
        <Controller
          name="runeterraRegion"
          control={control}
          render={({ field, fieldState }) => (
            <Select
              hasDropdownIndicator
              value={field.value}
              onChange={(v) => {
                setValue('runeterraRegion', v as string);
              }}
              onBlur={() => {
                trigger('runeterraRegion');
                field.onBlur();
              }}
              isDisabled={false}
              ref={field.ref}
              name={field.name}
              label={localeJSON['regRuneterraRegion'] ?? 'What is your favorite Runeterra Region?'}
              errorText={fieldState.error?.message}
              options={getRuneterraRegion()}
              placeholder={localeJSON['regRuneterraRegionPlaceholder'] ?? 'Choose region…'}
            />
          )}
        />
        <TextWithLines
          hasLeftLine={false}
          text={localeJSON['legalSectionTitle'] ?? 'LEGAL'}
          textVariant={['h9', 'h6']}
          color="midnight900"
          rightLine={{
            color: 'midnight20',
          }}
        />
        <Controller
          name="terms"
          control={control}
          render={({ field }) => (
            <Checkbox
              isChecked={field.value}
              onChange={(v) => {
                setValue('terms', v);
                trigger('terms');
              }}
              label={
                <Text textVariant="paragraph-xs-medium" color="midnight80">
                  {`${localeJSON['IAgree'] ?? 'I Agree to the'} `}
                  <Link
                    href={
                      locale === 'en'
                        ? 'https://www.riotgames.com/en/terms-of-service'
                        : 'https://www.riotgames.com/fr/terms-of-service-FR'
                    }
                    target="_blank"
                  >
                    <Text color="midnight80" textVariant="paragraph-xs-medium">
                      {localeJSON['bottomNavToS'] ?? 'Terms of Service'}
                    </Text>
                  </Link>{' '}
                  {`${localeJSON['and'] ?? 'and'} `}
                  <Link
                    href={
                      locale === 'en'
                        ? 'https://www.riotgames.com/en/privacy-notice'
                        : 'https://www.riotgames.com/fr/privacy-notice-FR'
                    }
                    target="_blank"
                  >
                    <Text color="midnight80" textVariant="paragraph-xs-medium">
                      {localeJSON['bottomNavPP'] ?? 'Privacy Policy'}.
                    </Text>
                  </Link>
                </Text>
              }
            />
          )}
        />
        <Controller
          name="marketing"
          control={control}
          render={({ field }) => (
            <Checkbox
              isChecked={field.value}
              onChange={(v) => {
                setValue('marketing', v);
                trigger('marketing');
              }}
              label={
                localeJSON['regLegalSecondCheckbox'] ??
                'I agree to receive emails about future TFT Open events, updates to the program, and marketing content.'
              }
            />
          )}
        />

        {error && (
          <Text textVariant="label-m-regular" color="error200" isCentered>
            {error}
          </Text>
        )}
        <Button
          label={
            isLoading
              ? `${localeJSON['submitting'] ?? 'submitting'}...`
              : localeJSON['registrationBottomCTA'] ?? 'Create my account'
          }
          isDisabled={!isValid || isLoading}
          sx={{ m: isMobile ? 'unset' : '0 auto', width: isMobile ? '100%' : 'fit-content' }}
          size={isMobile ? 'small' : 'large'}
        />
      </FlexLayout>
    </FlexLayout>
  );
}

function getOptionsFromRegions(data: RegionWithCountries[]) {
  return data.map((region: RegionWithCountries) => {
    const option: SelectOption = { label: region?.name };
    option.options = region?.countries?.map((country): SelectOption => {
      return {
        label: country?.name,
        value: country?.publicId,
      };
    });
    return option;
  });
}

export function getGameServers() {
  return [
    { label: 'Brazil', value: 'BR1' },
    { label: 'Europe Nordic & East', value: 'EUN1' },
    { label: 'Europe West', value: 'EUW1' },
    { label: 'Japan', value: 'JP1' },
    { label: 'Korea', value: 'KR' },
    { label: 'Latin America North', value: 'LA1' },
    { label: 'Latin America South', value: 'LA2' },
    { label: 'Middle East', value: 'ME1' },
    { label: 'North America', value: 'NA1' },
    { label: 'Oceania', value: 'OC1' },
    { label: 'Russia', value: 'RU' },
    { label: 'South East Asia', value: 'SG2' },
    { label: 'Turkey', value: 'TR1' },
    { label: 'Taiwan', value: 'TW2' },
    { label: 'Vietnam', value: 'VN2' },
  ];
}
export function getPreferredGameClientLanguages(lang: string) {
  return [
    { label: lang === 'en' ? 'Arabic' : 'العربية', value: 'ar' },
    { label: lang === 'en' ? 'Czech' : 'Čeština', value: 'cs' },
    { label: lang === 'en' ? 'German' : 'Deutsch', value: 'de' },
    { label: lang === 'en' ? 'Greek' : 'Ελληνικά', value: 'el' },
    { label: lang === 'en' ? 'English (Australia)' : 'English (Australia)', value: 'en_AU' },
    { label: lang === 'en' ? 'English (Great Britain)' : 'English (Great Britain)', value: 'en_GB' },
    { label: lang === 'en' ? 'English (Phillippines)' : 'English (Phillippines)', value: 'en_PH' },
    { label: lang === 'en' ? 'English (Singapore)' : 'English (Singapore)', value: 'en_SG' },
    { label: lang === 'en' ? 'English (NA)' : 'English (NA)', value: 'en_US' },
    { label: lang === 'en' ? 'Argentinian' : 'Argentinian', value: 'es_AR' },
    { label: lang === 'en' ? 'Spanish' : 'Español', value: 'es_ES' },
    { label: lang === 'en' ? 'Spanish (Mexico)' : 'Español (México)', value: 'es_MX' },
    { label: lang === 'en' ? 'French' : 'Français', value: 'fr_FR' },
    { label: lang === 'en' ? 'Hungarian' : 'Magyar', value: 'hu_HU' },
    { label: lang === 'en' ? 'Italian' : 'Italiano', value: 'it_IT' },
    { label: lang === 'en' ? 'Japanese' : '日本語', value: 'ja_JP' },
    { label: lang === 'en' ? 'Korean' : 'Korean', value: 'ko_KR' },
    { label: lang === 'en' ? 'Polish' : 'Polski', value: 'pl_PL' },
    { label: lang === 'en' ? 'Portuguese' : 'Português', value: 'pt_BR' },
    { label: lang === 'en' ? 'Romanian' : 'Română', value: 'ro_RO' },
    { label: lang === 'en' ? 'Russian' : 'Русский', value: 'ru_RU' },
    { label: lang === 'en' ? 'Thai' : 'ภาษาไทย', value: 'th_TH' },
    { label: lang === 'en' ? 'Vietnamese' : 'Tiếng Việt', value: 'vi_VN' },
    { label: lang === 'en' ? 'Turkish' : 'Türkçe', value: 'tr_TR' },
    { label: lang === 'en' ? 'Simplified Chinese' : '简体中文', value: 'zh_CN' },
    { label: lang === 'en' ? 'Simplified Chinese (Malaysia)' : '简体中文 (马来西亚)', value: 'zh_MY' },
    { label: lang === 'en' ? 'Traditional Chinese' : '繁體中文', value: 'zh_TW' },
  ];
}

function getShirtSizes() {
  return [
    { label: 'Small', value: 'Small' },
    { label: 'Medium', value: 'Medium' },
    { label: 'Large', value: 'Large' },
    { label: 'XL', value: 'XL' },
    { label: 'XXL', value: 'XXL' },
  ];
}

function getRuneterraRegion() {
  return [
    { label: 'Bandle City', value: 'bandle_city' },
    { label: 'Bilgewater', value: 'bilgewater' },
    { label: 'Demacia', value: 'demacia' },
    { label: 'Freljord', value: 'freljord' },
    { label: 'Ionia', value: 'ionia' },
    { label: 'Ixtal', value: 'ixtal' },
    { label: 'Noxus', value: 'noxus' },
    { label: 'Piltover', value: 'piltover' },
    { label: 'Shadow Isles', value: 'shadow_isles' },
    { label: 'Shurima', value: 'shurima' },
    { label: 'Targon', value: 'targon' },
    { label: 'Void', value: 'void' },
    { label: 'Zaun', value: 'zaun' },
  ];
}
