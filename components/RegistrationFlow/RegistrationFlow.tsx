'use client';

import { useRouter } from 'next/navigation';
import { signOut, useSession } from 'next-auth/react';
import { useMemo, useState } from 'react';

import { Box, FlexLayout, Icon } from '@/ui';
import AccountCreated from './steps/AccountCreated';
import CollectUserData from './steps/CollectUserData';
import EnterCode from './steps/EnterCode';
import EnterEmail from './steps/EnterEmail';
import { RegistrationStep } from './utils';

const componentMap = {
  [RegistrationStep.EnterEmail]: EnterEmail,
  [RegistrationStep.EnterCode]: EnterCode,
  [RegistrationStep.CollectUserData]: CollectUserData,
  [RegistrationStep.AccountCreated]: AccountCreated,
};

export function RegistrationFlow() {
  const { data: session, update: updateSession } = useSession();
  const [email, setEmail] = useState<string>('');
  const [step, setStep] = useState<RegistrationStep>(RegistrationStep.EnterEmail);
  const router = useRouter();

  // useMemo instead of useLayoutEffect for optimal SSR
  useMemo(() => {
    setEmail(session?.user?.extEmail ?? '');

    if (session?.user?.extCodeVerified) {
      setStep(RegistrationStep.CollectUserData);
    }
  }, []);

  const Component = componentMap[step];

  function close() {
    if (step === RegistrationStep.AccountCreated) {
      updateSession();
      router.push('/dashboard');
      router.refresh();
    } else {
      signOut({ callbackUrl: '/' });
    }
  }

  return (
    <FlexLayout
      justifyContent="center"
      backgroundColor="secondary-lemonade"
      sx={{
        backgroundImage: 'url(/images/homeBackground1.png)',
        backgroundRepeat: 'no-repeat',
        backgroundSize: 'cover',
        minHeight: '100dvh',
      }}
    >
      <Box
        mt={['61px', '136px']}
        mb={['128px', '136px', '300px']}
        px={[4, 0, 0]}
        sx={{ maxWidth: 640, minWidth: 350, width: '100%' }}
      >
        <Box sx={{ position: 'relative' }}>
          <Component email={email} setEmail={setEmail} setStep={setStep} close={close} />

          <Icon
            icon="close"
            size="l"
            color="primary500"
            sx={{ position: 'absolute', top: 8, right: 8, cursor: 'pointer' }}
            onClick={close}
          />
        </Box>
      </Box>
    </FlexLayout>
  );
}
