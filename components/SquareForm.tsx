import * as Square from '@square/web-sdk';
import { forwardRef, useEffect, useImperativeHandle, useRef, useState } from 'react';

import { Box } from '@/ui';

// TODO(competitor-bundle): refactor

export interface SquareFormHandle {
  tokenize: () => Promise<string | null>;
}

export default forwardRef<SquareFormHandle, Record<string, unknown>>(function SquareForm(_props, ref) {
  const [card, setCard] = useState<Square.Card | null>(null);
  const cardRef = useRef(null);
  const squareInitialized = useRef(false);

  useEffect(() => {
    if (squareInitialized.current) return;

    if (squareInitialized) {
      squareInitialized.current = true;
    }

    let cardInstance: Square.Card | undefined;

    (async () => {
      const squarePayments = await Square.payments(
        process.env.NEXT_PUBLIC_SQUARE_APPLICATION_ID as string,
        process.env.NEXT_PUBLIC_SQUARE_LOCATION_ID as string,
      );

      if (squarePayments === null) {
        throw new Error('Square Web Payments SDK failed to load');
      }

      cardInstance = await squarePayments.card();

      setCard(cardInstance);

      if (cardRef.current) {
        await cardInstance.attach(cardRef.current);
      }
    })();

    return () => {
      cardInstance?.destroy();
    };
  }, []);

  useImperativeHandle(
    ref,
    () => ({
      tokenize: async () => {
        return card ? (await card.tokenize()).token ?? null : null;
      },
    }),
    [card],
  );

  return (
    <Box as="form" sx={{ mt: 6, minHeight: 88.5 }}>
      <Box ref={cardRef} />
    </Box>
  );
});
