'use client';

import { useState } from 'react';
import { FacebookShareButton, TwitterShareButton } from 'react-share';

import { ModalLayout } from '@/components/layout';
import { FlexLayout, Icon, Image, useScreenType } from '@/ui';

export default function SocialShareIcons() {
  const { isDesktop } = useScreenType();

  const url = 'https://paris.competetft.com/get-your-competitor-bundle';

  const [isImageOpen, setIsImageOpen] = useState(false);

  return (
    <FlexLayout alignItems="center" justifyContent="center" space={5}>
      <TwitterShareButton url={url}>
        <Icon icon="X" color="white" size="l" />
      </TwitterShareButton>
      <FacebookShareButton url={url}>
        <Icon icon="facebook" color="white" size="l" />
      </FacebookShareButton>
      <>
        <a href="/social-share-photo.webp" download="get-your-competitor-bundle.png">
          <Icon icon="share" color="white" size="l" onClick={() => setIsImageOpen(true)} />
        </a>
        <ModalLayout onClickOutside={() => setIsImageOpen(false)} isOpen={isImageOpen}>
          <Image
            src="/social-share-photo.webp"
            alt="Get your competitor bundle"
            style={{ height: isDesktop ? '90dvw' : 'auto', width: isDesktop ? 'auto' : '90dvw' }}
          />
        </ModalLayout>
      </>
    </FlexLayout>
  );
}
