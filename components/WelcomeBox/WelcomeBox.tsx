'use client';

import { useSession } from 'next-auth/react';

import { Box, Button, FlexLayout, Image, Text } from '@/ui';
import SocialShareIcons from './SocialShareIcons';
import { WelcomeBoxData } from './types';

export default function WelcomeBox({ data }: { data: WelcomeBoxData }) {
  const { data: session } = useSession();

  return (
    <FlexLayout
      flexDirection="column"
      space={8}
      alignItems="center"
      justifyContent="center"
      backgroundColor="midnight40"
      pt={8}
      pb={12}
      px={6}
      sx={{ backdropFilter: 'blur(7px)', width: '100%' }}
    >
      <FlexLayout flexDirection="column" space={3} alignItems="center" justifyContent="center">
        {data.imageUrl && <Image src={data.imageUrl} sx={{ width: [290, 290, 475], height: [260, 260, 420] }} />}
        <Text textVariant={['h4', 'h4', 'h3']} color="white" sx={{ maxWidth: 872 }} isCentered upperCase>
          {data.title}
        </Text>
        <Text
          textVariant={['paragraph-m-medium', 'paragraph-m-medium', 'paragraph-l-medium']}
          color="white"
          sx={{ maxWidth: 872 }}
          isCentered
        >
          {data.subtitle}
        </Text>
        {!session?.user.extQuestionnaire && (
          <Text
            textVariant={['paragraph-m-medium', 'paragraph-m-medium', 'paragraph-l-medium']}
            color="white"
            sx={{ maxWidth: 872 }}
            isCentered
          >
            {data.body}
          </Text>
        )}
      </FlexLayout>
      <FlexLayout flexDirection="column" space={5} alignItems="center">
        <Button
          // prettier-ignore
          label={
              session?.user.extQuestionnaire
                ? data.strings.dashboard
                : data.strings.completeProfile
            }
          href={
            // prettier-ignore
            session?.user.extQuestionnaire
                ? '/dashboard'
                : '/complete-profile'
          }
          as="a"
          sx={{ textAlign: 'center' }}
          variant="tertiary"
        />
        <Box sx={{ height: 1, width: '100%' }} backgroundColor="white20" />
        <Text variant="paragraph-xs-medium">{data.strings.shareOnSocialMedia}</Text>
        <SocialShareIcons />
      </FlexLayout>
    </FlexLayout>
  );
}
