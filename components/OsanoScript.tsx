const url = 'https://cmp.osano.com/16BZ95S4qp9Kl2gUA/68d94257-95ca-48ee-835b-e66d6eda6cdf/osano.js';

import NextScript from 'next/script';

import '@/styles/osano.css';

export function OsanoScript() {
  return (
    <>
      <NextScript
        dangerouslySetInnerHTML={{
          __html: `
						window.dataLayer = window.dataLayer ||[]; 
						function gtag(){dataLayer.push(arguments);} 
						gtag('consent','default',{ 
							'ad_storage':'denied', 
							'analytics_storage':'denied', 
							'ad_user_data':'denied', 
							'ad_personalization':'denied', 
						'personalization_storage':'denied', 
						'functionality_storage':'granted', 
						'security_storage':'granted', 
						'wait_for_update': 500 
						}); 
						gtag("set", "ads_data_redaction", true); 
          `,
        }}
      />
      <NextScript src={url} rel="preload" />
    </>
  );
}
