import { usePathname } from 'next/navigation';
import { useCurrentLocale as useRouterCurrentLocale } from 'next-i18n-router/client';

import i18nConfig, { Locale } from '@/config/i18n';

const { locales } = i18nConfig;

export function useCurrentLocale() {
  return useRouterCurrentLocale(i18nConfig) as Locale;
}

export function useNormalizedPathname() {
  const pathname = usePathname();

  if (!pathname) return null;

  const isRootPath = ['/', ...locales.map((locale) => `/${locale}`)].includes(pathname);

  let normalizedPathname = pathname;

  locales.forEach((locale) => {
    if (pathname.startsWith(`/${locale}`)) {
      normalizedPathname = pathname.replace(`/${locale}`, '');
    }
  });

  return isRootPath ? '/' : normalizedPathname;
}
