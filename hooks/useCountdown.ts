import { differenceInMilliseconds } from 'date-fns';
import { useEffect, useState } from 'react';

export interface Time {
  hours: number;
  minutes: number;
  seconds: number;
}

interface UseCountdownProps {
  untilDate: Date | null;
  onCountdownEnd?: () => void;
}

export default function useCountdown({ untilDate, onCountdownEnd }: UseCountdownProps) {
  const [ms, setMs] = useState(0);

  useEffect(() => {
    if (!untilDate) {
      return;
    }

    const differenceMs = differenceInMilliseconds(untilDate, new Date());
    if (differenceMs <= 0) {
      return;
    }

    setMs(differenceMs);

    const intervalId = setInterval(() => {
      setMs((prevMs) => {
        if (prevMs <= 1000) {
          onCountdownEnd && onCountdownEnd();
          clearInterval(intervalId);
          return 0;
        }
        return prevMs - 1000;
      });
    }, 1000);

    return () => clearInterval(intervalId);
  }, [untilDate]);

  return msToTime(ms);
}

function msToTime(duration: number): Time {
  const seconds = Math.floor((duration / 1000) % 60);
  const minutes = Math.floor((duration / (1000 * 60)) % 60);
  const hours = Math.floor((duration / (1000 * 60 * 60)) % 24);

  return { hours, minutes, seconds };
}
