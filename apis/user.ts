import {
  KbygReadUpdateSchema,
  MarketingUpdateSchema,
  QuestionnaireUpdateSchema,
  ReleaseFormUpdateSchema,
  RulesAcceptedUpdateSchema,
} from '@/utils';

type UpdateUserPayload =
  | MarketingUpdateSchema
  | RulesAcceptedUpdateSchema
  | QuestionnaireUpdateSchema
  | ReleaseFormUpdateSchema
  | KbygReadUpdateSchema;

export async function updateUser(id: string, payload: UpdateUserPayload) {
  try {
    const response = await fetch(`/api/users/${id}`, {
      method: 'PUT',
      body: JSON.stringify(payload),
      headers: {
        'Content-Type': 'application/json',
      },
    });

    if (response.ok) {
      return Promise.resolve();
    }

    const message = await response.json();
    return Promise.reject(new Error(message ?? ''));
  } catch {
    throw new Error('');
  }
}

export async function getWhitelistStatus(id: string) {
  try {
    const response = await fetch(`/api/users/${id}/whitelist/status`, {
      method: 'GET',
    });

    if (!response.ok) {
      throw new Error('Invalid response');
    }

    const { status } = await response.json();
    return status;
  } catch (err) {
    throw new Error('Whitelist status check failed');
  }
}
