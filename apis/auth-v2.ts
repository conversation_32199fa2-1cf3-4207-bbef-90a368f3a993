import { UserData } from '@/utils/registration';

const headers = {
  'Content-Type': 'application/json',
};

export async function requestVerificationCode(email: string, locale = 'en') {
  try {
    const response = await fetch(`/api/auth/request-verification-code?locale=${locale}`, {
      method: 'POST',
      headers,
      body: JSON.stringify({ email }),
    });

    if (response.ok) {
      return Promise.resolve();
    }

    const message = await response.json();
    return Promise.reject(new Error(message ?? ''));
  } catch {
    throw new Error('');
  }
}

export async function submitVerificationCode(code: string) {
  const response = await fetch('/api/auth/submit-verification-code', {
    method: 'POST',
    headers,
    body: JSON.stringify({ code }),
  });

  if (!response.ok) {
    throw new Error('An error occurred while verifying the code.');
  }

  return response.json();
}

export async function register(data: UserData, locale = 'en') {
  try {
    const response = await fetch(`/api/auth/register?locale=${locale}`, {
      method: 'POST',
      headers,
      body: JSON.stringify(data),
    });

    if (response.ok) {
      return Promise.resolve();
    }

    const message = await response.json();
    return Promise.reject(new Error(message ?? ''));
  } catch {
    throw new Error('');
  }
}
