import get from 'lodash/get';

import { Locale } from '@/config/i18n';
import fallbackFeatureFlags from '@/data/fallbackFeatureFlags.json';
import fallbackLocale from '@/data/fallbackLocale.json';
import fallbackRegions from '@/data/fallbackRegions.json';
import {
  fallbackOfficialRulesModalHtml,
  fallbackParticipationAgreementModalHtml,
  fallbackReleaseFormModalHtml,
} from '@/data/legalDocumentsModals';
import { SiteConfig } from '@/types/strapi';

const options: RequestInit = {
  headers: {
    Authorization: `Bearer ${process.env.STRAPI_API_TOKEN}`,
  },
  next: {
    revalidate: +(process.env.STRAPI_REVALIDATE_SECONDS ?? 120),
  },
};

const status = process.env.RSO_ENVIRONMENT === 'staging' ? ('draft' as const) : ('published' as const);

export async function getSiteConfig(lang: Locale): Promise<SiteConfig> {
  try {
    const response = await fetch(
      `${process.env.STRAPI_API_URL}/api/site-config?locale=${lang}&status=${status}`,
      options,
    );

    if (!response.ok) {
      throw new Error('Invalid response');
    }

    const data = await response.json();

    return {
      localeJSON: get(data, 'data.localeJSON') ?? fallbackLocale[lang],
      regions: get(data, 'data.regions') ?? fallbackRegions[lang],
      featureFlags: get(data, 'data.featureFlags') ?? fallbackFeatureFlags,
      releaseFormModalHtml: get(data, 'data.releaseFormModalHtml') ?? fallbackReleaseFormModalHtml,
      officialRulesModalHtml: get(data, 'data.officialRulesModalHtml') ?? fallbackOfficialRulesModalHtml,
      participationAgreementModalHtml:
        get(data, 'data.participationAgreementModalHtml') ?? fallbackParticipationAgreementModalHtml,
    };
  } catch (err) {
    console.error('Site config error', err);

    return {
      localeJSON: fallbackLocale[lang],
      regions: fallbackRegions[lang],
      featureFlags: fallbackFeatureFlags,
      releaseFormModalHtml: fallbackReleaseFormModalHtml,
      officialRulesModalHtml: fallbackOfficialRulesModalHtml,
      participationAgreementModalHtml: fallbackParticipationAgreementModalHtml,
    };
  }
}

export async function getPageConfig<T>(slug: string, lang: Locale): Promise<T | null> {
  try {
    const response = await fetch(
      `${process.env.STRAPI_API_URL}/api/${slug}?locale=${lang}&populate=*&status=${status}`,
      options,
    );

    if (!response.ok) {
      throw new Error('Invalid response');
    }

    const data = await response.json();

    return get(data, 'data');
  } catch (err) {
    console.error(`${slug} error`, err);

    return null;
  }
}

export async function getCollectionType<T>(name: string, lang: Locale): Promise<T | null> {
  try {
    const response = await fetch(`${process.env.STRAPI_API_URL}/api/${name}?locale=${lang}&status=${status}`, options);

    if (!response.ok) {
      throw new Error('Invalid response');
    }

    const data = await response.json();

    return data.data.map((c: any) => c);
  } catch (err) {
    console.error(`${name} error`, err);

    return null;
  }
}
