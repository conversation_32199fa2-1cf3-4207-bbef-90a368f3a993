import { Locale } from '@/config/i18n';
import { handleError } from '@/utils/fetch';
import { CreatePayPalOrder, PayPalPayload, SquarePayload } from '@/utils/payment';

const headers = {
  'Content-Type': 'application/json',
};

export async function acquireTicket(dropId: string) {
  try {
    const response = await fetch(`/api/drops/${dropId}/acquire-ticket`, { method: 'POST', headers });

    if (!response.ok) {
      await handleError(response);
    }

    return await response.json();
  } catch (err) {
    console.error(err);

    throw err;
  }
}

export type PurchaseTicketPayload = SquarePayload | PayPalPayload;

export async function purchaseTicket(dropId: string, data: PurchaseTicketPayload, { locale }: { locale: Locale }) {
  try {
    const response = await fetch(`/api/drops/${dropId}/purchase-ticket?locale=${locale}`, {
      method: 'POST',
      headers,
      body: JSON.stringify(data),
    });

    if (!response.ok) {
      await handleError(response);
    }

    return await response.json();
  } catch (err) {
    console.error(err);

    throw err;
  }
}

export async function createPayPalOrder(dropId: string, data: CreatePayPalOrder) {
  try {
    const response = await fetch(`/api/drops/${dropId}}/create-paypal-order`, {
      method: 'POST',
      body: JSON.stringify(data),
    });

    if (!response.ok) {
      await handleError(response);
    }

    return await response.json();
  } catch (err) {
    console.error(err);

    throw err;
  }
}
