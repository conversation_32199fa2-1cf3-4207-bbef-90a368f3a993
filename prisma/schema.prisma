datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

generator client {
  provider = "prisma-client-js"
}

// ===============================
// BEGIN: NextAuth.js Models
// ===============================

model Account {
  id                String  @id @default(cuid())
  userId            String  @map("user_id")
  type              String
  provider          String
  providerAccountId String  @map("provider_account_id")
  refresh_token     String? @db.Text
  access_token      String? @db.Text
  expires_at        Int?
  token_type        String?
  scope             String?
  id_token          String? @db.Text
  session_state     String?

  user User @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@unique([provider, providerAccountId])
  @@map("accounts")
}

model Session {
  id           String   @id @default(cuid())
  sessionToken String   @unique @map("session_token")
  userId       String   @map("user_id")
  expires      DateTime
  user         User     @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@map("sessions")
}

model User {
  id            String    @id @default(cuid())
  name          String?
  email         String?   @unique
  emailVerified DateTime? @map("email_verified")
  image         String?
  accounts      Account[]
  sessions      Session[]

  // Fields with the `ext` prefix are extra fields added to the
  // NextAuth.js models. They’re not required by NextAuth.js.
  extPuuid         String?   @unique @map("ext_puuid")
  extGameName      String?   @map("ext_game_name")
  extEmail         String?   @unique @map("ext_email")
  extDisplayName   String?   @unique @map("ext_display_name")
  extCode          String?   @map("ext_code")
  extCodeExpires   DateTime? @map("ext_code_expires")
  extCodeVerified  DateTime? @map("ext_code_verified")
  extData          Json?     @map("ext_data")
  extRegistred     DateTime? @map("ext_registred")
  extMarketing     Boolean   @default(true) @map("ext_marketin")
  extRulesAccepted Boolean   @default(false) @map("ext_rules_accepted")
  extQuestionnaire Json?     @map("ext_questionnaire")
  extReleaseForm   Boolean   @default(false) @map("ext_release_form")
  extRiotId        String?   @map("ext_riot_id")
  extHasTicket     Boolean   @default(false) @map("ext_has_ticket")
  extTickets       Ticket[]
  extRealmUsername String?
  extRealmPassword String?
  extKbygRead      Boolean   @default(false) @map("ext_kbyg_read")

  @@map("users")
}

model VerificationToken {
  identifier String
  token      String
  expires    DateTime

  @@unique([identifier, token])
  @@map("verificationtokens")
}

// ===============================
// END: NextAuth.js Models
// ===============================

model Rank {
  puuid        String @id
  summonerId   String @map("summoner_id")
  accountId    String @map("account_id")
  tier         Tier
  rank         String
  leaguePoints Int    @map("league_points")
  wins         Int
  losses       Int
  region       String

  @@map("ranks")
}

enum Tier {
  CHALLENGER
  MASTER
  GRANDMASTER

  @@map("tiers")
}

// A drop represents a pool of tickets that are released for sale together. Each drop
// has a unique `name` field to simplify management and debugging. The `config` field
// in each drop contains the drop's configuration, which includes various rules related
// to ticket purchases (e.g. the sale times for users with a specific ranks, how long
// ticket can be reserved by a user, etc.).

model Drop {
  id        String   @id @default(cuid())
  name      String   @unique
  type      DropType @default(COMPETITOR_BUNDLE)
  config    Json
  tickets   Ticket[]
  createdAt DateTime @default(now()) @map("created_at")
  updatedAt DateTime @updatedAt @map("updated_at")

  @@map("drops")
}

// DropType is added for future-proofing. If we ever need to update the schema of the configuration object
// we can simply create a new drop type and use it to properly validate the drop config field.
enum DropType {
  COMPETITOR_BUNDLE

  @@map("drop_types")
}

// A ticket is a resource that a user can purchase. A ticket can be temporarily reserved
// for purchase by a specific user for a set duration (duration should be defined in the
// drop's configuration). While reserved, the ticket is unavailable for purchase by others.

model Ticket {
  id            String    @id @default(cuid())
  dropId        String?   @map("drop_id")
  userId        String?   @map("user_id")
  reserved      Boolean   @default(false)
  reservedAt    DateTime? @map("reserved_at")
  reservedUntil DateTime? @map("reserved_until")
  purchased     Boolean   @default(false)
  purchaseData  Json?
  createdAt     DateTime  @default(now()) @map("created_at")
  updatedAt     DateTime  @updatedAt @map("updated_at")
  drop          Drop?     @relation(fields: [dropId], references: [id])
  user          User?     @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@index([dropId])
  @@map("tickets")
}

model Whitelist {
  id     String @id @default(cuid())
  email  String @unique
  riotId String @map("riot_id")

  @@map("whitelist")
}
