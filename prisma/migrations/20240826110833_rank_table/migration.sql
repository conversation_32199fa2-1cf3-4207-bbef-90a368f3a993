-- CreateEnum
CREATE TYPE "tiers" AS ENUM ('CHALLENGER', 'MASTER', 'GRANDMASTER');

-- CreateTable
CREATE TABLE "ranks" (
    "puuid" TEXT NOT NULL,
    "summoner_id" TEXT NOT NULL,
    "account_id" TEXT NOT NULL,
    "tier" "tiers" NOT NULL,
    "rank" TEXT NOT NULL,
    "league_points" INTEGER NOT NULL,
    "wins" INTEGER NOT NULL,
    "losses" INTEGER NOT NULL,

    CONSTRAINT "ranks_pkey" PRIMARY KEY ("puuid")
);

-- CreateIndex
CREATE UNIQUE INDEX "ranks_summoner_id_key" ON "ranks"("summoner_id");

-- CreateIndex
CREATE UNIQUE INDEX "ranks_account_id_key" ON "ranks"("account_id");
