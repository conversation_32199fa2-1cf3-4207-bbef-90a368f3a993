-- CreateEnum
CREATE TYPE "drop_types" AS ENUM ('COMPETITOR_BUNDLE');

-- CreateTable
CREATE TABLE "drops" (
    "id" TEXT NOT NULL,
    "name" TEXT NOT NULL,
    "type" "drop_types" NOT NULL DEFAULT 'COMPETITOR_BUNDLE',
    "config" JSONB NOT NULL,
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "drops_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "tickets" (
    "id" TEXT NOT NULL,
    "drop_id" TEXT NOT NULL,
    "user_id" TEXT,
    "reserved" BOOLEAN NOT NULL DEFAULT false,
    "reserved_at" TIMESTAMP(3),
    "purchased" BOOLEAN NOT NULL DEFAULT false,
    "purchaseData" JSONB,
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "tickets_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE UNIQUE INDEX "drops_name_key" ON "drops"("name");

-- CreateIndex
CREATE INDEX "tickets_drop_id_idx" ON "tickets"("drop_id");

-- AddForeignKey
ALTER TABLE "tickets" ADD CONSTRAINT "tickets_drop_id_fkey" FOREIGN KEY ("drop_id") REFERENCES "drops"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "tickets" ADD CONSTRAINT "tickets_user_id_fkey" FOREIGN KEY ("user_id") REFERENCES "users"("id") ON DELETE CASCADE ON UPDATE CASCADE;
