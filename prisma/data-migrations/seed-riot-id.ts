/**
 * This script seeds user Riot ID (gameName + tagLine) into the 'users' table in the database.
 *
 * To run this script:
 *  - specify the DATABASE_URL environment variable
 *
 * Example usage:
 *   - `DATABASE_URL=your_database_url RIOT_API_KEY=your_riot_api_key npm run data-migration:seed-riot-id`
 *
 * Replace 'your_database_url' with the actual URL of your database.
 * Replace 'your_riot_api_key' with the actual api key.
 *
 */

import { Prisma, PrismaClient, User } from '@prisma/client';
import axios from 'axios';
import readline from 'readline/promises';

const gameserverToRegion: { [key: string]: string } = {
  br: 'americas',
  eune: 'europe',
  euw: 'europe',
  jp: 'asia',
  kr: 'asia',
  lan: 'americas',
  las: 'americas',
  mena: 'asia',
  na: 'americas',
  oce: 'asia',
  ph: 'asia',
  sg: 'asia',
  th: 'asia',
  tw: 'asia',
  vn: 'asia',
};

interface RiotApiResponse {
  puuid: string;
  gameName: string;
  tagLine: string;
}

type ExtData = Prisma.JsonObject & {
  gameServer?: string;
};

interface UserWithExtData extends User {
  extData: ExtData | null;
}

async function getUserRiotId(puuid: string, region: string): Promise<RiotApiResponse> {
  const url = `https://${region}.api.riotgames.com/riot/account/v1/accounts/by-puuid/${puuid}`;
  const response = await axios.get<RiotApiResponse>(url, {
    headers: { 'X-Riot-Token': process.env.RIOT_API_KEY },
  });

  if (!response.data.gameName || !response.data.tagLine) {
    throw new Error('No gameName or tagLine returned!');
  }

  return response.data;
}

const prisma = new PrismaClient({
  datasources: {
    db: { url: process.env.DATABASE_URL },
  },
});

const sleep = async (ms = 500) => new Promise((res) => setTimeout(res, ms));

async function main() {
  try {
    const usersWithoutId = (await prisma.user.findMany({ where: { extRiotId: null } })) as UserWithExtData[];

    const rl = readline.createInterface({ input: process.stdin, output: process.stdout });
    const answer = await rl.question('\nAre you sure you want to proceed with seeding Riot IDs? (y/n): ');
    rl.close();

    if (answer.toLowerCase() !== 'y') {
      console.log('Seeding aborted');
      return;
    }

    console.log('Users for update count: ', usersWithoutId.length);
    const userRiotIds: { id: string; extRiotId: string }[] = [];

    for (const user of usersWithoutId) {
      const gameServer = user.extData?.gameServer?.toLowerCase() ?? 'euw';

      const regionServer = gameserverToRegion[gameServer] || 'europe';

      if (!user.extPuuid) {
        console.warn(`Skipping user ${user.id}: No extPuuid found`);
        continue;
      }

      try {
        const { gameName, tagLine } = await getUserRiotId(user.extPuuid, regionServer);
        console.log('Fetched riot ID: ', `${gameName}#${tagLine}`);
        await sleep(50); // needed to bypass riot API limits

        const update = { id: user.id, extRiotId: `${gameName}#${tagLine}` };
        userRiotIds.push(update);
      } catch (error) {
        console.error(`Error fetching data for user ${user.id}:`, error);
      }
    }

    console.log(`Updating ${userRiotIds.length} users...`);
    const results = [];

    for (const userForUpdate of userRiotIds) {
      try {
        const result = await prisma.user.update({
          where: { id: userForUpdate.id },
          data: { extRiotId: userForUpdate.extRiotId },
        });
        results.push(result);
      } catch (error) {
        console.error(`Error updating data for ${userForUpdate.id}:`, error);
      }
    }

    console.log(`\n\nSuccessfully seeded Riot IDs:`);
    console.log(`- Updated: ${results.length}`);
  } catch (error) {
    console.error('An error occurred:', error);
  } finally {
    await prisma.$disconnect();
  }
}

main();
