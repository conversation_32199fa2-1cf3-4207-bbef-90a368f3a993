/* eslint-disable @typescript-eslint/no-var-requires */

/**
 * This script creates a competitor bundle drop based on the provided configuration.
 *
 * To run this script:
 *  - specify the DATABASE_URL environment variable
 *  - specify the path to the configuration file.
 *
 * Example usage:
 *   - `DATABASE_URL=your_database_url npm run data-migration:create-drop -- --config=/path/to/config.json`
 *
 * Replace 'your_database_url' with the actual URL of your database and
 * '/path/to/config.json' with the path to your JSON configuration file.
 *
 * The JSON config file must match the format by the Yup `configSchema` defined below in this script.
 */

import { PrismaClient } from '@prisma/client';
import { Command } from 'commander';
import fs from 'fs/promises';
import readline from 'readline/promises';
import * as yup from 'yup';

import { dropConfigSchema } from '../../utils/drops';

const configSchema = getConfigSchema();

const program = new Command();

program.requiredOption('--config <path>', 'Path to the configuration file for creating a drop');

program.parse(process.argv);
const options = program.opts();

const prisma = new PrismaClient({
  datasources: {
    db: { url: process.env.DATABASE_URL },
  },
});

main();

async function main() {
  try {
    const configStr = await fs.readFile(options.config, 'utf-8');
    const config = await configSchema.validate(JSON.parse(configStr));

    console.log('Config:\n', JSON.stringify(config, null, 2));

    const rl = readline.createInterface({ input: process.stdin, output: process.stdout });
    const answer = await rl.question('\nAre you sure you want to proceed? (y/n): ');
    rl.close();

    if (answer.toLowerCase() === 'y') {
      const result = await prisma.$transaction(async (prisma: any) => {
        const drop = await prisma.drop.create({
          data: {
            name: config.dropName,
            config: config.dropConfig,
          },
        });

        await prisma.ticket.createMany({
          data: Array.from({ length: config.ticketCount }).map(() => ({
            dropId: drop.id,
          })),
        });

        const dropWithTickets = await prisma.drop.findFirstOrThrow({
          where: { name: config.dropName },
          include: { tickets: true },
        });

        return dropWithTickets;
      });

      console.log(`\n\nSuccessfully created drop ${result.name} with ${result.tickets.length} tickets`);
    } else {
      console.log('Aborted');
    }
  } catch (error) {
    console.error('An error occured:', error);
  } finally {
    await prisma.$disconnect();
  }
}

function getConfigSchema() {
  return yup.object().shape({
    dropName: yup.string().required(),
    dropConfig: dropConfigSchema,
    ticketCount: yup.number().integer().min(0).required(),
  });
}
