/* eslint-disable @typescript-eslint/no-var-requires */

/**
 * This script creates a specified number of tickets for the first competitor bundle drop.
 *
 * To run this script:
 *  - specify the DATABASE_URL environment variable
 *  - specify the number of tickets to create.
 *
 * Example usage:
 *   - `DATABASE_URL=your_database_url npm run data-migration:create-tickets -- -t 10`
 *
 * Replace 'your_database_url' with the actual URL of your database and
 * 10 with the number of tickets to create.
 */

import { PrismaClient } from '@prisma/client';
import { Command, InvalidArgumentError } from 'commander';
import { isInteger } from 'lodash';
import readline from 'readline/promises';

const program = new Command();

program.requiredOption('-t <ticket_number>', 'Number of tickets to generate.', (value) => {
  if (isInteger(value)) {
    return value;
  }
  throw new InvalidArgumentError('Please specify an integer.');
});

program.parse(process.argv);
const options = program.opts();

const prisma = new PrismaClient({ datasources: { db: { url: process.env.DATABASE_URL } } });

main();

async function main() {
  try {
    const rl = readline.createInterface({ input: process.stdin, output: process.stdout });
    const answer = await rl.question('\nAre you sure you want to proceed? (y/n): ');
    rl.close();

    if (answer.toLowerCase() !== 'y') {
      console.log('Aborted');
      return;
    }

    const drop = await prisma.drop.findFirstOrThrow();
    const ticketNo = options.t as number;

    const tickets = await prisma.$transaction(async (prisma: any) =>
      prisma.ticket.createManyAndReturn({
        data: Array.from({ length: ticketNo }).map(() => ({ dropId: drop.id })),
      }),
    );

    console.log(`\n\nSuccessfully created ${tickets.length} tickets.`);
  } catch (error) {
    console.error('An error occured:', error);
  } finally {
    await prisma.$disconnect();
  }
}
