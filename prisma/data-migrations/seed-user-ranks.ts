/**
 * This script seeds user ranks into the 'ranks' table in the database based on the provided JSON file.
 *
 * To run this script:
 *  - specify the DATABASE_URL environment variable
 *  - specify the path to the JSON file containing user ranks.
 *
 * Example usage:
 *   - `DATABASE_URL=your_database_url npm run data-migration:seed-ranks -- --file=/path/to/user-ranks.json`
 *
 * Replace 'your_database_url' with the actual URL of your database and
 * '/path/to/user-ranks.json' with the path to your JSON file containing user ranks.
 *
 * The JSON file should contain an array of rank objects matching the Rank interface defined below.
 */

import { PrismaClient } from '@prisma/client';
import { Command } from 'commander';
import fs from 'fs/promises';
import readline from 'readline/promises';
import * as yup from 'yup';

type Tier = 'CHALLENGER' | 'GRANDMASTER' | 'MASTER';

const rankSchema = yup.object().shape({
  summonerId: yup.string().required(),
  leaguePoints: yup.number().required(),
  rank: yup.string().required(),
  wins: yup.number().required(),
  losses: yup.number().required(),
  tier: yup.string().required().oneOf(['CHALLENGER', 'GRANDMASTER', 'MASTER']) as yup.StringSchema<Tier>,
  region: yup.string().required(),
  accountId: yup.string().required(),
  puuid: yup.string().required(),
});

const program = new Command();

program.requiredOption('--file <path>', 'Path to the JSON file containing user ranks');

program.parse(process.argv);
const options = program.opts();

const prisma = new PrismaClient({
  datasources: {
    db: { url: process.env.DATABASE_URL },
  },
});

main();

async function main() {
  try {
    const ranksStr = await fs.readFile(options.file, 'utf-8');
    const ranks = await yup.array().of(rankSchema).validate(JSON.parse(ranksStr));
    if (!ranks) {
      console.log('ranks are not defined, seeding aborted.');
      return;
    }

    console.log(`Loaded ${ranks.length} ranks from file.`);

    const rl = readline.createInterface({ input: process.stdin, output: process.stdout });
    const answer = await rl.question('\nAre you sure you want to proceed with seeding? (y/n): ');
    rl.close();

    if (answer.toLowerCase() === 'y') {
      const res = await prisma.rank.createMany({
        data: ranks,
        skipDuplicates: true,
      });

      console.log(`\n\nSuccessfully seeded ranks:`);
      console.log(`- Created: ${res.count}`);
      console.log(`- Skipped cause duplicate puuid: ${ranks.length - res.count}`);
    } else {
      console.log('Seeding aborted');
    }
  } catch (error) {
    console.error('An error occurred:', error);
  } finally {
    await prisma.$disconnect();
  }
}
