/* eslint-disable @typescript-eslint/no-var-requires */

/**
 * This script adjusts number of tickets in drop.
 *
 * To run this script:
 *  - specify the DATABASE_URL environment variable
 *  - specify number of tickets you want to add or remove
 *  - specify name of the drop for which you want to adjust tickets
 *
 * Example usage:
 *   - `DATABASE_URL=your_database_url npm run data-migration:adjust-tickets -- --ticket-count=TICKET_COUNT --drop-name=DROP_NAME`
 *
 * Replace 'your_database_url' with the actual URL of your database,
 * 'TICKET_COUNT' with the amount of ticket you want to add or remove (postive or negative number) and
 * 'DROP_NAME' with the name of the drop for which you want adjust the number of tickets.
 *
 */

import { PrismaClient } from '@prisma/client';
import { Command } from 'commander';
import readline from 'readline/promises';

const program = new Command();

program.requiredOption(
  '--ticket-count <number>',
  'Number of tickets to add/remove to/from drop. e.g. 50 will add 50 tickets, -50 will remove 50 tickets',
);
program.requiredOption('--drop-name <name>', 'Name of the drop for which you want to adjust tickets');

program.parse(process.argv);
const options = program.opts();

const prisma = new PrismaClient({
  datasources: {
    db: { url: process.env.DATABASE_URL },
  },
});

main();

async function main() {
  const ticketCount = parseInt(options.ticketCount, 10);
  const { dropName } = options;
  if (ticketCount > 0) {
    try {
      const rl = readline.createInterface({ input: process.stdin, output: process.stdout });
      const answer = await rl.question(
        `\nAttempting to add ${ticketCount} tickets to drop ${dropName}. Are you sure you want to proceed? (y/n): `,
      );
      rl.close();

      if (answer.toLowerCase() === 'y') {
        await prisma.$transaction(async (prisma: any) => {
          const drop = await prisma.drop.findUnique({
            where: {
              name: dropName,
            },
          });

          await prisma.ticket.createMany({
            data: Array.from({ length: ticketCount }).map(() => ({
              dropId: drop.id,
            })),
          });

          await prisma.drop.findFirstOrThrow({
            where: { name: dropName },
            include: { tickets: true },
          });
        });

        console.log(`\n\nSuccessfully added ${ticketCount} tickets to drop ${dropName}`);
      } else {
        console.log('Aborted');
      }
    } catch (error) {
      console.error('An error occured:', error);
    } finally {
      await prisma.$disconnect();
    }
  }

  if (ticketCount < 0) {
    try {
      const rl = readline.createInterface({ input: process.stdin, output: process.stdout });
      const answer = await rl.question(
        `\nAttempting to remove ${-ticketCount} tickets from drop ${dropName}. Are you sure you want to proceed? (y/n): `,
      );
      rl.close();

      if (answer.toLowerCase() === 'y') {
        const deletedCount = await prisma.$transaction(async (prisma: any) => {
          const drop = await prisma.drop.findUnique({
            where: {
              name: dropName,
            },
          });

          const ticketsToDelete = await prisma.ticket.findMany({
            where: {
              dropId: drop.id,
              OR: [
                { purchased: false, reserved: false },
                {
                  purchased: false,
                  reserved: true,
                  reservedUntil: { lt: new Date() }, // TODO Double check this date logic
                },
              ],
            },
            take: ticketCount,
            select: { id: true },
          });

          if (ticketsToDelete.length === 0) {
            console.log('\n\nNo tickets found to delete');
            return 0;
          }

          const deleteResult = await prisma.ticket.deleteMany({
            where: {
              id: { in: ticketsToDelete.map((ticket: any) => ticket.id) },
            },
          });

          return deleteResult.count;
        });

        console.log(`\n\nSuccessfully removed ${deletedCount} tickets from drop ${dropName}`);
      } else {
        console.log('Aborted');
      }
    } catch (error) {
      console.error('An error occured:', error);
    } finally {
      await prisma.$disconnect();
    }
  }
}
