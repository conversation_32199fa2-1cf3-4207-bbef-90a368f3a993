export interface AssetData {
  title: string;
  url: string;
  uid: string;
  content_type: string;
  filename: string;
  filesize: string;
}

export interface LinkData {
  path: {
    title: string;
    titleKey: string;
    href: string;
  };
  is_offsite: boolean;
  _metadata: { uid: string };
  sublinks: SublinkData[];
}

export interface SublinkData extends Omit<LinkData, 'sublinks'> {}

export interface WayToReachUsEntry {
  title: string;
  description: string;
  email: string;
}

export interface ZoomableGraphicsEntry {
  title: string;
  asset: AssetData;
}
