export interface CompetitorBundleInfo {
  competitorBundleInfoTitle: string;
  competitorBundleBodyFirstRow: string;
  competitorBundleBodySecondRow: string;
  competitorBundleBodyThirdRow: string;
  competitorBundleBodyFourthRow: string;
  competitorBundleBodyFifthRow: string;
  competitorBundleBodySixthRow: string;
}

export interface CompetitorBundleStrings {
  paymentMethod: string;
  cardNumber: string;
  yourCardNumber: string;
  expiration: string;
  CVV: string;
  CVVCode: string;
  or: string;
  priceBreakdown: string;
  tax: string;
  total: string;
  completePurchase: string;
  competitorPassTicket: string;
}

export interface PurchaseErrorInfo {
  title: string;
  body: string;
  imageUrl?: string;
  strings: {
    refreshPage: string;
  };
}
