'use client';

import { useSession } from 'next-auth/react';

import { Backdrop, TextWithLines } from '@/components/index';
import { useSiteConfig } from '@/context/SiteConfig';
import { Box, Button, FlexLayout, Input, LoadingIndicator, useScreenType } from '@/ui';

export const ProfileInformation = () => {
  const { localeJSON } = useSiteConfig();

  const { isMobile } = useScreenType();
  const { data: session } = useSession();

  return (
    <FlexLayout flexDirection="column" space={[6, 6, 10]}>
      {false && (
        <Backdrop>
          <LoadingIndicator />
        </Backdrop>
      )}
      <TextWithLines
        text={localeJSON['accountSettingsTitle'] ?? 'Profile Information'}
        hasLeftLine={false}
        hasRightLine={!isMobile}
        upperCase
        textVariant={['h7', 'h6', 'h5']}
        color="primary-midnight"
        rightLine={{ color: 'white10' }}
      />
      <FlexLayout flexDirection="column" space={8} bg="white" p={[4, 6]}>
        <Box sx={{ maxWidth: ['unset', 'unset', '884px'] }}>
          <Input
            type="text"
            value={session?.user.extDisplayName ?? ''}
            onChange={() => undefined}
            isDisabled
            labelLeft={localeJSON['accountSettingsDisplayName'] ?? 'Display Name'}
            labelColor="primary-midnight"
            helperText={
              localeJSON['accountSettingsHelpText'] ??
              'Your display name will be used on broadcast during the event. You can change this until October 23, 2024.'
            }
          />
        </Box>
        <Button size="medium" variant="primary" label="Save" isDisabled />
      </FlexLayout>
    </FlexLayout>
  );
};
