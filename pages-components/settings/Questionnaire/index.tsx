'use client';

import { useSession } from 'next-auth/react';
import { useState } from 'react';

import { Questionnaire as QuestionnaireModal } from '@/components/contentstack/Questionnaire';
import { TextWithLines } from '@/components/index';
import { useSiteConfig } from '@/context/SiteConfig';
import { getQuestionnaireProps } from '@/data/complete-profile';
import { Box, Button, FlexLayout, Text, useScreenType } from '@/ui';

const defaultInitialAnswers = {
  prefferpreferredLanguages: [],
  socialMediaHandles: '',
  gamerNameInfo: '',
  celebrationInfo: '',
  comingWithInfo: '',
  playstyleDescription: '',
  preparationInfo: '',
  otherGamesInfo: '',
  playerAdvice: '',
  playerFeeling: '',
};

interface QuestionnaireProps {
  initialAnswers: any;
}

export const Questionnaire = ({ initialAnswers }: QuestionnaireProps) => {
  const { data: session } = useSession();
  const siteConfig = useSiteConfig();
  const { localeJSON } = siteConfig;
  const answers = session ? initialAnswers ?? defaultInitialAnswers : {};

  const { isMobile } = useScreenType();

  const [openQuestionnaire, setOpenQuestionnaire] = useState(false);

  return (
    <>
      {session?.user && openQuestionnaire && (
        <Box
          sx={{
            position: 'fixed',
            top: 0,
            left: 0,
            width: '100vw',
            height: '100vh',
            backgroundColor: 'rgba(0, 0, 0, 0.5)',
            display: 'flex',
            justifyContent: 'center',
            alignItems: 'center',
            zIndex: 9999,
            overflowY: 'scroll',
          }}
          onClick={() => setOpenQuestionnaire(false)}
        >
          <Box
            sx={{
              maxWidth: '90vw',
              maxHeight: '90vh',
              overflow: 'auto',
            }}
            onClick={(e) => e.stopPropagation()}
          >
            <QuestionnaireModal
              asModalOptions={{
                onSkip: () => setOpenQuestionnaire(false),
                onClose: () => setOpenQuestionnaire(false),
              }}
              initialAnswers={answers}
              {...getQuestionnaireProps({ siteConfig })}
            />
          </Box>
        </Box>
      )}
      <FlexLayout flexDirection="column" space={[6, 6, 10]}>
        <TextWithLines
          text={localeJSON['accountSettingsQuestionnaireTitle'] ?? 'Competitor Questionnaire'}
          hasLeftLine={false}
          hasRightLine={!isMobile}
          upperCase
          textVariant={['h7', 'h6', 'h5']}
          color="primary-midnight"
          rightLine={{ color: 'white10' }}
        />

        <FlexLayout
          backgroundColor="white"
          p={[4, 6]}
          space={3}
          flexDirection="column"
          sx={{ height: ['auto', 'auto', '180px'] }}
        >
          <Text textVariant={['paragraph-xs-medium', 'paragraph-s-medium']} color="primary-midnight">
            {localeJSON['accountSettingsQuestionnaireDesc'] ??
              'You can edit answers to the questionnaire until October 23rd at 5AM PT / 2PM CEST / 8PM GMT+8'}
          </Text>
          <Button
            label={localeJSON['accountSettingsQuestionnaireCTA'] ?? 'View'}
            onClick={() => setOpenQuestionnaire(true)}
            size={isMobile ? 'small' : 'medium'}
            // isDisabled={!session?.user?.extHasTicket}
          />
        </FlexLayout>
      </FlexLayout>
    </>
  );
};
