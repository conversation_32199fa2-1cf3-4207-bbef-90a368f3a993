'use client';
import { useSession } from 'next-auth/react';
import { useMemo, useState } from 'react';

import { updateUser } from '@/apis/user';
import { TextWithLines } from '@/components/index';
import { useSiteConfig } from '@/context/SiteConfig';
import { FlexLayout, Text, Toggle, useScreenType } from '@/ui';

export const NotificationSettings = () => {
  const { localeJSON } = useSiteConfig();
  const { isMobile } = useScreenType();
  const { data: session, update: updateSession } = useSession();
  const [marketing, setMarketing] = useState(true);

  useMemo(() => {
    setMarketing(session?.user?.extMarketing ?? true);
  }, [session]);

  function handleToggle() {
    if (!session) return;

    updateUser(session.user.id, { marketing: !(session?.user?.extMarketing ?? true) }).then(() => updateSession());
  }

  return (
    <FlexLayout flexDirection="column" space={[6, 6, 10]}>
      <TextWithLines
        text={localeJSON['notificationSettingsTitle'] ?? 'Notification Settings'}
        hasLeftLine={false}
        hasRightLine={!isMobile}
        upperCase
        textVariant={['h7', 'h6', 'h5']}
        color="primary-midnight"
        rightLine={{ color: 'white10' }}
      />
      <FlexLayout
        flexDirection={['column-reverse', 'row']}
        alignItems={['flex-start', 'center']}
        space={8}
        bg="white"
        p={[4, 6]}
      >
        <Toggle isOn={marketing} handleToggle={handleToggle} colorOn="success" colorOff="gray400" />
        <FlexLayout flexDirection="column" space={2}>
          <Text as="p" color="primary-midnight" textVariant={['paragraph-s-medium', 'paragraph-l-medium']}>
            {localeJSON['marketingEmailsCTA'] ?? 'Marketing Emails'}
          </Text>
          <Text as="p" color="midnight80" textVariant={['paragraph-s-medium', 'paragraph-m-medium']}>
            {localeJSON['marketingEmailsInfo'] ?? 'Receive new and helpful information related to product updates.'}
          </Text>
        </FlexLayout>
      </FlexLayout>
    </FlexLayout>
  );
};
