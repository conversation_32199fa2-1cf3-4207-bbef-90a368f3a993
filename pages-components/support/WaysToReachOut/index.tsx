import { WayToReachUsEntry } from '@/interfaces';
import { FlexLayout, Link, Text } from '@/ui';

interface WaysToReachOutProps {
  items: Array<WayToReachUsEntry>;
}

export const WaysToReachOut = (props: WaysToReachOutProps) => {
  const { items } = props;
  return (
    <FlexLayout
      space={[12, 12, 39]}
      flexDirection={['column', 'column', 'row']}
      sx={{ maxWidth: ['100%', '295px', '100%'] }}
    >
      {items.map((item, index) => (
        <FlexLayout flexDirection="column" key={`item-${item.title}-${index}`} space={[3, 6]}>
          <Text textVariant="h9" upperCase color="primary-midnight">
            {item.title}
          </Text>
          <Text as="p" textVariant="paragraph-s-medium" color="primary-midnight">
            {item.description}
          </Text>
          <Link as="a" href={`mailto:${item.email}`} color="primary-macaron">
            <Text color="primary-macaron" textVariant="paragraph-s-medium">
              {item.email}
            </Text>
          </Link>
        </FlexLayout>
      ))}
    </FlexLayout>
  );
};
