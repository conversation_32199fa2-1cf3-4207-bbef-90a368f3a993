import { Box, Image, Text, useScreenType } from '@/ui';

interface SpectatorTagProps {
  isWithHeroOverlayComponent?: boolean;
}

export const SpectatorsTag = (props: SpectatorTagProps) => {
  const { isWithHeroOverlayComponent = false } = props;
  const { isMobile } = useScreenType();
  return isWithHeroOverlayComponent || !isMobile ? (
    <>
      <Box
        sx={{
          position: 'absolute',
          bottom: ['0px', '-80px'],
          right: isWithHeroOverlayComponent ? ['-70px', '-25px', '-25px'] : ['50%', '230px'],
          transform: isWithHeroOverlayComponent
            ? ['translate(0, 30%)', 'translate(50%, 50%)']
            : ['translate(50%, 40%)', 'translate(105%, 85%)'],
        }}
      >
        <Image src={'/images/hushtail.png'} alt="hushtail" width={240} height={180} />
      </Box>
      <Box
        sx={{
          position: 'absolute',
          bottom: ['102px', '10px'],
          right: isWithHeroOverlayComponent ? ['106px', '105px'] : ['50%', '230px'],
          transform: isWithHeroOverlayComponent
            ? ['translate(0, 30%)', 'translate(50%, 50%)']
            : ['translate(50%, 40%)', 'translate(50%, 110%)'],
        }}
      >
        <Image src={'/images/chat-box.png'} alt="Chat box" />
      </Box>
      <Box
        sx={{
          maxWidth: '110px',
          position: 'absolute',
          bottom: ['120px', '15px'],
          right: isWithHeroOverlayComponent ? ['118px', '95px'] : ['50%', '230px'],
          transform: isWithHeroOverlayComponent
            ? ['translate(0, 30%)', 'translate(50%, 50%)']
            : ['translate(50%, 40%)', 'translate(60%, 205%)'],
        }}
      >
        <Text textVariant={['paragraph-m-bold', 'paragraph-s-bold']} color="primary500" isCentered>
          Spectators,{' '}
          <Text
            textVariant={['paragraph-m-bold', 'paragraph-s-bold']}
            color="linkColor1"
            sx={{ cursor: 'pointer', '&:hover': { color: 'linkColorHover1' } }}
            onClick={() => {
              const element = document.getElementById('spectatorPass');
              element && element.scrollIntoView({ behavior: 'smooth' });
            }}
          >
            click here!
          </Text>
        </Text>
      </Box>
    </>
  ) : (
    <Box sx={{ position: 'relative' }}>
      <Image src={'/images/chat-box.png'} alt="Chat box" />
      <Box
        sx={{
          position: 'absolute',
          maxWidth: '110px',
          top: '20%',
          left: '17%',
        }}
      >
        <Text textVariant={['paragraph-m-bold', 'paragraph-s-bold']} color="primary500" isCentered>
          Spectators,{' '}
          <Text
            textVariant={['paragraph-m-bold', 'paragraph-s-bold']}
            color="linkColor1"
            sx={{ cursor: 'pointer', '&:hover': { color: 'linkColorHover1' } }}
            onClick={() => {
              const element = document.getElementById('spectatorPass');
              element && element.scrollIntoView({ behavior: 'smooth' });
            }}
          >
            click here!
          </Text>
        </Text>
      </Box>
      <Box
        sx={{
          position: 'absolute',
          right: '-120%',
          top: '50%',
        }}
      >
        <Image src={'/images/hushtail.png'} alt="hushtail" width={240} height={180} />
      </Box>
    </Box>
  );
};
