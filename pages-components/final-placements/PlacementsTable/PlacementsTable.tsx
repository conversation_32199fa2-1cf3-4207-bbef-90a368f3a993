import { useEffect, useState } from 'react';

import { Box, FlexLayout, Tag, Text, theme, useScreenType } from '@/ui';
import { capitaliseFirstLetter, getAbbreviatedRegionName } from '@/utils';
import { Placement, placements } from './placements';

export const PlacementsTable = () => {
  const { isMobile } = useScreenType();
  const [finalPlacements, setFinalPlacements] = useState<Placement[]>([]);

  useEffect(() => {
    //bandaid fix to dodge react hydration error
    setFinalPlacements(placements);
  }, []);

  return (
    <FlexLayout
      flexDirection="column"
      pt={[6, 10]}
      pb={[10, 10, 6]}
      px={[0, 6, '30px']}
      space={[10, 6]}
      sx={{ width: ['100vw', '100vw', '980px'] }}
      flex={1}
    >
      {finalPlacements.length && (
        <FlexLayout
          as="table"
          flexDirection="column"
          space={6}
          flex={1}
          sx={{
            overflowX: 'auto',
            '::-webkit-scrollbar': {
              height: '35px',
            },
            '::-webkit-scrollbar-track': {
              background: 'white10',
              borderTop: `31.5px solid ${theme.colors?.primary500}`,
              borderBottom: `1.5px solid ${theme.colors?.primary500}`,
            },
            '::-webkit-scrollbar-thumb': {
              background: 'primary200',
              borderTop: `30px solid ${theme.colors?.primary500}`,
            },
            width: '100%',
            borderSpacing: '0px 4px',
          }}
        >
          <Box as="thead">
            <FlexLayout sx={{ paddingX: '5px' }}>
              <Box sx={{ minWidth: isMobile ? 160 : 0, width: '30vw' }}>
                <Text upperCase textVariant={['h9', 'h8']}>
                  Placement
                </Text>
              </Box>
              <Box sx={{ minWidth: isMobile ? 204 : 0, width: '55vw' }}>
                <Text upperCase textVariant={['h9', 'h8']}>
                  Display name
                </Text>
              </Box>
              <Box sx={{ minWidth: isMobile ? 120 : 0, width: '15vw' }}>
                <Text upperCase textVariant={['h9', 'h8']}>
                  Region
                </Text>
              </Box>
            </FlexLayout>
          </Box>
          <Box as="tbody" sx={{ flex: 1, display: 'auto' }}>
            {finalPlacements.map((placement) => (
              <Box as="tr" key={placement.name + placement.rank} bg="primary300" sx={{ height: [60, 60, 60] }}>
                <Box as="td" key={placement.rank} bg="primary300" sx={{ minWidth: isMobile ? 160 : 0, width: '30vw' }}>
                  <FlexLayout alignItems="center" bg="inherit" pl={['70px', '80px', '80px']}>
                    <Text textVariant={['paragraph-s-bold', 'paragraph-m-bold']}>{placement.rank}</Text>
                  </FlexLayout>
                </Box>
                <Box as="td" key={placement.name} bg="primary300" sx={{ minWidth: isMobile ? 204 : 0, width: '55vw' }}>
                  <FlexLayout
                    flexDirection={['column', 'row']}
                    space={[1, 4]}
                    pl={[0, 0, '12px']}
                    alignItems={['flex-start', 'center']}
                  >
                    {['Challenger', 'Master', 'Grandmaster'].includes(placement.badge) && (
                      <Tag
                        variant={
                          placement.badge === 'Challenger'
                            ? 'level1'
                            : placement.badge === 'Master'
                            ? 'level2'
                            : 'level3'
                        }
                      >
                        <Text textVariant="label-m-bold" color="primary500">
                          {capitaliseFirstLetter(placement.badge)}
                        </Text>
                      </Tag>
                    )}
                    <Text textVariant={['paragraph-s-regular', 'paragraph-m-regular']}>{placement.name}</Text>
                  </FlexLayout>
                </Box>
                <Box
                  as="td"
                  key={placement.region + placement.name}
                  bg="primary300"
                  sx={{ minWidth: isMobile ? 120 : 0, width: '15vw' }}
                >
                  <FlexLayout alignItems="center" bg="inherit">
                    <Text textVariant={['paragraph-s-bold', 'paragraph-m-bold']}>
                      {getAbbreviatedRegionName(placement.region)}
                    </Text>
                  </FlexLayout>
                </Box>
              </Box>
            ))}
          </Box>
        </FlexLayout>
      )}
    </FlexLayout>
  );
};
