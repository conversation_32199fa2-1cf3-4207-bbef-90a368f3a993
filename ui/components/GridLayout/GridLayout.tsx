import { Property } from 'csstype';
import { forwardRef } from 'react';
import { Box, GridProps } from 'theme-ui';

export interface GridLayoutProps extends GridProps {
  alignItems?: Property.AlignItems | Property.AlignItems[];
  columnGap?: number | number[];
  gap?: number | number[];
  gridTemplateColumns?: Property.GridTemplateColumns | Property.GridTemplateColumns[];
  gridTemplateRows?: Property.GridTemplateRows | Property.GridTemplateRows[];
  justifyItems?: Property.JustifyItems | Property.JustifyItems[];
  rowGap?: number | number[];
}

export const GridLayout = forwardRef<any, GridLayoutProps>((props, ref) => {
  const {
    children,
    alignItems,
    columnGap,
    gap,
    gridTemplateColumns,
    gridTemplateRows,
    justifyItems,
    rowGap,
    sx,
    ...rest
  } = props;
  return (
    <Box
      as="div"
      ref={ref}
      sx={{
        ...sx,
        alignItems,
        columnGap,
        display: 'grid',
        gap,
        gridTemplateColumns,
        gridTemplateRows,
        justifyItems,
        rowGap,
      }}
      {...rest}
    >
      {children}
    </Box>
  );
});

GridLayout.displayName = 'GridLayout';
