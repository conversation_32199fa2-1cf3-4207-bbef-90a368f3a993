import { forwardRef } from 'react';
import { Text as ThemeText, TextProps as ThemeUiTextProps } from 'theme-ui';

import { useBpIndex } from '@/ui/hooks/useScreenType';
import { getResponsiveValue } from '@/utils';
import type { Color, TextVariant } from '../../theme';

export interface TextProps extends ThemeUiTextProps {
  color?: Color | 'inherit';
  textVariant?: TextVariant | [TextVariant, TextVariant] | [TextVariant, TextVariant, TextVariant];
  upperCase?: boolean;
  isCentered?: boolean;
}

export const Text = forwardRef<HTMLParagraphElement, TextProps>((props, ref) => {
  const {
    as = 'span',
    color = 'white',
    textVariant = 'paragraph-m-regular',
    upperCase,
    children,
    sx,
    isCentered,
    ...rest
  } = props;

  const bpIndex = useBpIndex();

  return (
    <ThemeText
      as={as}
      color={color}
      variant={getResponsiveValue(textVariant, bpIndex as number)}
      sx={{ textTransform: upperCase ? 'uppercase' : null, textAlign: isCentered ? 'center' : 'initial', ...sx }}
      {...rest}
      ref={ref}
    >
      {children}
    </ThemeText>
  );
});

Text.displayName = 'Text';
