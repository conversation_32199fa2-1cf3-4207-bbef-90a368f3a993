import { forwardRef } from 'react';

import { Box, FlexLayout, Text, useUncontrolled } from '@/ui';

const style = {
  unchecked: {
    background: 'black5',
    hoverStyles: { background: 'midnight100' },
    disabledStyles: { background: 'black10' },
  },
  checked: {
    background: 'midnight100',
    hoverStyles: {
      background: 'midnight100',
      '> div': {
        width: '14px',
        height: '14px',
        background: 'midnight600',
      },
    },
    disabledStyles: {
      background: 'black5',
      '> div': {
        background: 'black10',
      },
    },
  },
};

export type RadioOption = {
  value: string;
  label: string;
};

export interface RadioProps {
  option: RadioOption;
  onChange: (value: string) => void;
  isDisabled?: boolean;
  isSelected?: boolean;
}

export const Radio = forwardRef<any, RadioProps>(
  ({ option: { value, label }, onChange, isDisabled = false, isSelected = false, ...rest }, ref) => {
    const [_value, onValueChange] = useUncontrolled({
      value: value,
      onChange: onChange,
    });
    const { background, hoverStyles, disabledStyles } = style[isSelected ? 'checked' : 'unchecked'];

    return (
      <FlexLayout alignItems="center" space={8} ref={ref} {...rest}>
        <Box as="input" sx={{ position: 'absolute', visibility: 'hidden' }} type="radio" value={_value} />
        <FlexLayout
          alignItems="center"
          justifyContent="center"
          bg={background}
          sx={{
            cursor: !isDisabled && 'pointer',
            width: '24px',
            height: '24px',
            borderRadius: '50%',
            '&:hover': !isDisabled && hoverStyles,
            ...(isDisabled && disabledStyles),
          }}
          onClick={() => !isDisabled && onValueChange(_value)}
        >
          {isSelected && (
            <Box
              sx={{
                width: '16px',
                height: '16px',
                borderRadius: '50%',
                background: 'primary200',
              }}
            />
          )}
        </FlexLayout>
        <Text textVariant="paragraph-s-regular" color="primary500">
          {label}
        </Text>
      </FlexLayout>
    );
  },
);

Radio.displayName = 'Radio';
