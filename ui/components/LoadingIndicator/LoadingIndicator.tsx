import { keyframes } from '@emotion/react';

import { Box, Color } from '@/ui';

const spinAnimation = keyframes`
  from {
    transform: rotate(0);
  }
  to {
    transform: rotate(360deg);
  }
`;

interface LoadingIndicatorProps {
  colorOne?: Color;
  colorTwo?: Color;
  sizePx?: number;
}

export const LoadingIndicator = (props: LoadingIndicatorProps) => {
  const { colorOne = 'white25', colorTwo = 'white', sizePx = 70 } = props;

  return (
    <Box sx={{ width: `${sizePx}px`, height: `${sizePx}px` }}>
      <Box
        sx={{
          width: '100%',
          height: '100%',
          border: '3px solid',
          borderColor: colorOne,
          borderRightColor: colorTwo,
          borderRadius: '50%',
          animation: `${spinAnimation} 0.8s linear infinite`,
        }}
        className="loading-indicator"
      />
    </Box>
  );
};
