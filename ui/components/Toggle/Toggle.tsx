import React from 'react';

import { Color } from '../../theme';
import { Box } from '../Box/Box';

interface ToggleProps {
  isOn: boolean;
  handleToggle: () => void;
  colorOn?: Color;
  colorOff?: Color;
  knobColor?: Color;
}

export const Toggle = (props: ToggleProps) => {
  const { isOn, handleToggle, colorOn = 'successGreen', colorOff = 'black20', knobColor = 'white' } = props;

  return (
    <Box
      onClick={handleToggle}
      sx={{
        position: 'relative',
        width: '62px',
        height: '33px',
        minWidth: '62px',
        borderRadius: '100px',
        cursor: 'pointer',
      }}
      backgroundColor={isOn ? colorOn : colorOff}
    >
      <Box
        sx={{
          width: '29px',
          height: '29px',
          position: 'absolute',
          top: '2px',
          left: isOn ? 'initial' : '2px',
          right: isOn ? '2px' : 'initial',
          borderRadius: '100%',
        }}
        backgroundColor={knobColor}
      />
    </Box>
  );
};
