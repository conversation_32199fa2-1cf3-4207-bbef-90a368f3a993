import { forwardRef } from 'react';
import type { LinkProps as ThemeLinkProps } from 'theme-ui';
import { Link as ThemeLink } from 'theme-ui';

export interface LinkProps extends ThemeLinkProps {}

export const Link = forwardRef<any, LinkProps>((props, ref) => {
  const { href, target, sx, children, ...rest } = props;

  return (
    <ThemeLink
      href={href}
      ref={ref}
      target={target}
      sx={{
        textDecoration: 'none',
        width: 'fit-content',
        cursor: 'pointer',
        ...sx,
      }}
      {...rest}
    >
      {children}
    </ThemeLink>
  );
});

Link.displayName = 'Link';
