import { forwardRef } from 'react';
import { ThemeUIStyleObject } from 'theme-ui';

import { Button, useScreenType } from '@/ui';

export interface TabProps {
  label: string;
  isSelected: boolean;
  onClick: () => void;
  sx?: ThemeUIStyleObject;
  size?: 'large' | 'medium' | 'small';
}

export const Tab = forwardRef<HTMLButtonElement, TabProps>((props, ref) => {
  const { isDesktop } = useScreenType();

  const { label, isSelected, onClick, size = isDesktop ? 'large' : 'medium', sx, ...rest } = props;

  return (
    <Button
      variant={isSelected ? 'tertiary' : 'secondary'}
      label={label}
      onClick={onClick}
      sx={sx}
      size={size}
      ref={ref}
      {...rest}
    />
  );
});

Tab.displayName = 'Tab';
