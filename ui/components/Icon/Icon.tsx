import * as icons from '../../assets/icons';
import { Box, BoxProps } from '../Box';

export const iconSizesMap = {
  xxl: '128px',
  xl: '64px',
  l54: '54px',
  l48: '48px',
  l: '32px',
  m: '24px',
  s: '20px',
  xs: '16px',
};

export type IconType = keyof typeof icons;

export interface IconProps extends BoxProps {
  ariaLabel?: string;
  color?: string;
  icon: IconType;
  size?: keyof typeof iconSizesMap;
}

export const Icon: React.FC<IconProps> = ({ ariaLabel, color, icon, size = 'm', sx, onClick, ...rest }) => {
  const IconComponent = icons[icon];

  return (
    <Box
      aria-label={ariaLabel}
      onClick={onClick}
      sx={{
        ...sx,
        color,
        flex: 'none',
        height: iconSizesMap[size],
        width: iconSizesMap[size],
      }}
      {...rest}
    >
      <IconComponent height="100%" width="100%" />
    </Box>
  );
};

export type IconSize = keyof typeof iconSizesMap;
