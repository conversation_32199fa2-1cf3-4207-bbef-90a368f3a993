import { Property } from 'csstype';
import { type ChangeEvent, type FocusEvent, forwardRef, KeyboardEvent } from 'react';
import { StylePropertyValue, ThemeUICSSObject, ThemeUIStyleObject } from 'theme-ui';

import { Box, Color, colors, FlexLayout, Icon, IconType, Text, useUncontrolled } from '@/ui';
import { fonts } from '@/ui/theme/typography/fonts';

interface CommonInputProps {
  onBlur?: (e: FocusEvent<HTMLInputElement>) => void;
  onKeyDown?: (e: KeyboardEvent<HTMLImageElement>) => void;
  onFocus?: (e: FocusEvent<HTMLInputElement>) => void;
  name?: string;
  placeholder?: string;
  isDisabled?: boolean;
  iconLeft?: IconType;
  iconRight?: IconType;
  width?: StylePropertyValue<Property.Width<string | number> | undefined>;
  helperText?: string;
  errorText?: string;
  labelLeft?: string;
  labelRight?: string;
  labelColor?: Color;
  onRightIconClick?: () => void;
  theme?: 'light' | 'dark' | 'transparent';
  placeholderSx?: ThemeUIStyleObject;
}

interface NumberInputProps extends CommonInputProps {
  type: 'number';
  value: number;
  onChange: (value: number) => void;
}

interface StringInputProps extends CommonInputProps {
  type: 'text' | 'email' | 'password' | 'tel';
  value: string;
  onChange: (value: string) => void;
}

type InputProps = NumberInputProps | StringInputProps;

export const Input = forwardRef<HTMLInputElement, InputProps>((props, ref) => {
  const {
    value,
    onChange,
    onBlur,
    onKeyDown,
    onFocus,
    name,
    placeholder,
    isDisabled = false,
    iconLeft,
    iconRight,
    width = '100%',
    type,
    helperText,
    errorText,
    labelLeft,
    labelRight,
    labelColor = 'midnight900',
    onRightIconClick,
    theme = 'light',
    placeholderSx,
    ...rest
  } = props;

  const lightStyles: ThemeUICSSObject = {
    fontSize: '16px',
    color: 'midnight900',
    '::placeholder': {
      color: 'black20',
      fontStyle: 'normal',
      fontWeight: 'normal',
      fontSize: '16px',
      lineHeight: '22px',
      ...placeholderSx,
    },
  };

  const darkStyles: ThemeUICSSObject = {
    fontSize: '14px',
    color: 'white',
    '::placeholder': {
      color: 'white',
      fontStyle: 'normal',
      fontWeight: 'normal',
      fontSize: '16px',
      lineHeight: '22px',
      ...placeholderSx,
    },
  };

  const [_value, onValueChange] = useUncontrolled({
    defaultValue: placeholder,
    value: value,
    onChange: onChange,
  });

  const iconColor = ['dark', 'transparent'].includes(theme) ? 'white' : 'primary500';

  return (
    <FlexLayout flexDirection="column" space={2} sx={{ width }}>
      {(labelLeft || labelRight) && (
        <FlexLayout justifyContent="space-between">
          {labelLeft && (
            <Text color={labelColor} textVariant="paragraph-xs-medium">
              {labelLeft}
            </Text>
          )}
          {labelRight && (
            <Text color={labelColor} textVariant="paragraph-xs-medium">
              {labelRight}
            </Text>
          )}
        </FlexLayout>
      )}
      <FlexLayout flexDirection="column" space={2}>
        <FlexLayout
          alignItems="center"
          justifyContent="flex-start"
          bg={theme === 'transparent' ? 'rgba(255,255,255,0.2)' : '#F8F8F9'}
          px={theme === 'dark' ? 5 : 4}
          py={4}
          sx={{
            border: `1px solid transparent`,
            ':hover': { border: `1px solid ${colors.midnight200}` },
            ':focus-within': { border: `1px solid ${colors.midnight200}` },
            '&[disabled]': { background: 'gray300' },
          }}
          {...{ disabled: isDisabled, ...rest }}
        >
          {iconLeft && <Icon color={iconColor} icon={iconLeft} mr={4} />}
          <Box
            as="input"
            bg="transparent"
            placeholder={placeholder}
            ref={ref}
            sx={{
              border: 'none',
              width: '100%',
              height: '100%',
              outline: 'none',
              fontFamily: fonts['Transducer'],
              fontStyle: 'normal',
              fontWeight: 'normal',
              lineHeight: '24px',
              '::-webkit-outer-spin-button': {
                WebkitAppearance: 'none',
                margin: 0,
              },
              '::-webkit-inner-spin-button': {
                WebkitAppearance: 'none',
                margin: 0,
              },
              MozAppearance: 'textfield',
              p: 0,
              ...(theme === 'light' && lightStyles),
              ...(['dark', 'transparent'].includes(theme) && darkStyles),
            }}
            type={type}
            value={String(_value)}
            onChange={(e: ChangeEvent<HTMLInputElement>) => onValueChange(e.target.value)}
            onBlur={onBlur}
            onKeyDown={onKeyDown}
            onFocus={onFocus}
            {...{ disabled: isDisabled }}
            {...{ name }}
          />
          {iconRight && (
            <Icon
              color={iconColor}
              icon={iconRight}
              onClick={onRightIconClick}
              sx={{ cursor: onRightIconClick ? 'pointer' : 'auto' }}
            />
          )}
        </FlexLayout>
        {helperText && (
          <Text color="black50" variant="paragraph-xs-medium">
            {helperText}
          </Text>
        )}
        {errorText?.trim() && (
          <Text color="alertRed" variant="paragraph-xs-medium">
            {errorText}
          </Text>
        )}
      </FlexLayout>
    </FlexLayout>
  );
});

Input.displayName = 'Input';
