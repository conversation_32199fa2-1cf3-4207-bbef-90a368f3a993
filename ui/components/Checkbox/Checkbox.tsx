import { FocusEventHandler, forwardRef, ReactElement } from 'react';

import { FlexLayout, Icon, Text, useUncontrolled } from '@/ui';

const style = {
  unchecked: {
    background: 'black5',
    hoverStyles: { background: 'midnight100' },
    disabledStyles: { background: 'black10' },
  },
  checked: {
    background: 'midnight600',
    hoverStyles: { background: 'midnight100' },
    disabledStyles: { background: 'black10' },
  },
  indeterminate: {
    background: 'midnight600',
    hoverStyles: { background: 'midnight100' },
    disabledStyles: { background: 'black10' },
  },
};

export interface CheckboxProps {
  /** Default value for uncontrolled component, false by default */
  defaultIsChecked?: boolean;
  /** Value for controlled component */
  isChecked?: boolean;
  /** Specifies if the checkbox is in indeterminate state */
  isIndeterminate?: boolean;
  /** Specifies if the checkbox is disabled, false by default */
  isDisabled?: boolean;
  /** Callback for controlled component */
  onChange?: (isChecked: boolean) => void;
  /** Value for checkbox component */
  label?: string | ReactElement;
  /** Callback for onBlur event */
  onBlur?: FocusEventHandler<HTMLDivElement>;
}

export const Checkbox = forwardRef<HTMLDivElement, CheckboxProps>(
  ({ defaultIsChecked, isChecked, isDisabled, isIndeterminate, label, onBlur, ...rest }, ref) => {
    const [_value, onChange] = useUncontrolled({
      defaultValue: defaultIsChecked,
      finalValue: false,
      value: isChecked,
      onChange: rest.onChange,
    });
    const { background, hoverStyles, disabledStyles } =
      style[isIndeterminate ? 'indeterminate' : isChecked ? 'checked' : 'unchecked'];

    return (
      <FlexLayout space={4} alignItems="center">
        <FlexLayout
          onBlur={onBlur}
          alignItems="center"
          aria-checked={_value}
          aria-disabled={isDisabled}
          data-testid="checkbox"
          justifyContent="center"
          ref={ref}
          role="checkbox"
          bg={background}
          sx={{
            cursor: !isDisabled && 'pointer',
            height: '24px',
            width: '24px',
            minWidth: '24px',
            minHeight: '24px',
            borderRadius: '4px',
            '&:hover': !isDisabled && hoverStyles,
            ...(isDisabled && disabledStyles),
          }}
          tabIndex={isDisabled ? -1 : 0}
          onClick={() => {
            !isDisabled && onChange(!_value);
          }}
        >
          {!!_value && <Icon color="white" data-testid="checkbox-icon" icon="checkmark" />}
          {!!isIndeterminate && <Icon color="white" data-testid="checkbox-icon" icon="minus" />}
        </FlexLayout>
        <Text textVariant="paragraph-xs-medium" color="midnight80">
          {label}
        </Text>
      </FlexLayout>
    );
  },
);

Checkbox.displayName = 'Checkbox';
