import { forwardRef, HTMLInputTypeAttribute } from 'react';
import type { BoxProps as ThemeBoxProps } from 'theme-ui';
import { Box as ThemeBox } from 'theme-ui';

export interface BoxProps extends ThemeBoxProps {
  type?: HTMLInputTypeAttribute;
  value?: string;
}

export const Box = forwardRef<any, BoxProps>((props, ref) => {
  const { children, type, value, ...rest } = props;

  return (
    <ThemeBox as="div" ref={ref} {...{ type, value, ...rest }}>
      {children}
    </ThemeBox>
  );
});

Box.displayName = 'Box';
