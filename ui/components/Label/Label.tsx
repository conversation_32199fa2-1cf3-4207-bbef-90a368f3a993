import React, { forwardRef, PropsWithChildren } from 'react';
import { ThemeUIStyleObject } from 'theme-ui';

import { Box, FlexLayout, Icon, IconType } from '@/ui';
import { LabelTypes, labelVariants } from './variants';

export interface LabelProps extends PropsWithChildren {
  variant: LabelTypes;
  icon?: IconType;
  sx?: ThemeUIStyleObject;
}

export const Label = forwardRef<HTMLDivElement, LabelProps>((props, ref) => {
  const { variant, icon, children, sx = {}, ...rest } = props;

  const { background, px, py, iconColor = 'white', space = 6, clipPath } = labelVariants[variant];

  return (
    <Box
      px={px}
      py={py}
      sx={{
        background: background,
        clipPath,
        ...sx,
      }}
      ref={ref}
      {...rest}
    >
      <FlexLayout justifyContent="center" alignItems="center" space={space}>
        {icon && <Icon color={iconColor} icon={icon} />}
        {children}
      </FlexLayout>
    </Box>
  );
});

Label.displayName = 'Label';
