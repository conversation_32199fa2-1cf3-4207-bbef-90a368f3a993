import { Property } from 'csstype';

import { Color } from '@/ui/theme';

interface LabelVariantStyle {
  background: Property.Background | Color;
  clipPath: Property.ClipPath;
  iconColor?: Color;
  px: Property.Padding;
  py: Property.Padding;
  space?: number;
}

type SansSerifTypes =
  | 'sans-serif-regular-light'
  | 'sans-serif-regular-dark'
  | 'sans-serif-flat-left-light'
  | 'sans-serif-flat-right-light'
  | 'sans-serif-flat-left-dark'
  | 'sans-serif-flat-right-dark';

const sansSerif: Record<SansSerifTypes, LabelVariantStyle> = {
  'sans-serif-regular-light': {
    background: 'primary300',
    px: '64px',
    py: '24px',
    clipPath: 'polygon(5% 0%, 100% 0%, 95% 100%, 0% 100%)',
  },
  'sans-serif-regular-dark': {
    background: 'primary500',
    px: '64px',
    py: '24px',
    clipPath: 'polygon(5% 0%, 100% 0%, 95% 100%, 0% 100%)',
  },
  'sans-serif-flat-left-light': {
    background: 'primary300',
    px: '64px',
    py: '24px',
    clipPath: 'polygon(0% 0%, 100% 0%, 90% 100%, 0% 100%)',
  },
  'sans-serif-flat-left-dark': {
    background: 'primary500',
    px: '64px',
    py: '24px',
    clipPath: 'polygon(0% 0%, 100% 0%, 90% 100%, 0% 100%)',
  },
  'sans-serif-flat-right-light': {
    background: 'primary300',
    px: '64px',
    py: '24px',
    clipPath: 'polygon(10% 0%, 100% 0%, 100% 100%, 0% 100%)',
  },
  'sans-serif-flat-right-dark': {
    background: 'primary500',
    px: '64px',
    py: '24px',
    clipPath: 'polygon(10% 0%, 100% 0%, 100% 100%, 0% 100%)',
  },
};

type SerifTypes =
  | 'serif-regular-light'
  | 'serif-regular-dark'
  | 'serif-flat-left-light'
  | 'serif-flat-right-light'
  | 'serif-flat-left-dark'
  | 'serif-flat-right-dark';

const serif: Record<SerifTypes, LabelVariantStyle> = {
  'serif-regular-light': {
    background: 'primary300',
    px: '64px',
    py: '16px',
    clipPath: 'polygon(5% 0%, 100% 0%, 95% 100%, 0% 100%)',
  },
  'serif-regular-dark': {
    background: 'primary500',
    px: '64px',
    py: '16px',
    clipPath: 'polygon(5% 0%, 100% 0%, 95% 100%, 0% 100%)',
  },
  'serif-flat-left-light': {
    background: 'primary300',
    px: '64px',
    py: '16px',
    clipPath: 'polygon(0% 0%, 100% 0%, 90% 100%, 0% 100%)',
  },
  'serif-flat-left-dark': {
    background: 'primary500',
    px: '64px',
    py: '16px',
    clipPath: 'polygon(0% 0%, 100% 0%, 90% 100%, 0% 100%)',
  },
  'serif-flat-right-light': {
    background: 'primary300',
    px: '64px',
    py: '16px',
    clipPath: 'polygon(10% 0%, 100% 0%, 100% 100%, 0% 100%)',
  },
  'serif-flat-right-dark': {
    background: 'primary500',
    px: '64px',
    py: '16px',
    clipPath: 'polygon(10% 0%, 100% 0%, 100% 100%, 0% 100%)',
  },
};

type MobileTypes =
  | 'mobile-regular-light'
  | 'mobile-regular-dark'
  | 'mobile-flat-left-light'
  | 'mobile-flat-right-light'
  | 'mobile-flat-left-dark'
  | 'mobile-flat-right-dark';

const mobile: Record<MobileTypes, LabelVariantStyle> = {
  'mobile-regular-light': {
    background: 'primary300',
    px: '24px',
    py: '12px',
    space: 2,
    clipPath: 'polygon(5% 0%, 100% 0%, 95% 100%, 0% 100%)',
  },
  'mobile-regular-dark': {
    background: 'primary500',
    px: '24px',
    py: '12px',
    space: 2,
    clipPath: 'polygon(5% 0%, 100% 0%, 95% 100%, 0% 100%)',
  },
  'mobile-flat-left-light': {
    background: 'primary300',
    px: '24px',
    py: '12px',
    space: 2,
    clipPath: 'polygon(0% 0%, 100% 0%, 90% 100%, 0% 100%)',
  },
  'mobile-flat-left-dark': {
    background: 'primary500',
    px: '24px',
    py: '12px',
    space: 2,
    clipPath: 'polygon(0% 0%, 100% 0%, 90% 100%, 0% 100%)',
  },
  'mobile-flat-right-light': {
    background: 'primary300',
    px: '24px',
    py: '12px',
    space: 2,
    clipPath: 'polygon(10% 0%, 100% 0%, 100% 100%, 0% 100%)',
  },
  'mobile-flat-right-dark': {
    background: 'primary500',
    px: '24px',
    py: '12px',
    space: 2,
    clipPath: 'polygon(10% 0%, 100% 0%, 100% 100%, 0% 100%)',
  },
};

type SerifMobileTypes =
  | 'serif-mobile-regular-light'
  | 'serif-mobile-regular-dark'
  | 'serif-mobile-flat-left-light'
  | 'serif-mobile-flat-right-light'
  | 'serif-mobile-flat-left-dark'
  | 'serif-mobile-flat-right-dark';

const serifMobile: Record<SerifMobileTypes, LabelVariantStyle> = {
  'serif-mobile-regular-light': {
    background: 'primary300',
    px: '24px',
    py: '12px',
    space: 2,
    clipPath: 'polygon(5% 0%, 100% 0%, 95% 100%, 0% 100%)',
  },
  'serif-mobile-regular-dark': {
    background: 'primary500',
    px: '24px',
    py: '12px',
    space: 2,
    clipPath: 'polygon(5% 0%, 100% 0%, 95% 100%, 0% 100%)',
  },
  'serif-mobile-flat-left-light': {
    background: 'primary300',
    px: '24px',
    py: '12px',
    space: 2,
    clipPath: 'polygon(0% 0%, 100% 0%, 90% 100%, 0% 100%)',
  },
  'serif-mobile-flat-left-dark': {
    background: 'primary500',
    px: '24px',
    py: '12px',
    space: 2,
    clipPath: 'polygon(0% 0%, 100% 0%, 90% 100%, 0% 100%)',
  },
  'serif-mobile-flat-right-light': {
    background: 'primary300',
    px: '24px',
    py: '12px',
    space: 2,
    clipPath: 'polygon(10% 0%, 100% 0%, 100% 100%, 0% 100%)',
  },
  'serif-mobile-flat-right-dark': {
    background: 'primary500',
    px: '24px',
    py: '12px',
    space: 2,
    clipPath: 'polygon(10% 0%, 100% 0%, 100% 100%, 0% 100%)',
  },
};

type AuxiliaryTypes = 'auxiliary-regular-light' | 'auxiliary-flat-left-light' | 'auxiliary-flat-right-light';

const auxiliary: Record<AuxiliaryTypes, LabelVariantStyle> = {
  'auxiliary-regular-light': {
    background: 'white',
    iconColor: 'primary500',
    px: '24px',
    py: '8px',
    space: 2,
    clipPath: 'polygon(5% 0%, 100% 0%, 95% 100%, 0% 100%)',
  },
  'auxiliary-flat-left-light': {
    background: 'white',
    iconColor: 'primary500',
    px: '24px',
    py: '8px',
    space: 2,
    clipPath: 'polygon(0% 0%, 100% 0%, 90% 100%, 0% 100%)',
  },
  'auxiliary-flat-right-light': {
    background: 'white',
    iconColor: 'primary500',
    px: '24px',
    py: '8px',
    space: 2,
    clipPath: 'polygon(10% 0%, 100% 0%, 100% 100%, 0% 100%)',
  },
};

export type LabelTypes = SansSerifTypes | SerifTypes | MobileTypes | SerifMobileTypes | AuxiliaryTypes;

export const labelVariants: Record<LabelTypes, LabelVariantStyle> = {
  ...sansSerif,
  ...serif,
  ...mobile,
  ...serifMobile,
  ...auxiliary,
};
