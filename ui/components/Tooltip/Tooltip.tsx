import * as TooltipPrimitive from '@radix-ui/react-tooltip';
import { useToggle } from '@uidotdev/usehooks';

import { useScreenType } from '@/ui/hooks';
import { Box } from '../Box';

import './style.css';

interface TooltipProps {
  content: React.ReactNode;
  children: React.ReactNode;
  arrowColor: string;
  side?: 'bottom' | 'left' | 'right' | 'top';
}

export const Tooltip = ({ content, children, side = 'top', arrowColor }: TooltipProps) => {
  const { isDesktop } = useScreenType();
  const [isOpen, toggleIsOpen] = useToggle(false);

  return (
    <TooltipPrimitive.Root open={isOpen} onOpenChange={toggleIsOpen}>
      <TooltipPrimitive.Trigger asChild>
        <Box sx={{ width: 'fit-content' }} onClick={isDesktop ? undefined : () => toggleIsOpen()}>
          {children}
        </Box>
      </TooltipPrimitive.Trigger>
      <TooltipPrimitive.Portal>
        <TooltipPrimitive.Content side={side} align="center" sideOffset={12} className="TooltipContent">
          {content}
          <TooltipPrimitive.Arrow style={{ fill: arrowColor }} />
        </TooltipPrimitive.Content>
      </TooltipPrimitive.Portal>
    </TooltipPrimitive.Root>
  );
};

export const TooltipProvider = (props: { children: React.ReactNode }) => (
  <TooltipPrimitive.Provider delayDuration={200} {...props} />
);
