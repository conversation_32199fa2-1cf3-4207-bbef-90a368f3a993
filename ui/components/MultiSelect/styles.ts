import styles from '../Select/styles';

export default function mergeStyles(type: 'dark' | 'light' | 'transparent') {
  return {
    ...styles(type),
    multiValue: (base: any) => ({
      ...base,
      backgroundColor: 'transparent',
      padding: 0,
      margin: 0,
    }),
    multiValueLabel: (base: any) => ({
      ...base,
      padding: '0 !important',
    }),
    multiValueRemove: (base: any) => ({
      ...base,
      padding: 0,
      '&:hover': {
        cursor: 'pointer',
        backgroundColor: 'transparent',
      },
    }),
  };
}
