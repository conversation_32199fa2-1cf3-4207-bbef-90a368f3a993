import {
  ClearIndicatorProps,
  components,
  MultiValueGenericProps,
  MultiValueRemoveProps,
  ValueContainerProps,
} from 'react-select';

import { FlexLayout, Icon, SelectOption, Text } from '@/ui';

export const ValueContainer = (props: ValueContainerProps<SelectOption, boolean, any>) => {
  return (
    <components.ValueContainer {...props}>
      <FlexLayout sx={{ alignItems: 'center', gap: 1 }}>{props.children}</FlexLayout>
    </components.ValueContainer>
  );
};

export const ClearIndicator = (
  props: ClearIndicatorProps<SelectOption, boolean> & {
    selectType: 'light' | 'dark' | 'transparent';
  },
) => {
  return (
    <components.ClearIndicator {...props}>
      <Icon
        icon="close"
        color={props.selectType === 'dark' ? 'white' : 'black'}
        sx={{ ':hover': { color: 'gray400' } }}
      />
    </components.ClearIndicator>
  );
};

export const MultiValueContainer = (props: MultiValueGenericProps<SelectOption, boolean, any>) => {
  return (
    <components.MultiValueContainer {...props}>
      <FlexLayout sx={{ p: '2px', alignItems: 'center', gap: 1, backgroundColor: 'lavender', borderRadius: 'm' }}>
        {props.children}
      </FlexLayout>
    </components.MultiValueContainer>
  );
};

export const MultiValueLabel = (
  props: MultiValueGenericProps<SelectOption, boolean, any> & { selectType: 'light' | 'dark' | 'transparent' },
) => {
  return (
    <components.MultiValueLabel {...props}>
      <FlexLayout sx={{ alignItems: 'center' }}>
        {props.data.icon}
        <Text color={props.selectType === 'dark' ? 'white' : 'primary500'} textVariant="paragraph-xs-regular">
          {props.children}
        </Text>
      </FlexLayout>
    </components.MultiValueLabel>
  );
};

export const MultiValueRemove = (props: MultiValueRemoveProps<SelectOption, boolean, any>) => {
  return (
    <components.MultiValueRemove {...props}>
      <Icon color="black" icon="close" size="s" />
    </components.MultiValueRemove>
  );
};
