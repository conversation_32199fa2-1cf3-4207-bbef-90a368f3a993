import keyBy from 'lodash/keyBy';
import { forwardRef } from 'react';
import Select, {
  ClearIndicatorProps,
  DropdownIndicatorProps,
  GroupBase,
  MultiValue,
  MultiValueGenericProps,
  OptionProps,
  SingleValueProps,
} from 'react-select';

import { FlexLayout, SelectOption, SelectProps, Text } from '@/ui';
import * as singleSelectComponents from '../Select/components';
import * as multiSelectComponents from './components';
import multiSelectStyles from './styles';

export interface MultiSelectProps extends Omit<SelectProps, 'value' | 'onChange'> {
  options: SelectOption[];
  values: SelectOption['value'][];
  onChange: (values: SelectOption['value'][]) => void;
  allowInput?: (inputValue: string) => boolean;
  isSortable?: boolean;
}

export const MultiSelect = forwardRef<any, MultiSelectProps>((props, ref) => {
  const {
    isDisabled = false,
    isSearchable = true,
    values,
    options,
    name,
    placeholder = 'Select',
    onChange,
    onBlur,
    allowInput,
    width = '100%',
    label,
    type = 'light',
  } = props;

  const optionsMap = keyBy(options, (option: SelectOption) => option?.value);

  function getSelectValue(values: SelectOption['value'][]) {
    if (!values) {
      return undefined;
    }

    return values.map((value) => {
      if (value) {
        return optionsMap[value];
      }
    });
  }

  return (
    <FlexLayout flexDirection="column" space={2} sx={{ width }}>
      {label && (
        <Text color="midnight900" textVariant="paragraph-xs-medium">
          {label}
        </Text>
      )}
      <Select
        closeMenuOnSelect={false}
        components={{
          ...singleSelectComponents,
          ...multiSelectComponents,
          SingleValue: (singleValueProps: SingleValueProps<SelectOption>) => (
            <singleSelectComponents.SingleValue {...singleValueProps} selectType={type} />
          ),
          Option: (optionProps: OptionProps<SelectOption>) => (
            <singleSelectComponents.Option {...optionProps} selectType={type} />
          ),
          DropdownIndicator: (
            dropdownIndicatorProps: DropdownIndicatorProps<SelectOption, boolean, GroupBase<SelectOption>>,
          ) => (
            <singleSelectComponents.DropdownIndicator
              {...dropdownIndicatorProps}
              hasDropdownIndicator
              selectType={type}
            />
          ),
          ClearIndicator: (clearIndicatorProps: ClearIndicatorProps<SelectOption>) => (
            <multiSelectComponents.ClearIndicator {...clearIndicatorProps} selectType={type} />
          ),
          MultiValueLabel: (multiValueLabelProps: MultiValueGenericProps<SelectOption>) => (
            <multiSelectComponents.MultiValueLabel {...multiValueLabelProps} selectType={type} />
          ),
        }}
        instanceId="custom-multi-select" // Fixes react-select error (https://github.com/JedWatson/react-select/issues/2629)
        isDisabled={isDisabled}
        isSearchable={isSearchable}
        name={name}
        noOptionsMessage={(inputValue) => {
          const { inputValue: value } = inputValue;

          if (!value) {
            return null;
          }

          const isValidInput = allowInput ? allowInput(value) : true;
          const isValueEmpty = !value.trim().length;

          if (isValueEmpty) {
            return 'No results.';
          } else if (!isValidInput) {
            return `${value} is not a valid input.`;
          } else {
            return `${value} is already in the list.`;
          }
        }}
        options={options}
        placeholder={placeholder}
        ref={ref}
        isMulti
        styles={multiSelectStyles(type)}
        value={getSelectValue(values) as MultiValue<SelectOption>}
        onBlur={onBlur}
        onChange={(updatedOptions: any) => {
          const updatedValues = updatedOptions.map((option: SelectOption) => option.value as SelectOption['value']);
          onChange(updatedValues);
        }}
      />
    </FlexLayout>
  );
});

MultiSelect.displayName = 'MultiSelect';
