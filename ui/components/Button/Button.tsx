import { Property } from 'csstype';
import { forwardRef } from 'react';
import { Box, BoxProps } from 'theme-ui';

// import { primaryButtonHoverBackground, secondaryButtonHoverBackground } from '@/ui/assets/images';
import { Color, FlexLayout, Icon, iconSizesMap, IconType, Text } from '../../';
import { colors, TextVariant } from '../../theme';

interface ButtonVariantStyle {
  background: Property.Background | Color;
  color: Color;
  backgroundPosition?: Property.BackgroundPosition;
  backgroundRepeat?: Property.BackgroundRepeat;
  transition?: Property.Transition;
  ':hover'?: {
    backgroundPosition?: Property.BackgroundPosition;
    background?: Property.Background;
  };
  boxShadow?: string;
  opacity?: number;
}

type VariantTypes = 'primary' | 'secondary' | 'tertiary' | 'primaryDisabled' | 'secondaryDisabled' | 'tertiaryDisabled';

const buttonVariants: Record<VariantTypes, ButtonVariantStyle> = {
  primary: {
    background: colors.lilac600,
    color: colors['secondary-lemonade'] as Color,
    // backgroundPosition: '0 -100%',
    // transition: 'background-position .8s ease',
    // backgroundRepeat: 'no-repeat',
    ':hover': {
      // backgroundPosition: '100% 50%',
      background: colors.lilac500,
    },
  },
  primaryDisabled: {
    background: colors.lilac100,
    color: colors.lilac300 as Color,
  },
  secondary: {
    background: colors.midnight800,
    color: colors['secondary-lemonade'] as Color,
    // backgroundPosition: '0 -100% ',
    // transition: 'background-position .8s ease',
    // backgroundRepeat: 'no-repeat',
    ':hover': {
      background: colors.midnight700,
    },
  },
  secondaryDisabled: {
    background: colors.midnight50,
    color: colors.midnight100 as Color,
    opacity: 0.6,
  },
  tertiary: {
    background: colors['primary-dusk'],
    color: colors['secondary-lemonade'] as Color,
    ':hover': {
      background: colors['primary-macaron'],
    },
  },
  tertiaryDisabled: {
    background: colors.midnight100,
    color: colors.white50 as Color,
    opacity: 0.6,
  },
};

interface CommonProps extends BoxProps {
  label: string;
  size?: 'large' | 'medium' | 'small';
  variant?: 'primary' | 'secondary' | 'tertiary';
  isDisabled?: boolean;
  icon?: IconType;
  iconSize?: keyof typeof iconSizesMap;
  isSkewedOnLeft?: boolean;
  backgroundSize?: string;
  glow?: boolean;
  fullWidth?: boolean;
}

interface ButtonAsButton extends CommonProps {
  as?: 'button';
}

interface ButtonAsLink extends CommonProps {
  as?: 'a';
  href: string;
  target?: string;
}

export type ButtonProps = ButtonAsButton | ButtonAsLink;

export const Button = forwardRef<HTMLButtonElement, ButtonProps>((props, ref) => {
  const {
    variant = 'primary',
    isDisabled = false,
    icon,
    iconSize = 'm',
    sx,
    label,
    as = 'button',
    // eslint-disable-next-line @typescript-eslint/no-unused-vars
    isSkewedOnLeft = false,
    backgroundSize = '100%',
    glow = false,
    size = 'large',
    onClick,
    fullWidth,
    ...rest
  } = props;

  const variantStyles = isDisabled ? buttonVariants[`${variant}Disabled`] : buttonVariants[variant];

  const sizeText = { large: 'h6', medium: 'h7', small: 'h9' };
  const sizeHeight = { large: '64px', medium: '44px', small: '36px' };
  const padding = {
    large: { px: 6, py: 4 },
    medium: { px: 4, py: '10px' },
    small: { px: 4, py: '6px' },
  };

  return (
    <Box
      sx={{
        filter: '', // `drop-shadow(11px 11px 0px ${colors.buttonShadow})`,
      }}
    >
      <FlexLayout
        as={isDisabled ? 'div' : as}
        sx={{
          cursor: isDisabled ? 'not-allowed' : 'pointer',
          width: fullWidth ? '100%' : 'fit-content',
          // clipPath: isSkewedOnLeft
          //   ? 'polygon(0% 0%, 95% 0%, 100% 100%, 5% 100%)'
          //   : 'polygon(5% 0%, 100% 0%, 95% 100%, 0% 100%)',
          minHeight: sizeHeight[size],
          borderRadius: 0,
          border: 'none',
          textDecoration: 'none',
          ':hover': {
            boxShadow: glow && !isDisabled ? '0px 12px 25px 0px #532D83, 0px 3px 10px 0px #81559F99' : undefined,
          },
          ...padding[size],
          ...variantStyles,
          ...sx,
          backgroundSize: backgroundSize,
          boxShadow: glow && !isDisabled ? '0px 10px 20px 0px #532D8399, 0px 3px 10px 0px #81559F99' : undefined,
        }}
        justifyContent="center"
        alignItems="center"
        ref={ref}
        {...{ disabled: isDisabled, ...rest }}
        onClick={isDisabled ? undefined : onClick}
      >
        <FlexLayout alignItems="center" justifyContent="center" space={4} sx={{ textAlign: 'center' }}>
          <Text
            as="span"
            textVariant={sizeText[size] as TextVariant}
            sx={{
              display: 'contents',
              color: 'inherit',
            }}
            upperCase
          >
            {label}
          </Text>
          {icon && <Icon icon={icon} size={iconSize} color={variantStyles.color} />}
        </FlexLayout>
      </FlexLayout>
    </Box>
  );
});

Button.displayName = 'Button';
