import { Property } from 'csstype';
import React, { forwardRef, PropsWithChildren } from 'react';

import { Color, FlexLayout, Icon, IconType, Text } from '@/ui';

interface TagVariantStyle {
  background: Property.Background | Color;
}

type TagTypes = 'level1' | 'level2' | 'level3';

const tagVariants: Record<TagTypes, TagVariantStyle> = {
  level1: {
    background: 'bracketPlayerLevel1',
  },
  level2: {
    background: 'bracketPlayerLevel2',
  },
  level3: {
    background: 'bracketPlayerLevel3',
  },
};

export interface TagProps extends PropsWithChildren {
  variant: TagTypes;
  icon?: IconType;
  label?: string;
}

export const Tag = forwardRef<HTMLDivElement, TagProps>((props, ref) => {
  const { variant, icon, label, children, ...rest } = props;

  return (
    <FlexLayout space={2} alignItems="center">
      {label && (
        <Text variant="label-l-regular" color="white">
          {label}
        </Text>
      )}
      {icon && <Icon color="white" icon={icon} />}
      <FlexLayout
        px={2}
        py={1}
        justifyContent="center"
        alignItems="center"
        sx={{ borderRadius: '2px', ...tagVariants[variant] }}
        ref={ref}
        {...rest}
      >
        {children}
      </FlexLayout>
    </FlexLayout>
  );
});

Tag.displayName = 'Tag';
