/* eslint-disable @typescript-eslint/no-explicit-any */
import { colors } from '@/ui';
import { fonts } from '@/ui/theme/typography/fonts';

// eslint-disable-next-line @typescript-eslint/no-unused-vars
export default function styles(type: 'dark' | 'light' | 'transparent', ..._args: any) {
  return {
    control: (base: any) => ({
      ...base,
      border: `1px solid transparent`,
      borderRadius: '0px',
      boxShadow: 'none',
      padding: '16px',
      backgroundColor: type === 'transparent' ? 'rgba(255, 255, 255, 0.2)' : '#F8F8F9',
      width: '100%',
      ':focus-within': { border: `1px solid ${colors.midnight200}` },
      '&[disabled]': { background: colors.gray300 },
      ':hover': { border: `1px solid ${colors.midnight200}` },
    }),
    dropdownIndicator: (base: any) => ({
      ...base,
      padding: 0,
    }),
    indicatorsContainer: (base: any) => ({
      ...base,
      padding: 0,
    }),
    input: (base: any) => ({
      ...base,
      margin: 0,
      padding: 0,
      marginLeft: '2px',
      font: `normal 400 16px ${fonts['Transducer']}, sans-serif`,
    }),
    menu: (base: any) => ({
      ...base,
      borderRadius: '0px',
      border: `1px solid ${colors.midnight200}`,
      boxShadow: 'none',
      marginTop: 4,
      zIndex: 10,
      maxWidth: '100%',
      minHeight: '100%',
      backgroundColor: '#F8F8F9',
    }),
    menuList: (base: any) => ({
      ...base,
      padding: 0,
      backgroundColor: 'transparent',
      '::-webkit-scrollbar':
        type === 'dark'
          ? {
              width: '8px',
              height: '0px',
              background: colors.primary400,
            }
          : {},
      '::-webkit-scrollbar-track':
        type === 'dark'
          ? {
              background: colors.white10,
              borderLeft: `2px solid ${colors.primary400}`,
              borderRight: `4px solid ${colors.primary400}`,
            }
          : {},
      '::-webkit-scrollbar-thumb':
        type === 'dark'
          ? {
              background: colors.primary200,
              borderRight: `2px solid ${colors.primary400}`,
            }
          : {},
    }),
    noOptionsMessage: (base: any) => ({
      ...base,
      padding: 0,
    }),
    option: (base: any) => ({
      ...base,
      padding: '16px',
      border: 0,
      background: 'transparent',
      ':hover': {
        cursor: 'pointer',
        backgroundColor: colors.midnight50,
      },
    }),
    valueContainer: (base: any) => ({
      ...base,
      display: 'flex',
      padding: 0,
      maxHeight: '22px',
    }),
  };
}
