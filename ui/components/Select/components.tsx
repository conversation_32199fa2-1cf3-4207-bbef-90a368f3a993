import {
  components,
  ControlProps,
  DropdownIndicatorProps,
  GroupBase,
  GroupHeadingProps,
  GroupProps,
  MenuProps,
  OptionProps,
  PlaceholderProps,
  SingleValueProps,
} from 'react-select';

import { Box, FlexLayout, Icon, Text } from '@/ui';
import { SelectOption } from './Select';

export const Control = (props: ControlProps<SelectOption>) => {
  return (
    <components.Control {...props}>
      <FlexLayout justifyContent="space-between" alignItems="center" sx={{ width: '100%' }}>
        {props.children}
      </FlexLayout>
    </components.Control>
  );
};

export const DropdownIndicator = (
  props: DropdownIndicatorProps<SelectOption, boolean, GroupBase<SelectOption>> & { hasDropdownIndicator: boolean } & {
    selectType: 'light' | 'dark' | 'transparent';
  },
) => {
  const { hasDropdownIndicator, selectType } = props;
  const color = ['dark', 'transparent'].includes(selectType) ? 'white' : 'black';

  return hasDropdownIndicator ? (
    <components.DropdownIndicator {...props}>
      <Icon
        icon="chevronDown"
        color={color}
        sx={{ transform: props.selectProps.menuIsOpen ? 'rotate(180deg)' : null }}
      />
    </components.DropdownIndicator>
  ) : null;
};

export const IndicatorSeparator = () => {
  return null;
};

export const NoOptionsMessage = (props: any) => (
  <components.NoOptionsMessage {...props}>
    <FlexLayout justifyContent="center" alignItems="center" bg="gray200" p={4}>
      <Text color="primary500" variant="paragraph-s-regular">
        {props.children}
      </Text>
    </FlexLayout>
  </components.NoOptionsMessage>
);

export const Option = (props: OptionProps<SelectOption> & { selectType: 'light' | 'dark' | 'transparent' }) => {
  const { children } = props;

  return (
    <components.Option {...props}>
      <Text color="midnight900" textVariant="paragraph-s-regular">
        {children}
      </Text>
    </components.Option>
  );
};

export const Placeholder = (props: PlaceholderProps<SelectOption>) => {
  return (
    <components.Placeholder {...props}>
      <Text color="black20" textVariant="paragraph-s-regular">
        {props.children}
      </Text>
    </components.Placeholder>
  );
};

export const SingleValue = (
  props: SingleValueProps<SelectOption> & { selectType: 'light' | 'dark' | 'transparent' },
) => {
  const { children, selectType } = props;
  const color = ['dark', 'transparent'].includes(selectType) ? 'white' : 'primary500';
  return (
    <components.SingleValue {...props}>
      <Text color={color} textVariant="paragraph-s-regular">
        {children}
      </Text>
    </components.SingleValue>
  );
};

export const Menu = (props: MenuProps<SelectOption>) => {
  return (
    <components.Menu {...props}>
      <Box mt={1}>{props.children}</Box>
    </components.Menu>
  );
};

export const Group = (props: GroupProps<SelectOption>) => {
  return (
    <Box sx={{ '> div': { padding: 0 } }}>
      <components.Group {...props}>{props.children}</components.Group>
    </Box>
  );
};

export const GroupHeading = (props: GroupHeadingProps<SelectOption>) => {
  return (
    <components.GroupHeading {...props} style={{ marginBottom: 0, padding: 0 }}>
      <Box backgroundColor="gray300" px={4} py={2}>
        <Text color="primary500" textVariant="label-m-bold">
          {props.children}
        </Text>
      </Box>
    </components.GroupHeading>
  );
};
