import { FocusEvent, forwardRef } from 'react';
import ReactSelect, { DropdownIndicatorProps, GroupBase, OptionProps, SingleValueProps } from 'react-select';

import { FlexLayout, Text } from '@/ui';
import * as customComponents from './components';
import styles from './styles';

export type SelectOption = {
  label: string;
  value?: string | number;
  options?: SelectOption[];
};

export interface SelectProps {
  options: SelectOption[];
  value: SelectOption['value'];
  name: string;
  onChange: (value: SelectOption['value']) => void;
  onBlur?: (e: FocusEvent<HTMLInputElement>) => void;
  isDisabled?: boolean;
  isError?: boolean;
  label?: string;
  helperText?: string;
  errorText?: string;
  width?: string;
  placeholder?: string;
  hasGroupedOptions?: boolean;
  type?: 'light' | 'dark' | 'transparent';
  hasDropdownIndicator?: boolean;
  isSearchable?: boolean;
}

export const Select = forwardRef<any, SelectProps>((props, ref) => {
  const {
    options,
    value,
    name,
    onChange,
    onBlur,
    isDisabled = false,
    isError = false,
    label,
    helperText,
    errorText,
    width = '100%',
    placeholder,
    hasGroupedOptions = false,
    type = 'light',
    hasDropdownIndicator = false,
    isSearchable = true,
  } = props;

  function setValue() {
    if (hasGroupedOptions) {
      return (
        options
          .map((options) => options.options)
          .flat()
          .find((option) => option?.value === value) || null
      );
    } else {
      return options.find((option) => option?.value === value) || null;
    }
  }

  return (
    <FlexLayout flexDirection="column" space={2} sx={{ width }}>
      {label && (
        <Text color="midnight900" textVariant="paragraph-xs-medium">
          {label}
        </Text>
      )}
      <ReactSelect
        instanceId="custo-select"
        options={options}
        onBlur={onBlur}
        name={name}
        isDisabled={isDisabled}
        onChange={(v) => {
          if (v) onChange((v as SelectOption).value);
        }}
        components={{
          ...customComponents,
          SingleValue: (singleValueProps: SingleValueProps<SelectOption>) => (
            <customComponents.SingleValue {...singleValueProps} selectType={type} />
          ),
          Option: (optionProps: OptionProps<SelectOption>) => (
            <customComponents.Option {...optionProps} selectType={type} />
          ),
          DropdownIndicator: (
            dropdownIndicatorProps: DropdownIndicatorProps<SelectOption, boolean, GroupBase<SelectOption>>,
          ) => (
            <customComponents.DropdownIndicator
              {...dropdownIndicatorProps}
              hasDropdownIndicator={hasDropdownIndicator}
              selectType={type}
            />
          ),
        }}
        value={setValue()}
        styles={styles(type, { isError: !!(isError || errorText) })}
        ref={ref}
        menuShouldScrollIntoView
        placeholder={placeholder}
        isSearchable={isSearchable}
      />
      {helperText && (
        <Text color="black50" variant="paragraph-xs-medium">
          {helperText}
        </Text>
      )}
      {errorText && (
        <Text color="alertRed" variant="paragraph-xs-medium">
          {errorText}
        </Text>
      )}
    </FlexLayout>
  );
});
