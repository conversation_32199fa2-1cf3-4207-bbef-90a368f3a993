'use client';

import { forwardRef } from 'react';
import { Input, InputProps } from 'theme-ui';

import { colors, FlexLayout, useScreenType } from '@/ui';
import { fonts } from '@/ui/theme/typography/fonts';

interface OTPInputCellProps extends InputProps {
  isDisabled?: boolean;
  error?: boolean;
}

export const OTPInputCell = forwardRef<HTMLInputElement, OTPInputCellProps>((props, ref) => {
  const { isMobile } = useScreenType();
  const { isDisabled, error, ...rest } = props;

  return (
    <FlexLayout
      alignItems="center"
      justifyContent="flex-start"
      bg={error ? 'alertRed10' : rest.value === '' ? 'black5' : 'midnight50'}
      sx={{
        width: ['42px', '82px'],
        height: ['50px', '82px'],
        border: `1px solid transparent`,
        ':focus-within': { background: colors.black5, border: `1px solid ${colors.midnight200}` },
        '&[disabled]': { background: 'gray300' },
      }}
      {...{ disabled: isDisabled }}
    >
      <Input
        {...rest}
        disabled={isDisabled}
        sx={{
          border: 'none',
          width: '100%',
          height: '100%',
          outline: 'none',
          color: error ? colors.alertRed : colors.midnight700,
          fontFamily: fonts['TransducerExtended'],
          fontSize: isMobile ? '24px' : '32px',
          fontStyle: 'normal',
          fontWeight: 'heavy',
          lineHeight: isMobile ? '32px' : '40px',
          textAlign: 'center',
          MozAppearance: 'textfield',
          '::-webkit-outer-spin-button': {
            WebkitAppearance: 'none',
            margin: 0,
          },
          '::-webkit-inner-spin-button': {
            WebkitAppearance: 'none',
            margin: 0,
          },
        }}
        bg="transparent"
        ref={ref}
      />
    </FlexLayout>
  );
});
