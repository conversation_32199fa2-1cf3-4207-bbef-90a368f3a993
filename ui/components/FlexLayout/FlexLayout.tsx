import type { Property } from 'csstype';
import { forwardRef } from 'react';
import type { FlexProps } from 'theme-ui';

import { Box } from '@/ui';

export interface FlexLayoutProps extends FlexProps {
  flex?: Property.Flex | Property.Flex[];
  flexBasis?: Property.FlexBasis | Property.FlexBasis[];
  flexDirection?: Property.FlexDirection | Property.FlexDirection[];
  justifyContent?: Property.JustifyContent | Property.JustifyContent[];
  alignItems?: Property.AlignItems | Property.AlignItems[];
  flexGrow?: Property.FlexGrow | Property.FlexGrow[];
  flexShrink?: Property.FlexShrink | Property.FlexShrink[];
  flexWrap?: Property.FlexWrap | Property.FlexWrap[];
  space?: number | number[];
}

export const FlexLayout = forwardRef<any, FlexLayoutProps>((props, ref) => {
  const {
    children,
    sx,
    flexDirection,
    justifyContent,
    alignItems,
    flexGrow,
    flexShrink,
    flexWrap,
    space,
    flex,
    flexBasis,
    ...rest
  } = props;

  return (
    <Box
      as="div"
      ref={ref}
      sx={{
        display: 'flex',
        alignItems,
        flexBasis,
        flex,
        flexDirection,
        flexGrow,
        flexShrink,
        flexWrap,
        justifyContent,
        gap: space,
        ...sx,
      }}
      {...rest}
    >
      {children}
    </Box>
  );
});

FlexLayout.displayName = 'FlexLayout';
