import { useBreakpointIndex } from '@theme-ui/match-media';
import mapValues from 'lodash/mapValues';

import { useHasMounted } from './useHasMounted';

export function useBpIndex() {
  const index = useBreakpointIndex();
  const hasMounted = useHasMounted();

  if (!hasMounted) {
    return null;
  }

  return index;
}

const screenIndexMap = {
  isDesktop: 2,
  isTablet: 1,
  isMobile: 0,
};

export function useScreenType() {
  const bpIndex = useBpIndex();

  return mapValues(screenIndexMap, (screenIndex: number) => screenIndex === bpIndex);
}
