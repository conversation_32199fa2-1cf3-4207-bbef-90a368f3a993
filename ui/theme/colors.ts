export const palette = {
  primary50: '#DDE8FE',
  primary100: '#7EAAFC',
  primary150: '#0C52D6',
  primary200: '#063793',
  primary300: '#041E4D',
  primary400: '#031840',
  primary500: '#02112D',
  primary600: '#010C21',
  secondary50: '#EFDBFF',
  secondary100: '#DEB6FF',
  secondary200: '#CE92FF',
  secondary300: '#8B00FD',
  secondary400: '#460080',
  secondary500: '#310059',
  tertiary50: '#FCDBB4',
  tertiary100: '#FCC88B',
  tertiary200: '#F9B869',
  tertiary300: '#F5921D',
  tertiary400: '#975606',
  tertiary500: '#4C2B03',
  gray900: '#101010',
  gray800: '#2A2A2A',
  gray700: '#484848',
  gray600: '#606060',
  gray500: '#8A8A8A',
  gray400: '#A0A0A0',
  gray300: '#E4E4E4',
  gray200: '#ECECEC',
  gray100: '#F5F5F5',
  error200: '#DB1616',
  error100: '#FFF9F9',
  warning: '#FFA800',
  success: '#00C981',
  buttonShadow: '#010C21',
  buttonHover1: '#F5921D',
  buttonHover2: '#E0E0E0',
  linkColor1: '#5B94FF',
  linkColorHover1: '#063793',
  linkColor2: '#F5921D',
  linkColorHover2: '#FCC88B',
  bracketPlayerLevel1: '#3ED7A9',
  bracketPlayerLevel2: '#FD9D79',
  bracketPlayerLevel3: '#FCE519',
  midnight50: '#E8E5FF',
  midnight100: '#BBB3FF',
  midnight200: '#8D80FF',
  midnight300: '#604DFF',
  midnight400: '#321AFF',
  midnight500: '#1900E6',
  midnight600: '#1300B3',
  midnight700: '#0E0080',
  midnight800: '#08004D',
  midnight900: '#05002E',
  dreamBlue: '#4D6B87',
  paleYellow100: '#E4E8E0',
  paleYellow300: '#BFC7B3',
  paleYellow500: '#E5FFCA',
  paleYellow600: '#D2FFA4',
  purpleLavander500: '#AC6CDD',
  purpleLavander400: '#C597E7',
  brightBlue: '#1210A2',
  alertRed: '#F91414',
  alertRed10: '#F914141A',
  successGreen: '#2AC139',
  'primary-sunrise': '#FEBDB7',
  'primary-midnight': '#0C0E42',
  'primary-dusk': '#532D83',
  'primary-crepe': '#FFDDBA',
  'primary-macaron': '#81559F',
  'primary-clouds': '#F3EAF5',
  'primary-denim': '#2D3C67',
  'secondary-sunshine': '#FFB400',
  'secondary-lemonade': '#FFF2CC',
  'secondary-eggplant': '#3A106B',
  'secondary-lilac': '#DE42C5',
  sunrise600: '#FEBDB7',
  sunrise500: '#FFCFCB',
  sunrise300: '#D7BCB9',
  sunrise100: '#F4E4E2',
  lilac600: '#DE42C5',
  lilac500: '#E369CF',
  lilac300: '#B169A6',
  lilac100: '#F1A9E5',
};

export const alphas = {
  black: 'rgba(0, 0, 0, 1)',
  black70: 'rgba(0, 0, 0, 0.70)',
  black75: 'rgba(0, 0, 0, 0.75)',
  black50: 'rgba(0, 0, 0, 0.5)',
  black25: 'rgba(0, 0, 0, 0.25)',
  black20: 'rgba(0, 0, 0, 0.2)',
  black10: 'rgba(0, 0, 0, 0.1)',
  black5: 'rgba(0, 0, 0, 0.05)',
  white: 'rgba(255, 255, 255, 1)',
  white75: 'rgba(255, 255, 255, 0.75)',
  white70: 'rgba(255, 255, 255, 0.70)',
  white50: 'rgba(255, 255, 255, 0.5)',
  white25: 'rgba(255, 255, 255, 0.25)',
  white20: 'rgba(255, 255, 255, 0.20)',
  white15: 'rgba(255, 255, 255, 0.15)',
  white10: 'rgba(255, 255, 255, 0.1)',
  white5: 'rgba(255, 255, 255, 0.05)',
  midnight20: 'rgba(12, 14, 66, 0.2)',
  midnight30: 'rgba(12, 14, 66, 0.3)',
  midnight40: 'rgba(12, 14, 66, 0.4)',
  midnight60: 'rgba(12, 14, 66, 0.6)',
  midnight80: 'rgba(12, 14, 66, 0.8)',
  brightBlue30: 'rgba(18, 16, 162, 0.3)',
  midnightTransparent: 'rgba(12, 14, 66, 0.6)',
  lemonade75: 'rgba(255, 242, 204, 0.75)',
  lemonade50: 'rgba(255, 242, 204, 0.50)',
  lemonade25: 'rgba(255, 242, 204, 0.25)',
};

export const gradients = {
  'button-gradient': `linear-gradient(198deg, #FFE000 0%, #FA9800 100%)`,
  'body-gradient':
    'linear-gradient(180deg, #0A0132 2.78%, #210A63 6.64%, #460A83 27.82%, #070025 36.99%, #09022C 40.83%, #3A076F 45.78%, #100746 54.58%, #15074E 74.39%, #0D0430 100%)',
};
