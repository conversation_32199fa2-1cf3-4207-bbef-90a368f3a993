import { fonts } from './fonts';
import { fontStyles } from './fontStyles';
import { fontWeights } from './fontWeights';

const heading = {
  h1: {
    fontFamily: fonts['TransducerExtended'],
    fontStyle: fontStyles.normal,
    fontWeight: fontWeights.heavy,
    fontSize: '70px',
    lineHeight: '72px',
  },
  h2: {
    fontFamily: fonts['TransducerExtended'],
    fontStyle: fontStyles.normal,
    fontWeight: fontWeights.heavy,
    fontSize: '64px',
    lineHeight: '70px',
  },
  h3: {
    fontFamily: fonts['TransducerExtended'],
    fontStyle: fontStyles.normal,
    fontWeight: fontWeights.heavy,
    fontSize: '48px',
    lineHeight: '56px',
  },
  h4: {
    fontFamily: fonts['TransducerExtended'],
    fontStyle: fontStyles.normal,
    fontWeight: fontWeights.heavy,
    fontSize: '32px',
    lineHeight: '40px',
  },
  h5: {
    fontFamily: fonts['TransducerExtended'],
    fontStyle: fontStyles.normal,
    fontWeight: fontWeights.heavy,
    fontSize: '26px',
    lineHeight: '26px',
  },
  h6: {
    fontFamily: fonts['TransducerExtended'],
    fontStyle: fontStyles.normal,
    fontWeight: fontWeights.heavy,
    fontSize: '24px',
    lineHeight: '32px',
  },
  h7: {
    fontFamily: fonts['TransducerExtended'],
    fontStyle: fontStyles.normal,
    fontWeight: fontWeights.heavy,
    fontSize: '20px',
    lineHeight: '24px',
  },
  h8: {
    fontFamily: fonts['TransducerExtended'],
    fontStyle: fontStyles.normal,
    fontWeight: fontWeights.heavy,
    fontSize: '18px',
    lineHeight: '24px',
  },
  h9: {
    fontFamily: fonts['TransducerExtended'],
    fontStyle: fontStyles.normal,
    fontWeight: fontWeights.heavy,
    fontSize: '16px',
    lineHeight: '24px',
  },
  h10: {
    fontFamily: fonts['TransducerExtended'],
    fontStyle: fontStyles.normal,
    fontWeight: fontWeights.heavy,
    fontSize: '12px',
    lineHeight: '18px',
  },
  'mobile-button': {
    fontFamily: fonts['Transducer'],
    fontStyle: fontStyles.normal,
    fontWeight: fontWeights.heavy,
    fontSize: '16px',
    lineHeight: '18px',
  },
  'mobile-button-small': {
    fontFamily: fonts['Transducer'],
    fontStyle: fontStyles.normal,
    fontWeight: fontWeights.heavy,
    fontSize: '12px',
    lineHeight: '16px',
  },
};

const paragraph = {
  'paragraph-l-regular': {
    fontFamily: fonts['Noto-Sans'],
    fontStyle: fontStyles.normal,
    fontWeight: fontWeights.normal,
    fontSize: '24px',
    lineHeight: '40px',
  },
  'paragraph-m-regular': {
    fontFamily: fonts['Noto-Sans'],
    fontStyle: fontStyles.normal,
    fontWeight: fontWeights.normal,
    fontSize: '18px',
    lineHeight: '30px',
  },
  'paragraph-s-regular': {
    fontFamily: fonts['Noto-Sans'],
    fontStyle: fontStyles.normal,
    fontWeight: fontWeights.normal,
    fontSize: '16px',
    lineHeight: '24px',
  },
  'paragraph-xs-regular': {
    fontFamily: fonts['Noto-Sans'],
    fontStyle: fontStyles.normal,
    fontWeight: fontWeights.normal,
    fontSize: '12px',
    lineHeight: '20px',
  },
  'paragraph-xl-bold': {
    fontFamily: fonts['Transducer'],
    fontStyle: fontStyles.normal,
    fontWeight: fontWeights.semibold,
    fontSize: '40px',
    lineHeight: '48px',
  },
  'paragraph-l-bold': {
    fontFamily: fonts['Transducer'],
    fontStyle: fontStyles.normal,
    fontWeight: fontWeights.semibold,
    fontSize: '24px',
    lineHeight: '40px',
  },
  'paragraph-m-bold': {
    fontFamily: fonts['Noto-Sans'],
    fontStyle: fontStyles.normal,
    fontWeight: fontWeights.bold,
    fontSize: '18px',
    lineHeight: '30px',
  },
  'paragraph-s-bold': {
    fontFamily: fonts['Noto-Sans'],
    fontStyle: fontStyles.normal,
    fontWeight: fontWeights.bold,
    fontSize: '16px',
    lineHeight: '24px',
  },
  'paragraph-xs-bold': {
    fontFamily: fonts['Noto-Sans'],
    fontStyle: fontStyles.normal,
    fontWeight: fontWeights.bold,
    fontSize: '12px',
    lineHeight: '20px',
  },
  'paragraph-xl-medium': {
    fontFamily: fonts['Transducer'],
    fontStyle: fontStyles.normal,
    fontWeight: fontWeights.medium,
    fontSize: '40px',
    lineHeight: '48px',
  },
  'paragraph-l-medium': {
    fontFamily: fonts['Transducer'],
    fontStyle: fontStyles.normal,
    fontWeight: fontWeights.medium,
    fontSize: '22px',
    lineHeight: '32px',
  },
  'paragraph-m-medium': {
    fontFamily: fonts['Transducer'],
    fontStyle: fontStyles.normal,
    fontWeight: fontWeights.medium,
    fontSize: '18px',
    lineHeight: '30px',
  },
  'paragraph-s-medium': {
    fontFamily: fonts['Transducer'],
    fontStyle: fontStyles.normal,
    fontWeight: fontWeights.medium,
    fontSize: '16px',
    lineHeight: '22px',
  },
  'paragraph-xs-medium': {
    fontFamily: fonts['Transducer'],
    fontStyle: fontStyles.normal,
    fontWeight: fontWeights.medium,
    fontSize: '14px',
    lineHeight: '20px',
  },
};

const label = {
  'label-l-regular': {
    fontFamily: fonts['Noto-Sans'],
    fontStyle: fontStyles.normal,
    fontWeight: fontWeights.normal,
    fontSize: '14px',
    lineHeight: '24px',
  },
  'label-m-regular': {
    fontFamily: fonts['Noto-Sans'],
    fontStyle: fontStyles.normal,
    fontWeight: fontWeights.normal,
    fontSize: '12px',
    lineHeight: '18px',
  },
  'label-l-bold': {
    fontFamily: fonts['Noto-Sans'],
    fontStyle: fontStyles.normal,
    fontWeight: fontWeights.bold,
    fontSize: '14px',
    lineHeight: '24px',
  },
  'label-m-bold': {
    fontFamily: fonts['Noto-Sans'],
    fontStyle: fontStyles.normal,
    fontWeight: fontWeights.bold,
    fontSize: '12px',
    lineHeight: '18px',
  },
  'label-logo': {
    fontFamily: fonts['Transducer'],
    fontStyle: fontStyles.normal,
    fontWeight: fontWeights.semibold,
    fontSize: '18px',
    lineHeight: '27px',
  },
  'label-l-countdown': {
    fontFamily: fonts['Transducer'],
    fontStyle: fontStyles.normal,
    fontWeight: 900,
    fontSize: '80px',
    lineHeight: '90px',
  },
  'label-s-countdown': {
    fontFamily: fonts['Transducer'],
    fontStyle: fontStyles.normal,
    fontWeight: fontWeights.medium,
    fontSize: '12px',
    lineHeight: '24px',
  },
};

const counterBox = {
  'counter-box-label': {
    fontFamily: fonts['Transducer'],
    fontStyle: fontStyles.normal,
    fontWeight: fontWeights.semibold,
    fontSize: '24px',
    lineHeight: '40px',
  },
  'counter-box-label-mobile': {
    fontFamily: fonts['Transducer'],
    fontStyle: fontStyles.normal,
    fontWeight: fontWeights.semibold,
    fontSize: '16px',
    lineHeight: '24px',
  },
};

const dashboard = {
  'dashboard-heading': {
    fontFamily: fonts['Transducer'],
    fontStyle: fontStyles.normal,
    fontWeight: fontWeights.normal,
    fontSize: '20px',
    lineHeight: '32px',
  },
  'dashboard-heading-mobile': {
    fontFamily: fonts['Transducer'],
    fontStyle: fontStyles.normal,
    fontWeight: fontWeights.normal,
    fontSize: '16px',
    lineHeight: '24px',
  },
  'counter-box-label-mobile': {
    fontFamily: fonts['Transducer'],
    fontStyle: fontStyles.normal,
    fontWeight: fontWeights.semibold,
    fontSize: '16px',
    lineHeight: '24px',
  },
};

const counter = {
  desktop: {
    fontFamily: fonts['Transducer'],
    fontStyle: fontStyles.normal,
    fontWeight: fontWeights.heavy,
    fontSize: '70px',
    lineHeight: '72px',
  },
  mobile: {
    fontFamily: fonts['Transducer'],
    fontStyle: fontStyles.normal,
    fontWeight: fontWeights.heavy,
    fontSize: '36px',
    lineHeight: '50px',
  },
};

const brackets = {
  'brackets-round-nav-item': {
    fontFamily: fonts['Transducer'],
    fontStyle: fontStyles.normal,
    fontWeight: fontWeights.semibold,
    fontSize: '14px',
    lineHeight: '24px',
  },
};

export const variants = {
  ...heading,
  ...paragraph,
  ...label,
  ...counterBox,
  ...dashboard,
  ...brackets,
  ...counter,
};
