import { Theme } from 'theme-ui';

import { borders } from './borders';
import { breakpoints } from './breakpoints';
import { alphas, gradients, palette } from './colors';
import { radii } from './radii';
import { shadows } from './shadows';
import { sizes } from './sizes';
import space from './space';
import { typography, variants as text } from './typography';
import zIndices from './zIndices';

const colors = {
  ...palette,
  ...alphas,
  ...gradients,
};

export const theme: Theme = {
  borders,
  breakpoints,
  colors,
  radii,
  shadows,
  sizes,
  space,
  text,
  zIndices,
  ...typography,
};

export { colors, radii };

export type Color = keyof typeof palette | keyof typeof alphas;
export type TextVariant = keyof typeof text;
export type Border = keyof typeof borders;
export type Radius = keyof typeof radii;
export type Shadow = keyof typeof shadows;
export type Size = keyof typeof sizes;
