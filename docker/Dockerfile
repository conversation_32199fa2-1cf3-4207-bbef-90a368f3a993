FROM --platform=linux/arm64 public.ecr.aws/docker/library/node:20.15.1-alpine AS base

# Install dependencies only when needed
FROM base AS deps
RUN apk add --no-cache libc6-compat
WORKDIR /app

# Install dependencies based on the preferred package manager
COPY package.json package-lock.json* ./

RUN npm ci
RUN npm install -g --arch=arm64 --platform=linux --libc=glibc sharp

# Rebuild the source code only when needed
FROM base AS builder
WORKDIR /app

COPY . .
COPY --from=deps /app/node_modules ./node_modules

ARG square_access_token
ARG next_public_square_application_id
ARG next_public_square_location_id
ARG pay_pal_client_secret
ARG next_public_pay_pal_client_id
ARG strapi_api_url
ARG strapi_api_token
ARG forge_api_url
ARG next_public_forge_api_url
ARG forge_web_domain
ARG api_key
ARG strapi_revalidate_seconds
ARG rso_environment
ARG rso_client_id
ARG rso_client_secret
ARG rso_redirect_uri
ARG nextauth_url
ARG nextauth_secret
ARG next_public_default_locale
ARG next_public_root_domain
ARG database_url
ARG aws_region
ARG aws_access_key
ARG aws_secret_access_key
#ARG contentstack_api_key
#ARG contentstack_delivery_token
#ARG contentstack_environment

ENV SQUARE_ACCESS_TOKEN=$square_access_token
ENV NEXT_PUBLIC_SQUARE_APPLICATION_ID=$next_public_square_application_id
ENV NEXT_PUBLIC_SQUARE_LOCATION_ID=$next_public_square_location_id
ENV PAY_PAL_CLIENT_SECRET=$pay_pal_client_secret
ENV NEXT_PUBLIC_PAY_PAL_CLIENT_ID=$next_public_pay_pal_client_id
ENV STRAPI_API_URL=$strapi_api_url
ENV STRAPI_API_TOKEN=$strapi_api_token
ENV FORGE_API_URL=$forge_api_url
ENV NEXT_PUBLIC_FORGE_API_URL=$next_public_forge_api_url
ENV FORGE_WEB_DOMAIN=$forge_web_domain
ENV API_KEY=$api_key
ENV STRAPI_REVALIDATE_SECONDS=$strapi_revalidate_seconds
ENV RSO_ENVIRONMENT=$rso_environment
ENV RSO_CLIENT_ID=$rso_client_id
ENV RSO_CLIENT_SECRET=$rso_client_secret
ENV RSO_REDIRECT_URI=$rso_redirect_uri
ENV NEXTAUTH_URL=$nextauth_url
ENV NEXTAUTH_SECRET=$nextauth_secret
ENV NEXT_PUBLIC_DEFAULT_LOCALE=$next_public_default_locale
ENV NEXT_PUBLIC_ROOT_DOMAIN=$next_public_root_domain
ENV DATABASE_URL=$database_url
ENV AWS_REGION=$aws_region
ENV AWS_ACCESS_KEY=$aws_access_key
ENV AWS_SECRET_ACCESS_KEY=$aws_secret_access_key
#ENV CONTENTSTACK_API_KEY=$contentstack_api_key
#ENV CONTENTSTACK_DELIVERY_TOKEN=$contentstack_delivery_token
#ENV CONTENTSTACK_ENVIRONMENT=$contentstack_environment
ENV NODE_ENV production
ENV NEXT_TELEMETRY_DISABLED 1
ENV NEXT_SHARP_PATH=/usr/local/lib/node_modules/sharp

RUN npm run prisma:generate
RUN npm run build

# Production image, copy all the files and run next
FROM builder AS runner
WORKDIR /app

RUN addgroup --system --gid 1001 nodejs
RUN adduser --system --uid 1001 nextjs

COPY --from=deps --chown=nextjs:nodejs /usr/local/lib/node_modules/sharp /usr/local/lib/node_modules/sharp
COPY --from=builder /app/public ./public

RUN mkdir -p /app/.next/cache/fetch-cache && chown -R nextjs:nodejs /app/.next

COPY --from=builder --chown=nextjs:nodejs /app/.next/standalone ./
COPY --from=builder --chown=nextjs:nodejs /app/.next/static ./.next/static
COPY --from=builder --chown=nextjs:nodejs /app/.next/cache ./.next/cache

USER nextjs

EXPOSE 3000

ENV PORT 3000
ENV HOSTNAME "0.0.0.0"

CMD ["node", "server.js"]