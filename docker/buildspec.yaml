version: 0.2

phases:
  pre_build:
    commands:
      - echo Logging in Amazon ECR...
      - aws ecr get-login-password --region $AWS_DEFAULT_REGION | docker login --username AWS --password-stdin $AWS_ACCOUNT_ID.dkr.ecr.$AWS_DEFAULT_REGION.amazonaws.com
  build:
    commands:
      - echo Build started on `date`
      - echo Building the Docker image...
      - docker build -f docker/Dockerfile -t $IMAGE
        --build-arg square_access_token=$SQUARE_ACCESS_TOKEN
        --build-arg next_public_square_application_id=$NEXT_PUBLIC_SQUARE_APPLICATION_ID
        --build-arg next_public_square_location_id=$NEXT_PUBLIC_SQUARE_LOCATION_ID
        --build-arg pay_pal_client_secret=$PAY_PAL_CLIENT_SECRET
        --build-arg next_public_pay_pal_client_id=$NEXT_PUBLIC_PAY_PAL_CLIENT_ID
        --build-arg strapi_api_url=$STRAPI_API_URL
        --build-arg strapi_api_token=$STRAPI_API_TOKEN
        --build-arg forge_api_url=$FORGE_API_URL
        --build-arg next_public_forge_api_url=$NEXT_PUBLIC_FORGE_API_URL
        --build-arg forge_web_domain=$FORGE_WEB_DOMAIN
        --build-arg api_key=$API_KEY
        --build-arg strapi_revalidate_seconds=$STRAPI_REVALIDATE_SECONDS
        --build-arg rso_environment=$RSO_ENVIRONMENT
        --build-arg rso_client_id=$RSO_CLIENT_ID
        --build-arg rso_client_secret=$RSO_CLIENT_SECRET
        --build-arg rso_redirect_uri=$RSO_REDIRECT_URI
        --build-arg nextauth_url=$NEXTAUTH_URL
        --build-arg nextauth_secret=$NEXTAUTH_SECRET
        --build-arg next_public_default_locale=$NEXT_PUBLIC_DEFAULT_LOCALE
        --build-arg next_public_root_domain=$NEXT_PUBLIC_ROOT_DOMAIN
        --build-arg database_url=$DATABASE_URL
        --build-arg aws_region=$AWS_REGION
        --build-arg aws_access_key=$AWS_ACCESS_KEY
        --build-arg aws_secret_access_key=$AWS_SECRET_ACCESS_KEY
        --build-arg contentstack_api_key=$CONTENTSTACK_API_KEY
        --build-arg contentstack_delivery_token=$CONTENTSTACK_DELIVERY_TOKEN
        --build-arg contentstack_environment=$CONTENTSTACK_ENVIRONMENT .
      - COMMIT_HASH=$(echo $CODEBUILD_RESOLVED_SOURCE_VERSION | cut -c 1-7)
      - IMAGE_TAG=${COMMIT_HASH:=latest}
      - IMAGE_URI=$ECR_REPO_URI:$IMAGE_TAG
      - docker tag $IMAGE:latest $IMAGE_URI
  post_build:
    commands:
      - echo Build completed.
      - echo Pushing the Docker image...
      - echo $IMAGE_URI
      - docker push $IMAGE_URI
      - echo Writing image definitions file...
      - printf '[{"name":"%s", "imageUri":"%s"}]' $ECS_CONTAINER_NAME $IMAGE_URI > imagedefinitions.json
      - npx prisma migrate deploy
artifacts:
  files: imagedefinitions.json
