import { WelcomeBoxData } from '@/components/WelcomeBox/types';
import { Options } from './competitor-bundle';

export function getWelcomeBoxProps({ siteConfig }: Options): WelcomeBoxData {
  return {
    title: siteConfig.localeJSON.competitorBundleCompletedTitle,
    subtitle: siteConfig.localeJSON.competitorBundleCompletedSubtitle,
    body: siteConfig.localeJSON.competitorBundleCompletedBody,
    imageUrl: '/images/infoBlockAlt.png',
    strings: {
      completeProfile: siteConfig.localeJSON.completeProfile,
      shareOnSocialMedia: siteConfig.localeJSON.shareOnSocialMedia,
      dashboard: siteConfig.localeJSON.navDashboard,
    },
  };
}
