import { SiteConfig } from '@/types/strapi';

export type Options = {
  siteConfig: SiteConfig;
};

export function getPurchaseInfoProps({ siteConfig }: Options) {
  return {
    imageAlt: 'Competitor Bundle image',
    imageUrl: '/images/competitorBundle.png',
    logoAlt: 'TFT  logo',
    logoUrl: '/logo.png',
    logoMobileUrl: '/logo-mobile2.png',
    date: siteConfig.localeJSON.heroDate,
    location: siteConfig.localeJSON.heroLocation,
  };
}

export function getCompetitorBundleInfoProps({ siteConfig }: Options) {
  return {
    competitorBundleInfoTitle: siteConfig.localeJSON.competitorBundleInfoTitle,
    competitorBundleBodyFirstRow: siteConfig.localeJSON.competitorBundleBodyFirstRow,
    competitorBundleBodySecondRow: siteConfig.localeJSON.competitorBundleBodySecondRow,
    competitorBundleBodyThirdRow: siteConfig.localeJSON.competitorBundleBodyThirdRow,
    competitorBundleBodyFourthRow: siteConfig.localeJSON.competitorBundleBodyFourthRow,
    competitorBundleBodyFifthRow: siteConfig.localeJSON.competitorBundleBodyFifthRow,
    competitorBundleBodySixthRow: siteConfig.localeJSON.competitorBundleBodySixthRow,
  };
}

export function getStrings({ siteConfig }: Options) {
  return {
    paymentMethod: siteConfig.localeJSON.paymentMethod,
    cardNumber: siteConfig.localeJSON.cardNumber,
    yourCardNumber: siteConfig.localeJSON.yourCardNumber,
    expiration: siteConfig.localeJSON.expiration,
    CVV: siteConfig.localeJSON.CVV,
    CVVCode: siteConfig.localeJSON.CVVCode,
    or: siteConfig.localeJSON.or,
    priceBreakdown: siteConfig.localeJSON.priceBreakdown,
    tax: siteConfig.localeJSON.tax,
    total: siteConfig.localeJSON.total,
    completePurchase: siteConfig.localeJSON.completePurchase,
    competitorPassTicket: siteConfig.localeJSON.competitorPassTicket,
  };
}

export function getCompetitorBundleErrorProps({ siteConfig }: Options) {
  return {
    title: siteConfig.localeJSON.competitorBundleErrorTitle,
    body: siteConfig.localeJSON.competitorBundleErrorBody,
    imageUrl: '/images/molediverStanding.webp',
    strings: {
      refreshPage: siteConfig.localeJSON.refreshPage,
    },
  };
}

export function getCompetitorBundleTimeoutProps({ siteConfig }: Options) {
  return {
    title: siteConfig.localeJSON.competitorBundleTimeoutTitle,
    body: siteConfig.localeJSON.competitorBundleTimeoutBody,
    imageUrl: '/images/furyhornSitting.png',
    strings: {
      refreshPage: siteConfig.localeJSON.refreshPage,
    },
  };
}

export function getCompetitorBundleSoldOutProps({ siteConfig }: Options) {
  return {
    title: siteConfig.localeJSON.competitorBundleSoldOutTitle,
    body: siteConfig.localeJSON.competitorBundleSoldOutBody,
    imageUrl: '/images/molediverStanding.webp',
    strings: {
      refreshPage: siteConfig.localeJSON.refreshPage,
    },
  };
}
