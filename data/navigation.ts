// !NOTE: Temporary comment out some navigation links before event starts (also check middleware to remove these routes from blocked ones)

import { Locale } from '@/config/i18n';

export const headerData = {
  _content_type_uid: 'header',
  _version: 35,
  locale: 'en-us',
  uid: 'blt0ea30c15d2d6704f',
  ACL: {},
  _in_progress: false,
  created_at: '2023-06-28T15:15:46.647Z',
  created_by: 'blta2a20caeb64a0dbc',
  links: [
    {
      path: {
        title: 'Home',
        titleKey: 'navHome',
        href: '/',
      },
      _metadata: {
        uid: 'cs508f707867eaa9de',
      },
      is_offsite: false,
      sublinks: [],
    },
    // {
    //   path: {
    //     title: 'Leaderboard',
    //     titleKey: 'navLeaderboard',
    //     href: '/leaderboard',
    //   },
    //   _metadata: {
    //     uid: 'cs508f707867eaa9da',
    //   },
    //   is_offsite: false,
    //   sublinks: [],
    // },
    // {
    //   path: {
    //     title: 'Brackets',
    //     titleKey: 'navBrackets',
    //     href: '/brackets/finals',
    //   },
    //   _metadata: {
    //     uid: 'cs508f707867eaa9db',
    //   },
    //   is_offsite: false,
    //   sublinks: [],
    // },
    {
      path: {
        title: 'Rules',
        titleKey: 'navRules',
        href: '/rules',
      },
      _metadata: {
        uid: 'cscc21849af20cde3a',
      },
      is_offsite: false,
      sublinks: [],
    },
    // {
    //   path: {
    //     title: 'Schedule',
    //     titleKey: 'navSchedule',
    //     href: '',
    //   },
    //   _metadata: {
    //     uid: 'ab6y542op9920121q3',
    //   },
    //   is_offsite: false,
    //   sublinks: [
    //     {
    //       path: {
    //         title: 'Event Schedule',
    //         titleKey: 'navEventSchedule',
    //         href: '/event-schedule',
    //       },
    //       _metadata: {
    //         uid: 'ab6y542op9920121q3',
    //       },
    //       is_offsite: false,
    //     },
    //     {
    //       path: {
    //         title: 'Side Events',
    //         titleKey: 'navSideEvents',
    //         href: '/side-events',
    //       },
    //       _metadata: {
    //         uid: 'ab6y542op9920121q3',
    //       },
    //       is_offsite: false,
    //     },
    //   ],
    // },
  ],
  additionalLinks: [
    {
      path: {
        title: 'My Dashboard',
        titleKey: 'navDashboard',
        href: '/dashboard',
      },
      _metadata: {
        uid: 'blta2a20caeb64biela',
      },
      is_offsite: false,
      sublinks: [],
    },
    {
      path: {
        title: 'Support',
        titleKey: 'supportTitle',
        href: '/support',
      },
      _metadata: {
        uid: 'blta2a20caeb64d982ja',
      },
      is_offsite: false,
      sublinks: [],
    },
  ],
  logo: {
    _version: 2,
    is_dir: false,
    uid: 'blt2c1949fe5c022014',
    ACL: {},
    content_type: 'image/png',
    created_at: '2023-08-17T07:49:58.571Z',
    created_by: 'blta2a20caeb64a0dbc',
    filesize: '11394',
    filename: 'logo.png',
    parent_uid: null,
    tags: [],
    title: 'TFT_logo.png',
    updated_at: '2023-08-17T07:50:48.105Z',
    updated_by: 'blta2a20caeb64a0dbc',
    publish_details: {
      environment: 'blt4125c34d41886635',
      locale: 'en-us',
      time: '2023-08-17T07:50:53.447Z',
      user: 'blta2a20caeb64a0dbc',
    },
    url: '/logo.png',
  },
  logo_mobile: {
    _version: 2,
    is_dir: false,
    uid: 'blt475425a672d6ba05',
    ACL: {},
    content_type: 'image/png',
    created_at: '2023-08-14T13:03:51.978Z',
    created_by: 'blta2a20caeb64a0dbc',
    description: '',
    filesize: '24347',
    filename: 'logo-mobile.png',
    parent_uid: null,
    tags: [],
    title: 'TFT_logo_mobile.png',
    updated_at: '2023-09-13T12:32:06.498Z',
    updated_by: 'blt3c866fd1644e538b',
    publish_details: {
      environment: 'blt4125c34d41886635',
      locale: 'en-us',
      time: '2023-09-13T12:32:11.112Z',
      user: 'blt3c866fd1644e538b',
    },
    url: '/logo-mobile.png',
  },
  tags: [],
  title: 'Header',
  updated_at: '2023-12-18T18:29:14.448Z',
  updated_by: 'bltfb9d82da18de3b25',
  publish_details: {
    environment: 'blt4125c34d41886635',
    locale: 'en-us',
    time: '2023-12-18T18:29:17.506Z',
    user: 'bltfb9d82da18de3b25',
  },
};

export const footerData = {
  _content_type_uid: 'footer',
  _version: 29,
  locale: 'en-us',
  uid: 'blt12841b9d66687c09',
  ACL: {},
  _in_progress: false,
  created_at: '2023-07-17T07:55:28.094Z',
  created_by: 'blt3c866fd1644e538b',
  description: '',
  links: [
    {
      path: {
        title: 'Support',
        titleKey: 'supportTitle',
        href: '/support',
      },
      _metadata: {
        uid: 'cs6ad2528cc1c3970c',
      },
      is_offsite: false,
      sublinks: [],
      is_newtab: false,
    },
    {
      path: {
        title: 'Terms of Service',
        titleKey: 'bottomNavToS',
        href: (locale: Locale) => {
          switch (locale) {
            case 'en':
              return 'https://www.riotgames.com/en/terms-of-service';
            case 'fr':
              return 'https://www.riotgames.com/fr/terms-of-service-FR';
            default:
              return 'https://www.riotgames.com/en/terms-of-service';
          }
        },
      },
      _metadata: {
        uid: 'csedb28ee4ff2f5fb1',
      },
      is_offsite: false,
      sublinks: [],
      is_newtab: true,
    },
    {
      path: {
        title: 'Privacy Policy',
        titleKey: 'bottomNavPP',
        href: (locale: Locale) => {
          switch (locale) {
            case 'en':
              return 'https://www.riotgames.com/en/privacy-notice';
            case 'fr':
              return 'https://www.riotgames.com/fr/privacy-notice-FR';
            default:
              return 'https://www.riotgames.com/en/privacy-notice';
          }
        },
      },
      _metadata: {
        uid: 'csca08d3cfd8265041',
      },
      is_offsite: false,
      sublinks: [],
      is_newtab: true,
    },
    {
      path: {
        title: 'Cookie Policy',
        titleKey: 'bottomNavCP',
        href: (locale: Locale) => {
          switch (locale) {
            case 'en':
              return 'https://www.riotgames.com/en/cookie-policy';
            case 'fr':
              return 'https://www.riotgames.com/fr/cookie-policy';
            default:
              return 'https://www.riotgames.com/en/cookie-policy';
          }
        },
      },
      _metadata: {
        uid: 'csa966ab5dcd226beb',
      },
      is_offsite: false,
      sublinks: [],
      is_newtab: true,
    },
  ],
  logos: [
    {
      _version: 2,
      is_dir: false,
      uid: 'blt2c1949fe5c022014',
      ACL: {},
      content_type: 'image/png',
      created_at: '2023-08-17T07:49:58.571Z',
      created_by: 'blta2a20caeb64a0dbc',
      file_size: '11394',
      filename: 'logo.png',
      link: '/',
      parent_uid: null,
      tags: [],
      title: 'TFT_logo.png',
      updated_at: '2023-08-17T07:50:48.105Z',
      updated_by: 'blta2a20caeb64a0dbc',
      publish_details: {
        environment: 'blt4125c34d41886635',
        locale: 'en-us',
        time: '2023-08-17T07:50:53.447Z',
        user: 'blta2a20caeb64a0dbc',
      },
      url: '/logo.png',
      width: 216,
      height: 26,
    },
    {
      _version: 2,
      is_dir: false,
      uid: 'asd2c1949fe5c022014',
      ACL: {},
      content_type: 'image/png',
      created_at: '2023-08-17T07:49:58.571Z',
      created_by: 'blta2a20caeb64a0dbc',
      file_size: '24100',
      filename: 'esl-logo.png',
      link: 'https://eslfaceitgroup.com/',
      parent_uid: null,
      tags: [],
      title: 'TFT_logo.png',
      updated_at: '2023-08-17T07:50:48.105Z',
      updated_by: 'blta2a20caeb64a0dbc',
      publish_details: {
        environment: 'blt4125c34d41886635',
        locale: 'en-us',
        time: '2023-08-17T07:50:53.447Z',
        user: 'blta2a20caeb64a0dbc',
      },
      url: '/esl-logo.png',
      width: 48,
      height: 48,
    },
  ],
  logos_mobile: [
    {
      _version: 3,
      is_dir: false,
      uid: 'blt20788fd53779813d',
      ACL: {},
      content_type: 'image/png',
      created_at: '2023-08-17T08:18:36.720Z',
      created_by: 'blta2a20caeb64a0dbc',
      file_size: '20797',
      filename: 'logo-mobile2.png',
      parent_uid: null,
      tags: [],
      title: 'TFT_logo_footer_mobile.png',
      updated_at: '2023-08-17T08:19:54.186Z',
      updated_by: 'blta2a20caeb64a0dbc',
      publish_details: {
        environment: 'blt4125c34d41886635',
        locale: 'en-us',
        time: '2023-08-17T08:20:15.126Z',
        user: 'blta2a20caeb64a0dbc',
      },
      url: '/logo-mobile2.png',
      width: [214, 200, 0],
      height: [129, 120, 0],
    },
    {
      _version: 2,
      is_dir: false,
      uid: 'rty3e5800dw9c224198',
      ACL: {},
      content_type: 'image/png',
      created_at: '2023-08-17T07:49:58.571Z',
      created_by: 'blta2a20caeb64a0dbc',
      file_size: '24100',
      filename: 'esl-logo.png',
      parent_uid: null,
      tags: [],
      title: 'ESL_logo.png',
      updated_at: '2023-08-17T07:50:48.105Z',
      updated_by: 'blta2a20caeb64a0dbc',
      publish_details: {
        environment: 'blt4125c34d41886635',
        locale: 'en-us',
        time: '2023-08-17T07:50:53.447Z',
        user: 'blta2a20caeb64a0dbc',
      },
      url: '/esl-logo.png',
      width: [48, 64, 0],
      height: [48, 64, 0],
    },
  ],
  tags: [],
  title: 'Footer',
  updated_at: '2023-08-31T11:28:18.926Z',
  updated_by: 'bltc08948fdd6f6b659',
  publish_details: {
    environment: 'blt4125c34d41886635',
    locale: 'en-us',
    time: '2023-08-31T11:28:25.129Z',
    user: 'bltc08948fdd6f6b659',
  },
};
