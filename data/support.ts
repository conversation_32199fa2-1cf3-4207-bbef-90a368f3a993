import fallbackFAQItems from '@/data/fallbackFAQItems.json';
import { SiteConfig, SupportPage } from '@/types/strapi';

type Options = {
  siteConfig: SiteConfig;
  pageConfig: SupportPage | null;
};

export function getWaysToReachOutProps({ siteConfig }: Options) {
  return {
    title: siteConfig.localeJSON.supportUndertitle,
    items: [
      {
        title: siteConfig.localeJSON.mediaTitle,
        description: siteConfig.localeJSON.mediaDetails,
        email: siteConfig.localeJSON.mediaContact,
      },
      {
        title: siteConfig.localeJSON.competitionTitle,
        description: siteConfig.localeJSON.competitionDetails,
        email: siteConfig.localeJSON.competitionContact,
      },
      {
        title: siteConfig.localeJSON.eventTitle,
        description: siteConfig.localeJSON.eventDetails,
        email: siteConfig.localeJSON.eventContact,
      },
    ],
  };
}

export function getFAQProps({ siteConfig, pageConfig }: Options) {
  return {
    title: siteConfig.localeJSON['faqTitle'] ?? 'FAQ',
    backgroundImage: '/images/paris-letters-faq.png',
    items:
      Array.isArray(pageConfig?.faqItems) && (pageConfig?.faqItems?.length ?? 0) > 0
        ? pageConfig?.faqItems
        : fallbackFAQItems,
  };
}
