import { Locale } from '@/config/i18n';
import fallbackFAQItems from '@/data/fallbackFAQItems.json';
import { HomeConfig, SiteConfig } from '@/types/strapi';

type Options = {
  siteConfig: SiteConfig;
  pageConfig: HomeConfig | null;
  locale: Locale;
};

export function getHeroProps({ siteConfig, pageConfig }: Options) {
  return {
    imageAlt: 'Hero Logo',
    imageUrl: '/images/heroLogo.png',
    backgroundImage: '/images/paris-letters.png',
    location: siteConfig.localeJSON.heroLocation,
    date: siteConfig.localeJSON.heroDate,
    login: siteConfig.localeJSON.login,
    navDashboard: siteConfig.localeJSON.navDashboard,
    heroLoginLabel: siteConfig.localeJSON.heroLoginLabel,
    buyCompetitorBundle: siteConfig.localeJSON.buyCompetitorBundle,
    competitorBundleSoldOutTitle: siteConfig.localeJSON.competitorBundleSoldOutTitle,
    competitorBundleSoldOutBody: siteConfig.localeJSON.competitorBundleSoldOutBody,
    competitorBundlesTechnicalDifficultiesTitle: siteConfig.localeJSON.competitorBundlesTechnicalDifficultiesTitle,
    competitorBundlesTechnicalDifficultiesBody: siteConfig.localeJSON.competitorBundlesTechnicalDifficultiesBody,
    spectatorPassesHeroBody: siteConfig.localeJSON.spectatorPassesHeroBody,
    firstRow: siteConfig.localeJSON.heroSaleInfoFirstRow,
    secondRow: siteConfig.localeJSON.heroSaleInfoSecondRow,
    thirdRow: siteConfig.localeJSON.heroSaleInfoThirdRow,
    fourthRow: siteConfig.localeJSON.heroSaleInfoFourthRow,
    fifthRow: siteConfig.localeJSON.heroSaleInfoFifthRow,
    sixthRow: siteConfig.localeJSON.heroSaleInfoSixthRow,
    watchLive: siteConfig.localeJSON.watchLiveStream,
    ticketPurchasedDashboardDescription: siteConfig.localeJSON.ticketPurchasedDashboardDescription,
    heroTitles: {
      competitorBundlesGoOnSaleIn: siteConfig.localeJSON.competitorBundlesGoOnSaleIn,
      competitorBundlesAreOnSale: siteConfig.localeJSON.competitorBundlesAreOnSale,
      heroTitleTactitiansCrown: pageConfig?.heroTitleTactitiansCrown,
      heroTitleChallenger: pageConfig?.heroTitleChallenger,
      heroTitleMaster: pageConfig?.heroTitleMaster,
      countdownHeroTitle: pageConfig?.heroTitle,
      countdownHeroTitleTactitiansCrown: pageConfig?.countdownHeroTitleTactitiansCrown,
      countdownHeroTitleChallenger: pageConfig?.countdownHeroTitleChallenger,
      countdownHeroTitleMaster: pageConfig?.countdownHeroTitleMaster,
      spectatorPassesHeroTitle: siteConfig.localeJSON.spectatorPassesHeroTitle,
    },
  };
}

export type HomepageHeroProps = ReturnType<typeof getHeroProps>;

export function getInfoBlockProps({ siteConfig }: Options) {
  return {
    title: siteConfig.localeJSON.joinTheCompetitionTitle,
    description: siteConfig.localeJSON.joinTheCompetitionBody,
    topImageUrl: '/images/about-event-img.png',
    topImageAlt: 'Molediver',
    // bottomImageUrl: '/images/swirl.png',
    // bottomImageAlt: 'Portal',
    assetPosition: 'right',
    assetPositionMobile: 'bottom',
    button: [],
    text_below_buttons: '',
  };
}

export function getWaysToPlayProps({ siteConfig }: Options) {
  return {
    title: siteConfig.localeJSON.waysToPlayTitle,
    description: siteConfig.localeJSON.waysToPlayBody,
    roles: [
      {
        title: siteConfig.localeJSON.waysToPlayTabOne,
        waysToPlay: [
          {
            title: siteConfig.localeJSON.competitors1Title,
            description: siteConfig.localeJSON.competitors1Body,
            imageUrl: '/images/competitors1.png',
            imageAlt: 'Competitors 1',
          },
          {
            title: siteConfig.localeJSON.competitors2Title,
            description: siteConfig.localeJSON.competitors2Body,
            imageUrl: '/images/competitors2.png',
            imageAlt: 'Competitors 2',
          },
          {
            title: siteConfig.localeJSON.competitors3Title,
            description: siteConfig.localeJSON.competitors3Body,
            imageAlt: 'Competitors 3',
            imageUrl: '/images/competitors3.png',
          },
        ],
      },
      {
        title: siteConfig.localeJSON.waysToPlayTabTwo,
        waysToPlay: [
          {
            title: siteConfig.localeJSON.spectators1Title,
            description: siteConfig.localeJSON.spectators1Body,
            imageUrl: '/images/spectators1.png',
            imageAlt: 'Spectators 1',
          },
          {
            title: siteConfig.localeJSON.spectators2Title,
            description: siteConfig.localeJSON.spectators2Body,
            imageUrl: '/images/spectators2.png',
            imageAlt: 'Spectators 2',
          },
          {
            title: siteConfig.localeJSON.spectators3Title,
            description: siteConfig.localeJSON.spectators3Body,
            imageUrl: '/images/spectators3.png',
            imageAlt: 'Spectators 3',
          },
        ],
      },
    ],
  };
}

export function getTicketsInfoProps({ siteConfig }: Options) {
  return {
    imageAlt: 'Tickets Info',
    imageUrl: '/images/ticketsInfo2.png',
    backgroundImage: '/images/background-r.png',
    description: siteConfig.localeJSON.competitorBundleSubtitle,
    list: [
      siteConfig.localeJSON.competitorBundleBodyFirstRow,
      siteConfig.localeJSON.competitorBundleBodySecondRow,
      // siteConfig.localeJSON.competitorBundleBodyThirdRow,
      siteConfig.localeJSON.competitorBundleBodyFourthRow,
      siteConfig.localeJSON.competitorBundleBodyFifthRow,
      siteConfig.localeJSON.competitorBundleBodySixthRow,
    ],
    title: siteConfig.localeJSON.competitorBundleTitle,
  };
}

export function getBrillianceBundleInfoProps({ siteConfig }: Options) {
  return {
    imageAlt: 'Brilliance Bundle Info',
    imageUrl: '/images/nimbleFoot.png',
    badge: '/images/vipBundleBadge.png',
    backgroundImage: '/images/background-s.png',
    title: siteConfig.localeJSON.brillianceBundleTitle,
    description: siteConfig.localeJSON.brillianceBundleSubtitle,
    list: [
      siteConfig.localeJSON.brillianceBundleBodyFirstRow,
      siteConfig.localeJSON.brillianceBundleBodySecondRow,
      siteConfig.localeJSON.brillianceBundleBodyThirdRow,
      siteConfig.localeJSON.brillianceBundleBodyFourthRow,
      // siteConfig.localeJSON.brillianceBundleBodyFifthRow,
      siteConfig.localeJSON.brillianceBundleBodySixthRow,
      siteConfig.localeJSON.brillianceBundleBodySeventhRow,
      siteConfig.localeJSON.brillianceBundleBodyEighthRow,
    ],
  };
}

export function getCarouselProps({ locale }: Options) {
  return {
    autoplay: true,
    autoplayTimeMs: 7500,
    items: [
      {
        imageAlt: 'Carousel Image 1',
        imageUrl: `/images/${locale}/carousel1.jpg`,
      },
      {
        imageAlt: 'Carousel Image 2',
        imageUrl: `/images/${locale}/carousel2.jpg`,
      },
      {
        imageAlt: 'Carousel Image 3',
        imageUrl: `/images/${locale}/carousel3.jpg`,
      },
      {
        imageAlt: 'Carousel Image 4',
        imageUrl: `/images/${locale}/carousel4.jpg`,
      },
      {
        imageAlt: 'Carousel Image 5',
        imageUrl: `/images/${locale}/carousel5.jpg`,
      },
      {
        imageAlt: 'Carousel Image 6',
        imageUrl: `/images/${locale}/carousel6.jpg`,
      },
      {
        imageAlt: 'Carousel Image 7',

        imageUrl: `/images/${locale}/carousel7.jpg`,
      },
    ],
    itemsMobile: [
      {
        imageAlt: 'Carousel Image 1',
        imageUrl: `/images/${locale}/carousel1mob.jpg`,
      },
      {
        imageAlt: 'Carousel Image 2',
        imageUrl: `/images/${locale}/carousel2mob.jpg`,
      },
      {
        imageAlt: 'Carousel Image 3',
        imageUrl: `/images/${locale}/carousel3mob.jpg`,
      },
      {
        imageAlt: 'Carousel Image 4',
        imageUrl: `/images/${locale}/carousel4mob.jpg`,
      },
      {
        imageAlt: 'Carousel Image 5',
        imageUrl: `/images/${locale}/carousel5mob.jpg`,
      },
      {
        imageAlt: 'Carousel Image 6',
        imageUrl: `/images/${locale}/carousel6mob.jpg`,
      },
      {
        imageAlt: 'Carousel Image 7',
        imageUrl: `/images/${locale}/carousel7mob.jpg`,
      },
    ],
  };
}

export function getFAQProps({ siteConfig, pageConfig }: Options) {
  return {
    title: siteConfig.localeJSON['faqTitle'] ?? 'FAQ',
    backgroundImage: '/images/paris-letters-faq.png',
    items:
      Array.isArray(pageConfig?.faqItems) && (pageConfig?.faqItems?.length ?? 0) > 0
        ? pageConfig?.faqItems
        : fallbackFAQItems,
  };
}

export function getAltInfoBlockProps({ siteConfig, pageConfig }: Options) {
  const areSpectatorPassesOnSale = siteConfig.featureFlags.areSpectatorPassesOnSale;

  return {
    title: siteConfig.localeJSON.spectatorPassTitle,
    description: siteConfig.localeJSON.spectatorPassBody,
    imageUrl: '/images/infoBlockAlt.png',
    imageAlt: 'Info Block Image',
    assetPosition: 'right',
    assetPositionMobile: 'top',
    button: [
      {
        showButton: !!areSpectatorPassesOnSale,
        isOffsite: true,
        link: {
          href: pageConfig?.spectatorPassesSaleLink || '',
          title: siteConfig.localeJSON.spectatorPassCTA,
          label: siteConfig.localeJSON.spectatorPassCTA,
        },
      },
      {
        showButton: !!areSpectatorPassesOnSale,
        isOffsite: true,
        link: {
          href: pageConfig?.spectatorPassesSaleLinkCn || '',
          title: siteConfig.localeJSON.cnSpectatorPassCTA,
          label: siteConfig.localeJSON.cnSpectatorPassCTA,
        },
      },
    ],
    textBelowButtons: areSpectatorPassesOnSale ? siteConfig.localeJSON.spectatorPassCTAinfo : '',
  };
}
