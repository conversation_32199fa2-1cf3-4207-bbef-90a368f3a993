import { SiteConfig } from '@/types/strapi';

type Options = {
  siteConfig: SiteConfig;
};

export function getDashboardLocales({ siteConfig }: Options) {
  return {
    title: siteConfig.localeJSON.dashboardPageTitle,
    desc: siteConfig.localeJSON.dashboardPageDesc,
    supportMail: siteConfig.localeJSON.dashboardSupportMail,
    outOfCompetitionTitle: siteConfig.localeJSON.dashboardOutOfCompetitionTitle,
    outOfCompetitionDesc: siteConfig.localeJSON.dashboardOutOfCompetitionDesc,
    profileIncompleteTitle: siteConfig.localeJSON.dashboardProfileIncomplete,
    profileIncompleteDesc: siteConfig.localeJSON.dashboardProfileIncompleteDesc,
    profileIncompleteCTA: siteConfig.localeJSON.dashboardProfileIncompleteCTA,
    settingsCTA: siteConfig.localeJSON.dashboardPageSettingsCTA,
    scheduleCTA: siteConfig.localeJSON.dashboardOutOfCompetitionCTA,
    upcomingTab: siteConfig.localeJSON.dashboardPageTabUpcoming,
    previousTab: siteConfig.localeJSON.dashboardPageTabPrevious,
    matchesFooter: siteConfig.localeJSON.dashboardMatchFooter,
    leaderboardsTitle: siteConfig.localeJSON.dashboardLeaderboardsTitle,
    leaderboardsDesc: siteConfig.localeJSON.dashboardLeaderboardsDesc,
    leaderboardPts: siteConfig.localeJSON.dashboardLeaderboardsPoints,
    leaderboardRegRank: siteConfig.localeJSON.dashboardLeaderboardsRegRank,
    leaderboardOverallRank: siteConfig.localeJSON.dashboardLeaderboardsRank,
    leaderboardCTA: siteConfig.localeJSON.dashboardLeaderboardsCTA,
    realmLoginTitle: siteConfig.localeJSON.dashboardRealmLogin,
    realmLoginUsername: siteConfig.localeJSON.dashboardRealmUsername,
    realmLoginPassword: siteConfig.localeJSON.dashboardRealmPassword,
    checkInTitle: siteConfig.localeJSON.dashboardCheckIn,
    emptyStateDescTitle: siteConfig.localeJSON.dashboardPageEmptyDescTitle,
    emptyStateDesc: siteConfig.localeJSON.dashboardPageEmptyDesc,
    emptyStateRealmLogin: siteConfig.localeJSON.dashboardRealmLoginEmpty,
    emptyStateCheckIn: siteConfig.localeJSON.dashboardCheckInEmpty,
    matchEstimatesInfo: siteConfig.localeJSON.dashboardMatchDisc,
    matchDate: siteConfig.localeJSON.dashboardMatchDate,
    matchTime: siteConfig.localeJSON.dashboardMatchTime,
    matchStation: siteConfig.localeJSON.dashboardMatchStation,
    matchYou: siteConfig.localeJSON.you,
    matchVs: siteConfig.localeJSON.dashboardMatchVs,
  };
}
