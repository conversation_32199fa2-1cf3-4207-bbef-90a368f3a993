import { SiteConfig } from '@/types/strapi';

type Options = {
  siteConfig: SiteConfig;
};

export function getPageInfoProps({ siteConfig }: Options) {
  return {
    imageAlt: 'Event Schedule image',
    imageUrl: '/images/eventSchedule.webp',
    title: siteConfig.localeJSON.mainEventSchedulePageTitle,
    date: siteConfig.localeJSON.eventSchedulePageTime,
    location: siteConfig.localeJSON.heroLocation,
  };
}

export function getEmptyPageProps({ siteConfig }: Options) {
  return {
    imageAlt: 'Event Schedule image',
    imageUrl: '/images/event-schedule-asset.png',
    bodyText: siteConfig.localeJSON.eventSchedulePageEmptyBody,
    firstDay: siteConfig.localeJSON.eventSchedulePageEmptyFirstDay,
    secondDay: siteConfig.localeJSON.eventSchedulePageEmptySecondDay,
    thirdDay: siteConfig.localeJSON.eventSchedulePageEmptyThirdDay,
    firstColumnFirstRow: siteConfig.localeJSON.eventSchedulePageEmptyFirstColumnFirstRow,
    firstColumnSecondRow: siteConfig.localeJSON.eventSchedulePageEmptyFirstColumnSecondRow,
    firstColumnThirdRow: siteConfig.localeJSON.eventSchedulePageEmptyFirstColumnThirdRow,
    secondColumnFirstRow: siteConfig.localeJSON.eventSchedulePageEmptySecondColumnFirstRow,
    secondColumnSecondRow: siteConfig.localeJSON.eventSchedulePageEmptySecondColumnSecondRow,
    secondColumnThirdRow: siteConfig.localeJSON.eventSchedulePageEmptySecondColumnThirdRow,
    thirdColumnFirstRow: siteConfig.localeJSON.eventSchedulePageEmptyThirdColumnFirstRow,
    thirdColumnSecondRow: siteConfig.localeJSON.eventSchedulePageEmptyThirdColumnSecondRow,
    thirdColumnThirdRow: siteConfig.localeJSON.eventSchedulePageEmptyThirdColumnThirdRow,
  };
}
