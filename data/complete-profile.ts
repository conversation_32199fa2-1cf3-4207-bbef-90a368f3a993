import { SiteConfig } from '@/types/strapi';

type Options = {
  siteConfig: SiteConfig;
};

export function getPageInfoProps({ siteConfig }: Options) {
  return {
    imageAlt: 'Complete Profile image',
    imageUrl: '/images/competitorBundle.png',
    logoAlt: 'TFT  logo',
    logoUrl: '/logo.png',
    logoMobileUrl: '/logo-mobile2.png',
    date: siteConfig.localeJSON.eventSchedulePageTime,
    location: siteConfig.localeJSON.heroLocation,
  };
}

export function getQuestionnaireProps({ siteConfig }: Options) {
  return {
    completeProfile: siteConfig.localeJSON.completeProfile,
    skipForNow: siteConfig.localeJSON.skipForNow,
    questionnaire: siteConfig.localeJSON.questionnaire,
    typeYourAnswerHere: siteConfig.localeJSON.typeYourAnswerHere,
    chooseLanguage: siteConfig.localeJSON.chooseLanguage,
    questionnaireQuestions: [
      siteConfig.localeJSON.questionnaireFirstQuestion,
      siteConfig.localeJSON.questionnaireSecondQuestion,
      siteConfig.localeJSON.questionnaireThirdQuestion,
      siteConfig.localeJSON.questionnaireFourthQuestion,
      siteConfig.localeJSON.questionnaireFifthQuestion,
      siteConfig.localeJSON.questionnaireSixthQuestion,
      siteConfig.localeJSON.questionnaireSeventhQuestion,
      siteConfig.localeJSON.questionnaireEighthQuestion,
      siteConfig.localeJSON.questionnaireNinthQuestion,
      siteConfig.localeJSON.questionnaireTenthQuestion,
    ],
    questionnaireLanguages: [
      siteConfig.localeJSON.arabic,
      siteConfig.localeJSON.brazilianPortuguese,
      siteConfig.localeJSON.czech,
      siteConfig.localeJSON.english,
      siteConfig.localeJSON.french,
      siteConfig.localeJSON.german,
      siteConfig.localeJSON.greek,
      siteConfig.localeJSON.hungarian,
      siteConfig.localeJSON.indonesian,
      siteConfig.localeJSON.italian,
      siteConfig.localeJSON.japanese,
      siteConfig.localeJSON.korean,
      siteConfig.localeJSON.polish,
      siteConfig.localeJSON.romanian,
      siteConfig.localeJSON.russian,
      siteConfig.localeJSON.simplifiedChinese,
      siteConfig.localeJSON.spanishMexico,
      siteConfig.localeJSON.spanishEU,
      siteConfig.localeJSON.taiwanTraditionalChinese,
      siteConfig.localeJSON.thai,
      siteConfig.localeJSON.turkish,
      siteConfig.localeJSON.vietnamese,
    ],
  };
}

export type QuestionnaireProps = ReturnType<typeof getQuestionnaireProps> & {
  initialAnswers: any;
  asModalOptions?: {
    onSkip?: () => void;
    onClose?: () => void;
  };
};
