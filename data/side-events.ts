import { SideEvent, SiteConfig } from '@/types/strapi';

type Options = {
  siteConfig: SiteConfig;
  sideEventsCollection?: SideEvent[] | null;
};

export function getPageInfoProps({ siteConfig }: Options) {
  return {
    imageAlt: 'Side Events image',
    imageUrl: '/images/side-events-asset.png',
    title: siteConfig.localeJSON.sideEventSchedulePageTitle,
    date: siteConfig.localeJSON.eventSchedulePageTime,
    location: siteConfig.localeJSON.heroLocation,
  };
}

export function getEmptyPageProps({ siteConfig }: Options) {
  return {
    bodyText: siteConfig.localeJSON.sideEventSchedulePageBody,
  };
}

export function getPageEventsProps({ siteConfig, sideEventsCollection }: Options) {
  return {
    howTo: {
      title: siteConfig.localeJSON.sideEventHowToTitle,
      firstParagraph: siteConfig.localeJSON.sideEventHowToFirstParagraph,
      secondParagraph: siteConfig.localeJSON.sideEventHowToSecondParagraph,
      eventTypes: siteConfig.localeJSON.sideEventHowToEventTypes,
      eventTypesFirstRow: siteConfig.localeJSON.sideEventHowToFirstRow,
      eventTypesSecondRow: siteConfig.localeJSON.sideEventHowToSecondRow,
      lastParagraph: siteConfig.localeJSON.sideEventHowToLastParagraph,
    },
    sideEvents: {
      events: sideEventsCollection,
      firstTab: siteConfig.localeJSON.sideEventFirstTab,
      secondTab: siteConfig.localeJSON.sideEventSecondTab,
      thirdTab: siteConfig.localeJSON.sideEventThirdTab,
      seeMore: siteConfig.localeJSON.seeMore,
      startTime: siteConfig.localeJSON.sideEventStartTime,
      location: siteConfig.localeJSON.sideEventLocation,
      info: siteConfig.localeJSON.sideEventInfo,
      showMore: siteConfig.localeJSON.showMore,
    },
  };
}

export type SideEventsPageProps = ReturnType<typeof getPageEventsProps>;
