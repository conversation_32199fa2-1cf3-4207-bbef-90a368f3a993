{"extends": ["plugin:prettier/recommended", "eslint:recommended", "plugin:@typescript-eslint/recommended"], "plugins": ["simple-import-sort"], "rules": {"simple-import-sort/imports": ["error", {"groups": [["^next", "^@?\\w"], ["^@/?", "^\\."]]}], "simple-import-sort/exports": "error", "no-unused-vars": "off", "@typescript-eslint/no-unused-vars": ["error", {"argsIgnorePattern": "^_", "varsIgnorePattern": "^_", "caughtErrorsIgnorePattern": "^_"}], "@typescript-eslint/no-explicit-any": ["warn"], "no-case-declarations": "off", "@typescript-eslint/ban-types": "off"}, "globals": {"NodeJS": true}, "env": {"browser": true, "amd": true, "node": true}}