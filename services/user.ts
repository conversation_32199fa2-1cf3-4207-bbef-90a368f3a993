import prisma from './db';

export async function getQuestionnaire(id: string) {
  try {
    const user = await prisma.user.findUnique({
      where: { id },
      select: {
        extQuestionnaire: true,
      },
    });

    if (!user) {
      throw new Error('User not found');
    }

    if (!user.extQuestionnaire) {
      return {
        prefferpreferredLanguages: [],
        socialMediaHandles: '',
        gamerNameInfo: '',
        celebrationInfo: '',
        comingWithInfo: '',
        playstyleDescription: '',
        preparationInfo: '',
        otherGamesInfo: '',
        playerAdvice: '',
        playerFeeling: '',
      };
    }
    return user.extQuestionnaire;
  } catch (err) {
    console.error('getQuestionnaire error', err);

    return null;
  }
}
