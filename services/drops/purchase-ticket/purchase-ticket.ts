import { User } from 'next-auth';

import { currentDrop } from '@/config/drops';
import { Locale } from '@/config/i18n';
import { PaymentProcessor } from '@/enums/payments';
import { sendEmail } from '@/services/mail';
import { captureOrder as capturePayPalOrder } from '@/services/paypal';
import { createPayment } from '@/services/square';
import { DropConfig } from '@/types/drops';
import { formatCurrency } from '@/utils/currency';
import { dropConfigSchema, getCurrentPeriod, getUserRank, isTicketReservedByUser, validateAmount } from '@/utils/drops';
import { PayPalPayload, SquarePayload } from '@/utils/payment';
import prisma from '../../db';
import { getMailHtml, getMailSubject, getMailText } from './you-are-in-mail.utils';

// TODO: properly inject prisma

// The following transaction encountered a serialization anomaly (two concurrent transactions
// interfered and resulted in an inconsistent state). This anomaly led to 2 out of 192 faulty
// purchases in the Sep 2024 release.
// https://www.postgresql.org/docs/current/transaction-iso.html

// To avoid this in the future, one approach would be to raise the transaction isolation
// level to "serializable" and implement a retry mechanism as described in:
// https://www.prisma.io/docs/orm/prisma-client/queries/transactions#transaction-timing-issues

type PurchaseTicketOptions = {
  locale: Locale;
  dropName?: string;
};

export async function purchaseTicket(
  user: User,
  paymentData: SquarePayload | PayPalPayload,
  options: PurchaseTicketOptions,
) {
  const locale = options.locale ?? 'en';
  const dropName = options.dropName ?? currentDrop;

  try {
    const ticket = await prisma.$transaction(
      async (prisma) => {
        // Sanity check to ensure the user object is valid
        if (!user || !user.id) throw new Error('Invalid user.');

        // eslint-disable-next-line @typescript-eslint/no-unused-vars
        const drop = await prisma.drop.findFirstOrThrow({
          where: { name: dropName },
          include: { tickets: { where: { userId: user.id } } },
        });

        const dropConfig: DropConfig = await dropConfigSchema.validate(drop.config);

        const reservedTicket = drop.tickets.find((ticket) => isTicketReservedByUser(ticket, user));
        if (!reservedTicket) {
          throw new Error(`Failed to resolve a reserved ticket for user ${user.id}`);
        }

        // Sanity check to verify that the `userId` on the ticket matches `user.id`.
        if (reservedTicket && reservedTicket.userId !== user.id) {
          throw new Error(`Ticket userId (${reservedTicket.userId}) does not match user.id (${user.id})`);
        }

        if (reservedTicket.purchased) {
          throw new Error(`Ticket (${reservedTicket.id}) already purchased by (${reservedTicket.userId})`);
        }

        const currentPeriod = getCurrentPeriod(dropConfig.orderedPeriods);
        if (!currentPeriod) {
          throw new Error('Tickets are not on sale yet (failed to resolve current period)');
        }

        const rank = user.extPuuid
          ? await prisma.rank.findFirst({
              where: { puuid: { equals: user.extPuuid } },
            })
          : null;

        const userRank = getUserRank(user.extEmail, rank?.tier);
        if (!currentPeriod.allowed_ranks[userRank]) {
          throw new Error(
            `User with rank ${userRank} cannot reserve a ticket during ` +
              `the current period (start: ${currentPeriod.start})`,
          );
        }

        const reservedByUser = isTicketReservedByUser(reservedTicket, user);
        if (!reservedByUser) {
          throw new Error(`Ticket ${reservedTicket.id} is not reserved by user ${user.id}`);
        }

        const isAmountValid = validateAmount(paymentData.meta, currentPeriod);
        if (!isAmountValid) {
          throw new Error('User does not see the same amount that is resolved from currentPeriod');
        }

        // TODO(competitor-bundle): extract currency from drop config
        // TODO(competitor-bundle): refactor payment processing below
        // eslint-disable-next-line @typescript-eslint/no-explicit-any
        let processorResult: any;
        switch (paymentData.processor) {
          case PaymentProcessor.Square:
            processorResult = await createPayment({
              sourceId: (paymentData as SquarePayload).processorData.sourceId,
              idempotencyKey: (paymentData as SquarePayload).processorData.idempotencyKey,
              locationId: (paymentData as SquarePayload).processorData.locationId,
              amountMoney: {
                // Cent (amount 100 in the Money object indicates $1.00). Multipying by 100 here
                // to convert `currentPeriod.price` and `currentPeriod.tax` dollars to cents.
                amount: BigInt(Math.floor((currentPeriod.price + currentPeriod.tax) * 100)),
                currency: 'USD',
              },
            });
            break;
          case PaymentProcessor.PayPal:
            processorResult = await capturePayPalOrder((paymentData as PayPalPayload).processorData.orderId);
            break;
        }

        const purchasedTicket = await prisma.ticket.update({
          where: { id: reservedTicket.id },
          data: {
            purchased: true,
            userId: user.id,
            purchaseData: { processorResult },
          },
        });

        // TODO(competitor-bundle): verify tax info
        await sendEmail({
          to: user.extEmail as string,
          subject: getMailSubject({ locale }),
          html: getMailHtml(
            {
              total: formatCurrency(currentPeriod.price + currentPeriod.tax),
              bundlePrice: formatCurrency(currentPeriod.price),
              code: purchasedTicket.id.toString(),
              tax: formatCurrency(currentPeriod.tax),
              taxRate: `${((currentPeriod.tax / currentPeriod.price) * 100).toFixed(0)}%`,
            },
            { locale: locale },
          ),
          text: getMailText(purchasedTicket.id.toString(), { locale: locale }),
        });

        return purchasedTicket;
      },
      {
        timeout: 20000,
        maxWait: 20000,
      },
    );

    return ticket;
  } catch (err) {
    console.error('purchaseTicket error', err);

    throw err;
  }
}
