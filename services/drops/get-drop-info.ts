import { currentDrop } from '@/config/drops';
import type { DropConfig } from '@/types/drops';
import {
  dropConfigSchema,
  getCurrentPeriod,
  getTicketsSaleFlags,
  getTicketsSaleStartTimes,
  getTicketsSaleStats,
} from '@/utils/drops';
import prisma from '../db';

// TODO: properly inject prisma

export type DropInfo = Awaited<ReturnType<typeof getDropInfo>>;

export async function getDropInfo(dropName: string = currentDrop) {
  try {
    const drop = await prisma.drop.findFirstOrThrow({
      where: { name: dropName },
      include: { tickets: true },
    });

    const dropConfig: DropConfig = await dropConfigSchema.validate(drop.config);

    const ticketStats = getTicketsSaleStats(drop.tickets);
    const ticketFlags = getTicketsSaleFlags(ticketStats);
    const saleStartTimes = getTicketsSaleStartTimes(dropConfig);
    const currentPeriod = getCurrentPeriod(dropConfig.orderedPeriods);
    const saleStarted = !!currentPeriod;

    return {
      dropId: drop.id,
      dropName: drop.name,
      ticketStats,
      ticketFlags,
      saleStartTimes,
      saleStarted,
      currentPeriod,
    };
  } catch (err) {
    console.error('getDropInfo error', err);

    return null;
  }
}
