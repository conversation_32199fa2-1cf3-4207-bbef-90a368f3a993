import { User } from 'next-auth';
import { isBefore } from 'date-fns';

import { currentDrop } from '@/config/drops';
import type { DropConfig } from '@/types/drops';
import {
  dropConfigSchema,
  getTicketPurchaseState,
  getTicketsSaleStartTimes,
  getUserRank,
  isTicketPurchasedByUser,
  isTicketReservedByUser,
} from '@/utils/drops';
import prisma from '../db';

// TODO: properly inject prisma

export type UserContext = Awaited<ReturnType<typeof getUserContext>>;

export async function getUserContext(user: User, dropName: string = currentDrop) {
  try {
    // Sanity check to ensure the user object is valid
    if (!user || !user.id) throw new Error('Invalid user.');

    const [drop, rank] = await Promise.all([
      prisma.drop.findFirstOrThrow({
        where: { name: dropName },
        include: { tickets: { where: { userId: user.id } } },
      }),
      // WARNING: `extPuuid` can be undefined, and passing undefined
      // to the query below will cause a bug.
      user.extPuuid
        ? prisma.rank.findFirst({
            where: { puuid: { equals: user.extPuuid } },
          })
        : null,
    ]);

    const dropConfig: DropConfig = await dropConfigSchema.validate(drop.config);

    const ticket =
      drop.tickets.find((ticket) => isTicketReservedByUser(ticket, user) || isTicketPurchasedByUser(ticket, user)) ??
      null;

    // Sanity check to verify that the `userId` on the ticket matches `user.id`.
    if (ticket && ticket.userId !== user.id) {
      throw new Error(`Ticket userId (${ticket.userId}) does not match user.id (${user.id})`);
    }

    const state = getTicketPurchaseState(ticket);
    const userRank = getUserRank(user.extEmail, rank?.tier);
    const saleStartTimes = getTicketsSaleStartTimes(dropConfig);
    const saleStartTimeForRank = saleStartTimes[userRank];
    const saleStartedForRank = isBefore(saleStartTimeForRank, new Date());

    return {
      state,
      rank: userRank,
      saleStartTimeForRank,
      saleStartedForRank,
      ticket,
    };
  } catch (err) {
    console.error('getUserContext error', err);

    return null;
  }
}
