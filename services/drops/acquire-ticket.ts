import { User } from 'next-auth';
import { addSeconds } from 'date-fns';

import { currentDrop } from '@/config/drops';
import {
  DropConfig,
  dropConfigSchema,
  findAvailableTicket,
  getCurrentPeriod,
  getUserRank,
  isTicketPurchasedByUser,
  isTicketReservedByUser,
} from '@/utils/drops';
import prisma from '../db';

// TODO: properly inject prisma

// The following transaction encountered a serialization anomaly (two concurrent transactions
// interfered and resulted in an inconsistent state). This anomaly led to 2 out of 192 faulty
// purchases in the Sep 2024 release.
// https://www.postgresql.org/docs/current/transaction-iso.html

// To avoid this in the future, one approach would be to raise the transaction isolation
// level to "serializable" and implement a retry mechanism as described in:
// https://www.prisma.io/docs/orm/prisma-client/queries/transactions#transaction-timing-issues

// A better approach would be to use advisory locks and refactor the code below to resolve
// reserved and available tickets directly with a query, instead of fetching everything
// (to avoid locking the table).

export async function acquireTicket(user: User, dropName: string = currentDrop) {
  try {
    // Sanity check to ensure the user object is valid
    if (!user || !user.id) throw new Error('Invalid user.');

    const ticket = await prisma.$transaction(
      async (prisma) => {
        const drop = await prisma.drop.findFirstOrThrow({
          where: { name: dropName },
          include: { tickets: true },
        });

        const purchasedOrReservedTicket = drop.tickets.find(
          (ticket) => isTicketPurchasedByUser(ticket, user) || isTicketReservedByUser(ticket, user),
        );

        // Return the user's ticket if they already have one for this drop
        if (purchasedOrReservedTicket) {
          return purchasedOrReservedTicket;
        }

        const dropConfig: DropConfig = await dropConfigSchema.validate(drop.config);

        const currentPeriod = getCurrentPeriod(dropConfig.orderedPeriods);
        if (!currentPeriod) {
          throw new Error('Tickets are not on sale yet (failed to resolve current period)');
        }

        const rank = user.extPuuid
          ? await prisma.rank.findFirst({
              where: { puuid: { equals: user.extPuuid } },
            })
          : null;

        const userRank = getUserRank(user.extEmail, rank?.tier);
        if (!currentPeriod.allowed_ranks[userRank]) {
          throw new Error(`User with rank ${userRank} cannot reserve a ticket during the current period`);
        }

        // Find the first available ticket that is not reserved or purchased
        const availableTicket = findAvailableTicket(drop.tickets);
        if (!availableTicket) {
          throw new Error('No available tickets to reserve.');
        }

        const reservedAt = new Date();
        const reservedUntil = addSeconds(reservedAt, dropConfig.reservationSeconds);

        const reservedTicket = await prisma.ticket.update({
          where: { id: availableTicket.id },
          data: {
            reserved: true,
            userId: user.id,
            reservedAt,
            reservedUntil,
          },
        });
        return reservedTicket;
      },
      {
        timeout: 20000,
        maxWait: 20000,
      },
    );

    return ticket;
  } catch (err) {
    console.error('acquireTicket error', err);

    throw err;
  }
}
