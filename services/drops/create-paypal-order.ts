import { User } from 'next-auth';

import { currentDrop } from '@/config/drops';
import { DropConfig } from '@/types/drops';
import { dropConfigSchema, getCurrentPeriod, getUserRank, isTicketReservedByUser, validateAmount } from '@/utils/drops';
import { CreatePayPalOrder } from '@/utils/payment';
import prisma from '../db';
import { createOrder } from '../paypal';

// TODO: properly inject prisma

// TODO(competitor-bundle): extract shared code between create-paypal-order and purchase-ticket

export async function createPaypalOrder(user: User, payload: CreatePayPalOrder, dropName: string = currentDrop) {
  try {
    // Sanity check to ensure the user object is valid
    if (!user || !user.id) throw new Error('Invalid user.');

    // eslint-disable-next-line @typescript-eslint/no-unused-vars
    const [drop, rank] = await Promise.all([
      prisma.drop.findFirstOrThrow({
        where: { name: dropName },
        include: { tickets: { where: { userId: user.id } } },
      }),
      user.extPuuid
        ? await prisma.rank.findFirst({
            where: { puuid: { equals: user.extPuuid } },
          })
        : null,
    ]);

    const dropConfig: DropConfig = await dropConfigSchema.validate(drop.config);

    // // Each user can get a maximum of 1 ticket per drop
    const reservedTicket = drop.tickets.find((ticket) => isTicketReservedByUser(ticket, user));
    if (!reservedTicket) {
      throw new Error(`Failed to resolve a ticket for user ${user.id}`);
    }

    // Sanity check to verify that the `userId` on the ticket matches `user.id`.
    if (reservedTicket && reservedTicket.userId !== user.id) {
      throw new Error(`Ticket userId (${reservedTicket.userId}) does not match user.id (${user.id})`);
    }

    // Sanity check to verify that the ticket is not already purchased.
    if (reservedTicket.purchased) {
      throw new Error(`Ticket (${reservedTicket.id}) already purchased by (${reservedTicket.userId})`);
    }

    const currentPeriod = getCurrentPeriod(dropConfig.orderedPeriods);
    if (!currentPeriod) {
      throw new Error('Tickets are not on sale yet (failed to resolve current period)');
    }

    const userRank = getUserRank(user.extEmail, rank?.tier);
    if (!currentPeriod.allowed_ranks[userRank]) {
      throw new Error(
        `User with rank ${userRank} cannot reserve a ticket during ` +
          `the current period (start: ${currentPeriod.start})`,
      );
    }

    const reservedByUser = isTicketReservedByUser(reservedTicket, user);
    if (!reservedByUser) {
      throw new Error(`Ticket ${reservedTicket.id} is not reserved by user ${user.id}`);
    }

    const isAmountValid = validateAmount(payload.meta, currentPeriod);
    if (!isAmountValid) {
      throw new Error('User does not see the same amount that is resolved from currentPeriod');
    }

    return await createOrder(currentPeriod.price + currentPeriod.tax);
  } catch (err) {
    console.error('createOrder error', err);

    throw err;
  }
}
