import { Client, CreatePaymentRequest, Environment } from 'square';

// TODO(competitor-bundle): use a separate env var

const environment = process.env.RSO_ENVIRONMENT == 'QA' ? Environment.Sandbox : Environment.Production;

const squareClient = new Client({
  environment,
  accessToken: process.env.SQUARE_ACCESS_TOKEN,
  httpClientOptions: {
    timeout: 4500,
    retryConfig: {
      maxNumberOfRetries: 2,
      maximumRetryWaitTime: 9500,
    },
  },
});

export async function createPayment(data: CreatePaymentRequest) {
  return (await squareClient.paymentsApi.createPayment(data)).result;
}
