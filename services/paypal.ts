const baseUrl = process.env.RSO_ENVIRONMENT == 'QA' ? 'https://api-m.sandbox.paypal.com' : 'https://api-m.paypal.com';

const clientId = process.env.NEXT_PUBLIC_PAY_PAL_CLIENT_ID as string;
const clientSecret = process.env.PAY_PAL_CLIENT_SECRET as string;

export async function generateAccessToken() {
  const auth = btoa(`${clientId}:${clientSecret}`);

  const response = await fetch(`${baseUrl}/v1/oauth2/token`, {
    method: 'POST',
    body: 'grant_type=client_credentials',
    headers: {
      Authorization: `Basic ${auth}`,
    },
  });

  if (!response.ok) {
    throw new Error(`Error: ${response.status} ${response.statusText}`);
  }

  const data = await response.json();
  return data.access_token;
}

export async function createOrder(amount: number) {
  const accessToken = await generateAccessToken();

  const response = await fetch(`${baseUrl}/v2/checkout/orders`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      Authorization: `Bearer ${accessToken}`,
    },
    body: JSON.stringify({
      intent: 'CAPTURE',
      purchase_units: [
        {
          amount: {
            currency_code: 'USD',
            value: amount.toFixed(2),
          },
        },
      ],
    }),
  });

  if (!response.ok) {
    throw new Error('Failed to create paypal order');
  }

  return await response.json();
}

export async function captureOrder(orderId: string) {
  const accessToken = await generateAccessToken();

  const response = await fetch(`${baseUrl}/v2/checkout/orders/${orderId}/capture`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      Authorization: `Bearer ${accessToken}`,
    },
  });

  if (!response.ok) {
    throw new Error('Failed to capture paypal order');
  }

  return await response.json();
}
