{"private": true, "scripts": {"dev": "next dev", "dev:https": "next dev -p 443 --experimental-https", "build": "next build", "start": "next start", "typecheck": "tsc --incremental false", "lint": "eslint . --fix", "format": "prettier . --write", "prepare": "husky install", "prisma:generate": "npx prisma generate", "prisma:migrate:prod": "npx prisma migrate deploy", "prisma:studio:dev": "dotenv -e .env.local npx prisma studio", "prisma:migrate:dev": "dotenv -e .env.local npx prisma migrate dev", "data-migration:create-drop": "ts-node -r tsconfig-paths/register ./prisma/data-migrations/create-drop", "data-migration:create-tickets": "ts-node -r tsconfig-paths/register ./prisma/data-migrations/create-tickets", "data-migration:seed-ranks": "ts-node -r tsconfig-paths/register ./prisma/data-migrations/seed-user-ranks.ts", "data-migration:seed-riot-id": "ts-node -r tsconfig-paths/register ./prisma/data-migrations/seed-riot-id.ts", "data-migration:adjust-tickets": "ts-node -r tsconfig-paths/register ./prisma/data-migrations/adjust-tickets.ts"}, "dependencies": {"@auth/prisma-adapter": "^2.4.1", "@aws-sdk/client-ses": "^3.621.0", "@emotion/react": "11.11.1", "@hookform/resolvers": "3.1.1", "@paypal/react-paypal-js": "8.1.3", "@prisma/client": "^5.17.0", "@radix-ui/react-tooltip": "^1.1.2", "@square/web-sdk": "2.0.1", "@strapi/blocks-react-renderer": "^1.0.1", "@svgr/webpack": "8.0.1", "@tanstack/react-query": "^5.62.7", "@tanstack/react-table": "^8.10.3", "@theme-ui/match-media": "0.16.0", "@types/pluralize": "^0.0.33", "@uidotdev/usehooks": "^2.4.1", "axios": "1.4.0", "commander": "^12.1.0", "contentstack": "3.17.1", "cookie": "0.5.0", "cors": "^2.8.5", "date-fns": "^3.6.0", "date-fns-tz": "^3.2.0", "dompurify": "^3.1.6", "dotenv-cli": "^7.4.2", "html-react-parser": "4.0.0", "js-cookie": "3.0.5", "jsdom": "^25.0.0", "lodash": "^4.17.21", "mjml": "^4.15.3", "next": "^14.2.5", "next-auth": "^4.24.7", "next-i18n-router": "^5.5.1", "pluralize": "^8.0.0", "prisma": "^5.17.0", "query-string": "^8.1.0", "react": "^18.3.1", "react-dom": "^18.3.1", "react-hook-form": "7.45.2", "react-otp-input": "3.0.3", "react-qr-code": "2.0.12", "react-query": "3.39.3", "react-select": "5.7.4", "react-share": "^5.1.0", "react-slick": "0.29.0", "sanitize-html": "^2.13.1", "server-only": "^0.0.1", "sharp": "^0.33.4", "slick-carousel": "1.8.1", "sonner": "^1.5.0", "square": "^38.0.0", "theme-ui": "0.16.0", "uuid": "9.0.0", "yup": "1.2.0"}, "devDependencies": {"@types/cookie": "0.5.1", "@types/dompurify": "^3.0.5", "@types/js-cookie": "3.0.3", "@types/jsdom": "^21.1.7", "@types/lodash": "^4.14.195", "@types/node": "20.3.1", "@types/react": "18.2.22", "@types/react-dom": "18.2.7", "@types/react-slick": "0.23.10", "@types/sanitize-html": "^2.13.0", "@types/uuid": "9.0.2", "@typescript-eslint/eslint-plugin": "6.0.0", "@typescript-eslint/parser": "6.0.0", "csstype": "3.1.2", "encoding": "^0.1.13", "eslint": "8.43.0", "eslint-config-prettier": "8.8.0", "eslint-plugin-prettier": "4.2.1", "eslint-plugin-simple-import-sort": "10.0.0", "husky": "8.0.3", "lint-staged": "13.2.2", "mini-css-extract-plugin": "^2.9.0", "prettier": "2.8.8", "ts-node": "^10.9.2", "tsconfig-paths": "^4.2.0", "typescript": "5.1.3"}, "lint-staged": {"**/*.{ts,tsx,json,css}": ["npm run lint", "npm run format"]}}